{"ast": null, "code": "import isObject from './isObject.js';\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = function () {\n  function object() {}\n  return function (proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object();\n    object.prototype = undefined;\n    return result;\n  };\n}();\nexport default baseCreate;", "map": {"version": 3, "names": ["isObject", "objectCreate", "Object", "create", "baseCreate", "object", "proto", "prototype", "result", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_baseCreate.js"], "sourcesContent": ["import isObject from './isObject.js';\n\n/** Built-in value references. */\nvar objectCreate = Object.create;\n\n/**\n * The base implementation of `_.create` without support for assigning\n * properties to the created object.\n *\n * @private\n * @param {Object} proto The object to inherit from.\n * @returns {Object} Returns the new object.\n */\nvar baseCreate = (function() {\n  function object() {}\n  return function(proto) {\n    if (!isObject(proto)) {\n      return {};\n    }\n    if (objectCreate) {\n      return objectCreate(proto);\n    }\n    object.prototype = proto;\n    var result = new object;\n    object.prototype = undefined;\n    return result;\n  };\n}());\n\nexport default baseCreate;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,YAAY,GAAGC,MAAM,CAACC,MAAM;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,UAAU,GAAI,YAAW;EAC3B,SAASC,MAAMA,CAAA,EAAG,CAAC;EACnB,OAAO,UAASC,KAAK,EAAE;IACrB,IAAI,CAACN,QAAQ,CAACM,KAAK,CAAC,EAAE;MACpB,OAAO,CAAC,CAAC;IACX;IACA,IAAIL,YAAY,EAAE;MAChB,OAAOA,YAAY,CAACK,KAAK,CAAC;IAC5B;IACAD,MAAM,CAACE,SAAS,GAAGD,KAAK;IACxB,IAAIE,MAAM,GAAG,IAAIH,MAAM,CAAD,CAAC;IACvBA,MAAM,CAACE,SAAS,GAAGE,SAAS;IAC5B,OAAOD,MAAM;EACf,CAAC;AACH,CAAC,CAAC,CAAE;AAEJ,eAAeJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}