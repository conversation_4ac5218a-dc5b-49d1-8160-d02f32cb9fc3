{"ast": null, "code": "/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return func + '';\n    } catch (e) {}\n  }\n  return '';\n}\nexport default toSource;", "map": {"version": 3, "names": ["funcProto", "Function", "prototype", "funcToString", "toString", "toSource", "func", "call", "e"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_toSource.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar funcProto = Function.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/**\n * Converts `func` to its source code.\n *\n * @private\n * @param {Function} func The function to convert.\n * @returns {string} Returns the source code.\n */\nfunction toSource(func) {\n  if (func != null) {\n    try {\n      return funcToString.call(func);\n    } catch (e) {}\n    try {\n      return (func + '');\n    } catch (e) {}\n  }\n  return '';\n}\n\nexport default toSource;\n"], "mappings": "AAAA;AACA,IAAIA,SAAS,GAAGC,QAAQ,CAACC,SAAS;;AAElC;AACA,IAAIC,YAAY,GAAGH,SAAS,CAACI,QAAQ;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIA,IAAI,IAAI,IAAI,EAAE;IAChB,IAAI;MACF,OAAOH,YAAY,CAACI,IAAI,CAACD,IAAI,CAAC;IAChC,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;IACb,IAAI;MACF,OAAQF,IAAI,GAAG,EAAE;IACnB,CAAC,CAAC,OAAOE,CAAC,EAAE,CAAC;EACf;EACA,OAAO,EAAE;AACX;AAEA,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}