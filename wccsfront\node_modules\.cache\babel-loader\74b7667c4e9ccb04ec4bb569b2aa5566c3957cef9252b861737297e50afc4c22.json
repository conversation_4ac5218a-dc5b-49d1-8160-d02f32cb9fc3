{"ast": null, "code": "import baseSum from './_baseSum.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/**\n * The base implementation of `_.mean` and `_.meanBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the mean.\n */\nfunction baseMean(array, iteratee) {\n  var length = array == null ? 0 : array.length;\n  return length ? baseSum(array, iteratee) / length : NAN;\n}\nexport default baseMean;", "map": {"version": 3, "names": ["baseSum", "NAN", "baseMean", "array", "iteratee", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_baseMean.js"], "sourcesContent": ["import baseSum from './_baseSum.js';\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/**\n * The base implementation of `_.mean` and `_.meanBy` without support for\n * iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @returns {number} Returns the mean.\n */\nfunction baseMean(array, iteratee) {\n  var length = array == null ? 0 : array.length;\n  return length ? (baseSum(array, iteratee) / length) : NAN;\n}\n\nexport default baseMean;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;;AAEnC;AACA,IAAIC,GAAG,GAAG,CAAC,GAAG,CAAC;;AAEf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACjC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAC7C,OAAOA,MAAM,GAAIL,OAAO,CAACG,KAAK,EAAEC,QAAQ,CAAC,GAAGC,MAAM,GAAIJ,GAAG;AAC3D;AAEA,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}