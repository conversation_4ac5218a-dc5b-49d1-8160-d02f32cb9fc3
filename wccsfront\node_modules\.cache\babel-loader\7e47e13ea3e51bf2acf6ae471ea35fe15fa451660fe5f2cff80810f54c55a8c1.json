{"ast": null, "code": "import { createVNode, mergeProps, isVNode } from 'vue';\nimport { tryCall } from '../utils.mjs';\nimport Row$1 from '../components/row.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst RowRenderer = (props, {\n  slots\n}) => {\n  const {\n    columns,\n    columnsStyles,\n    depthMap,\n    expandColumnKey,\n    expandedRowKeys,\n    estimatedRowHeight,\n    hasFixedColumns,\n    rowData,\n    rowIndex,\n    style,\n    isScrolling,\n    rowProps,\n    rowClass,\n    rowKey,\n    rowEventHandlers,\n    ns,\n    onRowHovered,\n    onRowExpanded\n  } = props;\n  const rowKls = tryCall(rowClass, {\n    columns,\n    rowData,\n    rowIndex\n  }, \"\");\n  const additionalProps = tryCall(rowProps, {\n    columns,\n    rowData,\n    rowIndex\n  });\n  const _rowKey = rowData[rowKey];\n  const depth = depthMap[_rowKey] || 0;\n  const canExpand = Boolean(expandColumnKey);\n  const isFixedRow = rowIndex < 0;\n  const kls = [ns.e(\"row\"), rowKls, {\n    [ns.e(`row-depth-${depth}`)]: canExpand && rowIndex >= 0,\n    [ns.is(\"expanded\")]: canExpand && expandedRowKeys.includes(_rowKey),\n    [ns.is(\"fixed\")]: !depth && isFixedRow,\n    [ns.is(\"customized\")]: Boolean(slots.row)\n  }];\n  const onRowHover = hasFixedColumns ? onRowHovered : void 0;\n  const _rowProps = {\n    ...additionalProps,\n    columns,\n    columnsStyles,\n    class: kls,\n    depth,\n    expandColumnKey,\n    estimatedRowHeight: isFixedRow ? void 0 : estimatedRowHeight,\n    isScrolling,\n    rowIndex,\n    rowData,\n    rowKey: _rowKey,\n    rowEventHandlers,\n    style\n  };\n  const handlerMosueEnter = e => {\n    onRowHover == null ? void 0 : onRowHover({\n      hovered: true,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex\n    });\n  };\n  const handlerMouseLeave = e => {\n    onRowHover == null ? void 0 : onRowHover({\n      hovered: false,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex\n    });\n  };\n  return createVNode(Row$1, mergeProps(_rowProps, {\n    \"onRowExpand\": onRowExpanded,\n    \"onMouseenter\": handlerMosueEnter,\n    \"onMouseleave\": handlerMouseLeave,\n    \"rowkey\": _rowKey\n  }), _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar Row = RowRenderer;\nexport { Row as default };", "map": {"version": 3, "names": ["columns", "columnsStyles", "depthMap", "expandColumnKey", "expandedRowKeys", "estimatedRowHeight", "hasFixedColumns", "rowData", "rowIndex", "style", "isScrolling", "rowProps", "rowClass", "<PERSON><PERSON><PERSON>", "rowEventHandlers", "ns", "onRowHovered", "onRowExpanded", "props", "rowKls", "tryCall", "additionalProps", "_row<PERSON>ey", "depth", "canExpand", "Boolean", "isFixedRow", "kls", "e", "is", "includes", "slots", "row", "onRowHover", "_rowProps", "class", "handler<PERSON><PERSON><PERSON><PERSON><PERSON>", "hovered", "event", "handlerMouseLeave", "createVNode", "Row$1", "mergeProps", "_isSlot", "default", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/row.tsx"], "sourcesContent": ["import { Row } from '../components'\nimport { tryCall } from '../utils'\n\nimport type {\n  ComponentInternalInstance,\n  FunctionalComponent,\n  UnwrapNestedRefs,\n} from 'vue'\nimport type { UseNamespaceReturn } from '@element-plus/hooks'\nimport type { UseTableReturn } from '../use-table'\nimport type { TableV2Props } from '../table'\nimport type { TableGridRowSlotParams } from '../table-grid'\n\ntype RowRendererProps = TableGridRowSlotParams &\n  Pick<\n    TableV2Props,\n    | 'expandColumnKey'\n    | 'estimatedRowHeight'\n    | 'rowProps'\n    | 'rowClass'\n    | 'rowKey'\n    | 'rowEventHandlers'\n  > &\n  UnwrapNestedRefs<\n    Pick<\n      UseTableReturn,\n      | 'depthMap'\n      | 'expandedRowKeys'\n      | 'hasFixedColumns'\n      | 'onRowHovered'\n      | 'onRowExpanded'\n      | 'columnsStyles'\n    >\n  > & {\n    ns: UseNamespaceReturn\n    tableInstance: null | ComponentInternalInstance\n  }\n\nconst RowRenderer: FunctionalComponent<RowRendererProps> = (\n  props,\n  { slots }\n) => {\n  const {\n    columns,\n    columnsStyles,\n    depthMap,\n    expandColumnKey,\n    expandedRowKeys,\n    estimatedRowHeight,\n    hasFixedColumns,\n    rowData,\n    rowIndex,\n    style,\n    isScrolling,\n    rowProps,\n    rowClass,\n    rowKey,\n    rowEventHandlers,\n    ns,\n    onRowHovered,\n    onRowExpanded,\n  } = props\n\n  const rowKls = tryCall(rowClass, { columns, rowData, rowIndex }, '')\n  const additionalProps = tryCall(rowProps, {\n    columns,\n    rowData,\n    rowIndex,\n  })\n  const _rowKey = rowData[rowKey]\n  const depth = depthMap[_rowKey] || 0\n  const canExpand = Boolean(expandColumnKey)\n  const isFixedRow = rowIndex < 0\n  const kls = [\n    ns.e('row'),\n    rowKls,\n    {\n      [ns.e(`row-depth-${depth}`)]: canExpand && rowIndex >= 0,\n      [ns.is('expanded')]: canExpand && expandedRowKeys.includes(_rowKey),\n      [ns.is('fixed')]: !depth && isFixedRow,\n      [ns.is('customized')]: Boolean(slots.row),\n    },\n  ]\n\n  const onRowHover = hasFixedColumns ? onRowHovered : undefined\n\n  const _rowProps = {\n    ...additionalProps,\n    columns,\n    columnsStyles,\n    class: kls,\n    depth,\n    expandColumnKey,\n    estimatedRowHeight: isFixedRow ? undefined : estimatedRowHeight,\n    isScrolling,\n    rowIndex,\n    rowData,\n    rowKey: _rowKey,\n    rowEventHandlers,\n    style,\n  }\n\n  const handlerMosueEnter = (e: MouseEvent) => {\n    onRowHover?.({\n      hovered: true,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex,\n    })\n  }\n\n  const handlerMouseLeave = (e: MouseEvent) => {\n    onRowHover?.({\n      hovered: false,\n      rowKey: _rowKey,\n      event: e,\n      rowData,\n      rowIndex,\n    })\n  }\n\n  return (\n    <Row\n      {..._rowProps}\n      onRowExpand={onRowExpanded}\n      onMouseenter={handlerMosueEnter}\n      onMouseleave={handlerMouseLeave}\n      rowkey={_rowKey}\n    >\n      {slots}\n    </Row>\n  )\n}\n\nexport default RowRenderer\n"], "mappings": ";;;;;;;;AAsCA,MAAM;EAEF;IACCA,OAAA;IACGC,aAAA;IACJC,QADI;IAEJC,eAFI;IAGJC,eAHI;IAIJC,kBAJI;IAKJC,eALI;IAMJC,OANI;IAOJC,QAPI;IAQJC,KARI;IASJC,WATI;IAUJC,QAVI;IAWJC,QAXI;IAYJC,MAZI;IAaJC,gBAbI;IAcJC,EAdI;IAeJC,YAfI;IAgBJC;MAhBIC,KAAA;EAkBJ,MAAAC,MAAA,GAAAC,OAAA,CAAAR,QAAA;IAlBIZ,OAAN;IAqBAO,OAAY;IAAuBC;KAAF;EAAoB,MAAAa,eAAA,GAAAD,OAAA,CAAAT,QAAA;IAA/BX,OAAtB;IACAO,OAAqB;IACnBC;IACA;EACA,MAAAc,OAAA,GAAAf,OAAA,CAAAM,MAAA;EAHwC,MAA1CU,KAAA,GAAArB,QAAA,CAAAoB,OAAA;EAKA,MAAME,SAAO,GAAUC,OAAA,CAAAtB,eAAvB;EACA,MAAMuB,UAAQ,GAAAlB,QAAQ,IAAR;EACd,MAAMmB,GAAS,IAAAZ,EAAA,CAAAa,CAAA,CAAG,KAAO,GAACT,MAAA;IAC1B,CAAAJ,EAAA,CAAMa,CAAU,cAAWL,KAAA,MAA3BC,SAAA,IAAAhB,QAAA;IACM,CAAAO,EAAA,CAAAc,EAAA,CAAG,UACP,IAAAL,SADU,IAGVpB,eAAA,CAAA0B,QAAA,CAAAR,OAAA;IACE,CAACP,EAAE,CAACc,EAAG,CAAY,YAAAN,KAAM,IAAKG,UAAA;IAC9B,CAACX,EAAE,CAACc,EAAH,CAAM,YAAP,IAA8BJ,OAAA,CAAAM,KAAmB,CAAAC,GAAA;IACjD;QACIC,UAAG,GAAA3B,eAA6B,GAAAU,YAAN;EAJhC,MAHFkB,SAAA;IAWA,GAAAb,eAAmB;IAEnBrB,OAAe;IAEbC,aAFgB;IAGhBkC,KAHgB,EAAAR,GAAA;IAIhBJ,KAAK;IACLpB,eALgB;IAMhBE,kBANgB,EAAAqB,UAAA,YAAArB,kBAAA;IAOhBK,WAAA;IACAF,QARgB;IAShBD,OATgB;IAUhBM,MAVgB,EAAAS,OAAA;IAWhBR,gBAXgB;IAYhBL;EACA;EAbgB,MAAlB2B,iBAAA,GAAAR,CAAA;;MAgBMS,OAAA;MACJxB,MAAA,EAAUS,OAAG;MACXgB,KAAA,EAAOV,CAAA;MACPrB,OAAA;MACAC;MACA;EACA;EALW,MAAb+B,iBAAA,GAAAX,CAAA;IADFK,UAAA,oBAAAA,UAAA;;MAUMpB,MAAA,EAAAS,OAAA;MACJgB,KAAA,EAAAV,CAAU;MACRrB,OAAO;MACPC;IACA;;EAEA,OAAAgC,WAAA,CAAAC,KAAA,EAAAC,UAAA,CAAAR,SAAA;IALW,aAAb,EAAAjB,aAAA;IADF,gBAAAmB,iBAAA;;IAUA,UAAAd;EAAA,IAAAqB,OAAA,CAAAZ,KAGiB,CAHjB,GAAAA,KAAA;IAAAa,OAAA,EAAAA,CAAA,MAIkBb,KAJlB;EAAA;;UAQKc,WARL", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}