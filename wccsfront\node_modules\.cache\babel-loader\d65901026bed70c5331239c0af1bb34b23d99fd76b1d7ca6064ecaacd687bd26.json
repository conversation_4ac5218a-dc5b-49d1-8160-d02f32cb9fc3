{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/es.set.difference.v2.js\";\nimport \"core-js/modules/es.set.intersection.v2.js\";\nimport \"core-js/modules/es.set.is-disjoint-from.v2.js\";\nimport \"core-js/modules/es.set.is-subset-of.v2.js\";\nimport \"core-js/modules/es.set.is-superset-of.v2.js\";\nimport \"core-js/modules/es.set.symmetric-difference.v2.js\";\nimport \"core-js/modules/es.set.union.v2.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { Refresh, RefreshLeft, Loading } from '@element-plus/icons-vue';\nimport { ref, inject, watch, nextTick, onUnmounted, computed } from 'vue';\n// , Clock, Location, User, CollectionTag, Microphone\nimport { Search, Filter, Document, Picture, ChatDotRound } from '@element-plus/icons-vue';\nimport ChatRecordPopUp from './ChatRecordPopUp.vue';\nimport axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例\nimport { ElMessage } from 'element-plus';\n\n//文件在线预览方案\nimport VueOfficeDocx from '@vue-office/docx';\nimport VueOfficeExcel from '@vue-office/excel';\nimport VueOfficePdf from '@vue-office/pdf';\nimport BenzAMRRecorder from 'benz-amr-recorder';\n\n// 搜索关键词\nconst loadMoreThreshold = 100;\n// 记录上一次的滚动位置\nconst minScale = 0.1; // 降低最小缩放比例\nconst maxScale = 5; // 提高最大缩放比例\nconst scaleStep = 0.1;\n\n// 处理图片加载完成事件\n\nexport default {\n  __name: 'DetailChatBoard',\n  setup(__props, {\n    expose: __expose\n  }) {\n    __expose();\n    const searchName = ref('');\n\n    //----------------------------------------- chat-board显示以及会话详情数据获取 ----------------------------------------\n\n    // 获取会话详情接口的参数信息,调用接口获取会话详情数据\n    const selectedChat = inject('selectedChat', ref(null));\n    const chatDetailMessages = ref([]); //存储详情会话数组\n\n    const chatDetailMessagesTotal = ref(null); //记录获取到的详情会话总数\n\n    const fetchedPages = ref(new Set()); //记录已经获取过的页码\n\n    const mediaLoadingCount = ref(0); // 添加媒体加载计数器\n    const totalMediaCount = ref(0); // 添加媒体总数计数器\n\n    // 调用获取会话详情接口\n    const getDetailChatMessages = (from, to, type, searchName, page, limit) => {\n      console.log(\"调用获取会话详情接口\");\n      if (fetchedPages.value.has(page)) {\n        return; //如果该页数据已经获取过，直接返回\n      }\n      const jwt_token = localStorage.getItem('access_token');\n      // 构建请求参数\n      const params = {\n        from: from,\n        to: to,\n        type: type || '',\n        // 确保type不为undefined\n        searchName: searchName,\n        page: page,\n        limit: limit\n      };\n      // 只有当searchName有值且不为空字符串时才添加到参数中\n      // if (searchName && searchName.trim() !== '') {\n      //     params.searchName = searchName.trim()\n      // }\n      axiosInstance.post('/api/chatmessage/detail', params, {\n        headers: {\n          Authorization: 'Bearer ' + jwt_token\n        }\n      }).then(res => {\n        const newMessages = res.data.data.data;\n\n        // 重置媒体计数器\n        mediaLoadingCount.value = 0;\n        totalMediaCount.value = 0;\n\n        // 计算需要加载的媒体总数\n        newMessages.forEach(message => {\n          if (message.msgType === 'image' || message.msgType === 'emotion' || message.msgType === 'voice' || message.msgType === 'video' || message.msgType === 'file') {\n            totalMediaCount.value++;\n          }\n        });\n\n        // 检查chatDetailMessages.value是否为空数组\n        if (chatDetailMessages.value.length === 0) {\n          chatDetailMessages.value = newMessages.sort((a, b) => {\n            return a.msgTime - b.msgTime;\n          });\n          // 只有在没有媒体需要加载时才立即滚动\n          if (totalMediaCount.value === 0) {\n            scrollToBottom();\n          }\n        } else {\n          chatDetailMessages.value = [...chatDetailMessages.value, ...newMessages].sort((a, b) => {\n            return a.msgTime - b.msgTime;\n          });\n        }\n        chatDetailMessagesTotal.value = newMessages.length;\n\n        //记录已经获取过的页码\n        fetchedPages.value.add(page);\n      }).catch(error => {\n        console.log(error);\n        ElMessage.error('获取会话详情失败，请检查网络或联系管理员');\n      }).finally();\n    };\n\n    // ------------------------------------------------------------------------------------------- Utils\n    // ----------------------------------------------------- 显示相关\n    //控制chat-board的显示\n    const chatBoardVisible = ref(false);\n    const old_selectedChat = ref(null);\n    watch(selectedChat, newValue => {\n      console.log('DetailChatBoard检测到selectedChat存在变化', newValue);\n      if (newValue) {\n        currentPage.value = 1;\n        chatBoardVisible.value = true;\n        chatDetailMessages.value = []; //清空消息数组\n        fetchedPages.value = new Set(); //清空已获取页码的记录\n        searchName.value = '';\n        hasMore.value = true;\n        old_selectedChat.value = newValue;\n        getDetailChatMessages(newValue.from, newValue.to, newValue.type, searchName.value, newValue.page, newValue.limit);\n      } else {\n        chatBoardVisible.value = false;\n        // console.log('selectedChat为空');\n      }\n    });\n\n    // 监听搜索关键词变化\n    watch(searchName, newValue => {\n      if (selectedChat.value && newValue != \"\" && newValue.length != 0 && newValue != null || old_selectedChat.value == selectedChat.value) {\n        currentPage.value = 1;\n        chatDetailMessages.value = []; //清空消息数组\n        fetchedPages.value = new Set(); //清空已获取页码的记录\n        hasMore.value = true;\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, newValue, selectedChat.value.page, selectedChat.value.limit);\n      }\n    });\n\n    // 控制使用sent还是receive样式\n    const sender_or_reciever = chatMessage => {\n      //当为群聊时，selectedChat.value.from=群聊id，selectedChat.value.to=''\n      if (chatMessage.fromUser == selectedChat.value.from) {\n        return 'sent';\n      } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return 'received';\n      }\n      return 'received'; //群聊默认\n    };\n\n    //控制使用员工头像还是用户头像\n    const avatar_from_or_to = chatMessage => {\n      if (chatMessage.fromUser == selectedChat.value.from) {\n        return selectedChat.value.fromAvatar;\n      } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return selectedChat.value.toAvatar;\n      }\n      return require('@/assets/人员头像.png'); //目前客户会话功能暂时无法拉取客户头像，所以会话详情面板界面展示的头像均使用静态图\n    };\n\n    //控制人员标签颜色 \n    const getLableClass = chatMessage => {\n      if (chatMessage.fromUser == selectedChat.value.from) {\n        return 'employee';\n      } else if (chatMessage.fromUser == selectedChat.value.to) {\n        if (selectedChat.value.toType == 1) {\n          return 'wechat';\n        } else {\n          return 'other';\n        }\n      } else {\n        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {\n          const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type;\n          switch (type) {\n            case 1:\n              return 'wechat';\n            case 2:\n              return 'other';\n            default:\n              return 'other';\n          }\n        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {\n          return 'employee';\n        }\n        return 'other';\n      }\n    };\n\n    //控制人员标签显示内容\n    const getLable = chatMessage => {\n      if (chatMessage.fromUser == selectedChat.value.from) {\n        return selectedChat.value.fromLable;\n      } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return selectedChat.value.toLable;\n      } else {\n        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {\n          const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type;\n          switch (type) {\n            case 1:\n              return '@微信';\n            case 2:\n              return sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName ? `@${sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName}` : '@未知企业';\n            default:\n              return '';\n          }\n        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {\n          return '@员工';\n        }\n        return '';\n      }\n    };\n\n    //处理显示的发送方、接收方名称\n    // const handle_sender_reciever_name = (fromName) => {\n    //     if (fromName == selectedChat.value.from) {\n    //         return selectedChat.value.fromName\n    //     } else if (fromName == selectedChat.value.to) {\n    //         return selectedChat.value.toName\n    //     } else {\n\n    //         // 先检查缓存中是否已有该用户的名称\n    //         if (sender_nameAndLable.value[fromName]) {\n    //             return sender_nameAndLable.value[fromName]?.from_user.name || '加载中...';\n    //         }\n    //         // 如果缓存中没有且不在请求中，则调用接口获取\n    //         if (!pending_requests.value.has(fromName)) {\n    //             get_sender_nameAndLable(fromName);\n    //         }\n    //         return '加载中...';\n    //     }\n    // }\n\n    //使用计算属性优化handle_sender_reciever_name方法反复调用\n    const preprocessedNames = computed(() => {\n      // console.log('处理显示的发送方、接收方名称，chatmessages数组的长度为：', chatDetailMessages.value.length)\n      const namesMap = new Map();\n      chatDetailMessages.value.forEach(message => {\n        const fromName = message.fromUser;\n        if (namesMap.has(fromName)) return;\n        if (fromName == selectedChat.value.from) {\n          namesMap.set(fromName, selectedChat.value.fromName);\n        } else if (fromName == selectedChat.value.to) {\n          namesMap.set(fromName, selectedChat.value.toName);\n        } else {\n          namesMap.set(fromName, sender_nameAndLable_cache.value[fromName]?.from_user.name || (checkAndFetchName(fromName) ? '加载中...' : '未知用户'));\n        }\n      });\n      return namesMap;\n    });\n\n    //提取判断逻辑\n    const checkAndFetchName = fromUser => {\n      if (!sender_nameAndLable.value[fromUser] && !pending_requests.value.has(fromUser)) {\n        get_sender_nameAndLable(fromUser);\n        return true;\n      }\n      return false;\n    };\n\n    // 键值存储已获取的人员的 id, 避免接口冗余调用\n    const sender_nameAndLable = ref({});\n    const sender_nameAndLable_cache = ref({});\n    // 记录正在请求中的用户ID\n    const pending_requests = ref(new Set());\n\n    //一般情况下只有选中群聊会话才会调用此方法\n    const get_sender_nameAndLable = from_userId => {\n      // 如果已经在获取中或已有缓存，则不重复获取\n      if (pending_requests.value.has(from_userId) || sender_nameAndLable_cache.value[from_userId]) {\n        return;\n      }\n\n      // 添加到正在请求的集合中\n      pending_requests.value.add(from_userId);\n\n      // console.log('调用真实名称、标签获取接口')\n      const jwt_token = localStorage.getItem('access_token');\n      axiosInstance.get('/api/chatmessage/detail/getNameAndLable', {\n        params: {\n          from_userId\n        },\n        headers: {\n          Authorization: 'Bearer ' + jwt_token\n        }\n      }).then(res => {\n        if (res.data.code === 0) {\n          const data = res.data.data;\n          sender_nameAndLable.value[from_userId] = data;\n          sender_nameAndLable_cache.value[from_userId] = data;\n        }\n      }).catch(error => {\n        console.error('获取真实人员信息失败:', error);\n        sender_nameAndLable.value[from_userId] = {\n          from_user: {\n            name: '未知用户'\n          }\n        };\n        sender_nameAndLable_cache.value[from_userId] = {\n          from_user: {\n            name: '未知用户'\n          }\n        };\n      }).finally(\n      // 请求完成后从集合中移除\n      pending_requests.value.delete(from_userId));\n    };\n\n    //将时间戳转换为时间格式\n    const convertTime = timestamp => {\n      if (!timestamp) return '';\n\n      // 判断时间戳是否为13位，如果是10位则转换为13位\n      const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;\n      const date = new Date(ts);\n\n      // 获取年月日时分秒\n      const year = date.getFullYear();\n      const month = (date.getMonth() + 1).toString().padStart(2, '0');\n      const day = date.getDate().toString().padStart(2, '0');\n      const hours = date.getHours().toString().padStart(2, '0');\n      const minutes = date.getMinutes().toString().padStart(2, '0');\n      const seconds = date.getSeconds().toString().padStart(2, '0');\n\n      // 拼接成指定格式\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n    };\n\n    //-----------------------------------------------------------------------------------------------------文件消息\n    const fileUrls = ref(['']);\n    const fileUrls_cache = ref(['']);\n    const currentFile = ref(null);\n    const currentFileUrl = ref('');\n\n    // 添加文件操作相关的状态变量\n    const fileDialogVisible = ref(false);\n\n    // 根据文件扩展名获取对应的图标\n    const getFileIcon = fileext => {\n      const iconMap = {\n        // 文档类\n        'doc': require('@/assets/文件类型图片/docx.png'),\n        'docx': require('@/assets/文件类型图片/docx.png'),\n        'pdf': require('@/assets/文件类型图片/pdf.png'),\n        'txt': require('@/assets/文件类型图片/txt.png'),\n        // 表格类\n        'xls': require('@/assets/文件类型图片/xlsx.png'),\n        'xlsx': require('@/assets/文件类型图片/xlsx.png'),\n        // 演示类\n        'ppt': require('@/assets/文件类型图片/ppt.png'),\n        'pptx': require('@/assets/文件类型图片/ppt.png'),\n        // 压缩包类\n        'zip': require('@/assets/文件类型图片/zip.png'),\n        'rar': require('@/assets/文件类型图片/zip.png'),\n        '7z': require('@/assets/文件类型图片/zip.png'),\n        // 图片类\n        'jpg': require('@/assets/文件类型图片/jpg.png'),\n        'jpeg': require('@/assets/文件类型图片/jpg.png'),\n        'png': require('@/assets/文件类型图片/jpg.png'),\n        'gif': require('@/assets/文件类型图片/jpg.png'),\n        // 视频类\n        'mp4': require('@/assets/文件类型图片/mp4.png'),\n        'avi': require('@/assets/文件类型图片/mp4.png'),\n        'mov': require('@/assets/文件类型图片/mp4.png'),\n        // 音频类\n        'mp3': require('@/assets/文件类型图片/mp3.png'),\n        'wav': require('@/assets/文件类型图片/mp3.png')\n      };\n\n      // 返回对应的图标，如果没有匹配则返回默认图标\n      return iconMap[fileext] || require('@/assets/文件类型图片/default.png');\n    };\n\n    // 格式化文件大小\n    const formatFileSize = size => {\n      if (size < 1024) {\n        return size + 'B';\n      } else if (size < 1024 * 1024) {\n        return (size / 1024).toFixed(2) + 'KB';\n      } else if (size < 1024 * 1024 * 1024) {\n        return (size / (1024 * 1024)).toFixed(2) + 'MB';\n      } else {\n        return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';\n      }\n    };\n    const getFileContent = message => {\n      // console.log('点击',message.sdkfileid)\n\n      const sdkfileid = message.sdkfileid;\n      // console.log('sdkfileid:', sdkfileid)\n      const url = fileUrls.value[sdkfileid];\n      if (url) {\n        currentFileUrl.value = url;\n        currentFile.value = message;\n        fileDialogVisible.value = true;\n      }\n    };\n    const previewDialogVisible = ref(false);\n\n    // 修改预览文件方法\n    const previewFile = () => {\n      if (currentFileUrl.value && currentFile.value) {\n        const fileExt = currentFile.value.fileext?.toLowerCase();\n        if (['docx', 'xlsx', 'pdf'].includes(fileExt)) {\n          previewDialogVisible.value = true;\n          fileDialogVisible.value = false;\n        }\n      }\n    };\n\n    // 添加预览组件的回调方法\n    const handleDocxRendered = () => {\n      console.log('docx渲染完成');\n    };\n    const handleExcelRendered = () => {\n      console.log('excel渲染完成');\n    };\n    const handlePdfRendered = () => {\n      console.log('pdf渲染完成');\n    };\n    const handlePreviewError = error => {\n      console.error('预览出错:', error);\n      ElMessage.error('文件预览失败，请尝试下载后查看');\n      previewDialogVisible.value = false;\n    };\n\n    // 下载文件\n    const downloadFile = () => {\n      if (currentFileUrl.value && currentFile.value) {\n        const link = document.createElement('a');\n        link.href = currentFileUrl.value;\n        link.download = currentFile.value.filename;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n      }\n      fileDialogVisible.value = false;\n    };\n\n    // ----------------------------------------------------------------------------------------语音消息\n    //定义一个变量用于存储语音\n    const voiceUrls = ref(['']);\n    const voiceUrls_cache = ref(['']);\n\n    // 语音播放状态管理\n    const voiceStates = ref({});\n    const currentPlayingVoice = ref(null);\n    const test = url => {\n      console.log('test', url);\n    };\n\n    // ----------------------------------------------------------------------------------------图片消息\n    //定义一个变量用于存储图片url\n    const imageUrls = ref(['']);\n    const imageUrls_cache = ref(['']);\n    const getImageUrl = message => {\n      const sdkfileid = get_sdkfileid(message);\n      return imageUrls.value[sdkfileid];\n    };\n\n    // ----------------------------------------------------------------------------------------视频消息\n    const videoUrls = ref(['']);\n    const videoUrls_cache = ref(['']);\n\n    // 添加视频相关的状态管理\n    const videoRefs = ref({}); // 存储视频元素引用\n    const videoStates = ref({}); // 存储视频状态\n\n    // 设置视频引用\n    const setVideoRef = (msgid, el) => {\n      if (el) {\n        videoRefs.value[msgid] = el;\n      }\n    };\n\n    // 处理视频播放\n    const handleVideoPlay = message => {\n      try {\n        const currentVideo = videoRefs.value[message.msgid];\n        if (!currentVideo) return;\n\n        // 暂停其他正在播放的视频\n        Object.entries(videoRefs.value).forEach(([msgid, video]) => {\n          if (msgid !== message.msgid && !video.paused) {\n            video.pause();\n          }\n        });\n\n        // 更新当前视频状态\n        videoStates.value[message.msgid] = 'playing';\n        console.log('视频开始播放:', message.msgid);\n      } catch (error) {\n        console.error('视频播放处理失败:', error);\n      }\n    };\n\n    // 处理视频暂停\n    const handleVideoPause = message => {\n      try {\n        videoStates.value[message.msgid] = 'paused';\n        console.log('视频暂停:', message.msgid);\n      } catch (error) {\n        console.error('视频暂停处理失败:', error);\n      }\n    };\n\n    // 处理视频播放结束\n    const handleVideoEnded = message => {\n      try {\n        videoStates.value[message.msgid] = 'ended';\n        console.log('视频播放结束:', message.msgid);\n      } catch (error) {\n        console.error('视频结束处理失败:', error);\n      }\n    };\n\n    // 处理视频错误\n    const handleVideoError = message => {\n      try {\n        const video = videoRefs.value[message?.msgid];\n        if (!video) return;\n        console.error('视频加载失败:', video.error);\n        videoStates.value[message?.msgid] = 'error';\n\n        // 尝试重新加载视频\n        const sdkfileid = get_sdkfileid(message);\n        if (videoUrls_cache.value[sdkfileid]) {\n          console.log('尝试从缓存重新加载视频');\n          video.src = videoUrls_cache.value[sdkfileid];\n          video.load();\n        }\n\n        // ElMessage.error('视频加载失败，请重试');\n      } catch (error) {\n        console.error('视频错误处理失败:', error);\n      }\n    };\n\n    // 获取视频URL的方法\n    const getVideoUrl = message => {\n      console.log('获取视频url', message);\n      try {\n        const sdkfileid = get_sdkfileid(message);\n        const url = videoUrls.value[sdkfileid];\n\n        // 如果URL不存在但有缓存，使用缓存\n        if (!url && videoUrls_cache.value[sdkfileid]) {\n          return videoUrls_cache.value[sdkfileid];\n        }\n        return url || '';\n      } catch (error) {\n        console.error('获取视频URL失败:', error);\n        return '';\n      }\n    };\n\n    // 在组件卸载时清理资源\n    onUnmounted(() => {\n      // 清理视频URL\n      Object.values(videoUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n          URL.revokeObjectURL(url);\n        }\n      });\n\n      // 清理语音URL\n      Object.values(voiceUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n          URL.revokeObjectURL(url);\n        }\n      });\n\n      // 清理图片URL\n      Object.values(imageUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n          URL.revokeObjectURL(url);\n        }\n      });\n\n      // 清理视频状态\n      videoStates.value = {};\n      videoRefs.value = {};\n\n      // 清理语音状态\n      voiceStates.value = {};\n      currentPlayingVoice.value = null;\n    });\n\n    // ----------------------------------------------------------------------------------------名片消息\n    const getCardUserId = id => {\n      if (id && id.length > 20) {\n        return id.slice(0, 20) + '...';\n      }\n      return id;\n    };\n\n    // ----------------------------------------------------------------------------------------链接消息\n    // 处理链接点击事件\n    const handleLinkClick = link_url => {\n      if (link_url) {\n        window.open(link_url, '_blank');\n      }\n    };\n\n    // 截断URL显示\n    const truncateUrl = url => {\n      if (!url) return '';\n      const baseUrl = new URL(url);\n      let displayUrl = baseUrl.host;\n      const path = baseUrl.pathname;\n      if (path.length > 10) {\n        displayUrl += path.substring(0, 10) + '...';\n      } else {\n        displayUrl += path;\n      }\n      return displayUrl;\n    };\n\n    // ----------------------------------------------------- 功能相关\n    //刷新按钮\n    // 添加刷新状态控制\n    const isRefreshing = ref(false);\n    const overlayTop = ref(0); // 遮罩层距离顶部的距离\n\n    // 计算超出可视区域的高度（scrollHeight - clientHeight）\n    const calculateOverlayTop = () => {\n      if (!chatContentRef.value) return;\n      if (chatContentRef.value) {\n        const scrollHeight = chatContentRef.value.scrollHeight;\n        const clientHeight = chatContentRef.value.clientHeight;\n        overlayTop.value = scrollHeight - clientHeight;\n      }\n    };\n    const refreshChat = async () => {\n      if (isRefreshing.value) return; // 防止重复点击\n      calculateOverlayTop();\n      try {\n        isRefreshing.value = true;\n        const jwt_token = localStorage.getItem('access_token');\n        currentPage.value = 1;\n        searchName.value = '';\n\n        // 调用刷新接口\n        const response = await axiosInstance.post('/api/wechat/chatdata/download', {\n          seq: 0,\n          limit: 200,\n          proxy: \"\",\n          password: \"\",\n          timeout: 30,\n          type: \"AUTO_MODE\"\n        }, {\n          headers: {\n            Authorization: 'Bearer ' + jwt_token\n          }\n        });\n        if (response.data.code !== 0) {\n          ElMessage.error(response.data.msg || '刷新失败');\n          return;\n        }\n        ElMessage.success('刷新成功');\n      } catch (error) {\n        console.error('刷新失败:', error);\n        ElMessage.error('刷新失败，请检查网络或联系管理员');\n      } finally {\n        searchName.value = '';\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, '', selectedChat.value.page, selectedChat.value.limit);\n        // 请求后等待5秒\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        isRefreshing.value = false;\n      }\n    };\n\n    // ---------------------------------------------------------------------------------------------------- 消息处理\n    //消息位置：如果消息类型是同意会话类型，显示在中间；\n    // 如果是其他消息类型，需判断发送/接收方，发送方显示在右边，反之\n    const handleMessageType = message => {\n      if (message.msgtype === 'agree' || message.msgtype === 'disagree') {\n        return message.msgtype;\n      } else {\n        return sender_or_reciever(message);\n      }\n    };\n\n    //处理消息内容类型，若为撤回消息，可以通过此方法读取pre_msgid，从而进一步获取被撤回的消息，调用相应的渲染方法\n    const handleMessageContentType = message => {\n      if (message.msgType == 'revoke') {\n        // 获取chatMsg中的pre_msgid\n        if (!message.chatMsg) {\n          console.log('检查 message.chatMsg 是否存在: 该字段不存在');\n          return \" \";\n        }\n        const ChatMsg = JSON.parse(message.chatMsg);\n        if (!ChatMsg || !ChatMsg.msgtype) {\n          console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');\n          return \" \";\n        }\n\n        // 如果缓存中已有该消息，直接返回\n        if (recall_msg_cache.value[ChatMsg.msgid]) {\n          return recall_msg_cache.value[ChatMsg.msgid].msgType;\n        }\n        return \"text\"; // 默认返回text类型\n      } else if (message.msgType === 'emotion') {\n        return 'image';\n      } else if (message.msgType === 'video') {\n        return 'video';\n      } else {\n        return message.msgType;\n      }\n    };\n\n    //处理消息内容，若为撤回消息，可以通过此方法读取premise_id，从而获取被撤回的消息内容类型，调用相应的渲染方法\n    const messageContent = ref([]);\n    const handleMessageContent = message => {\n      const msgid = message.msgid;\n      try {\n        //判断message.msgType是不是revoke,如果是的话message替换成从recall_msg中message.msgid对应的值\n        if (message.msgType === 'revoke' && recall_msg.value[msgid]) {\n          console.log('替换撤回消息内容');\n          message = recall_msg.value[msgid];\n        }\n\n        // 检查 message 对象中 chatMsg 字段是否存在\n        if (!message.chatMsg) {\n          console.log('检查 message.chatMsg 是否存在: 该字段不存在');\n          return;\n        }\n\n        // 若 chatMsg 字段存在，尝试将其从字符串解析为 JSON 对象\n        const ChatMsg = JSON.parse(message.chatMsg);\n\n        // console.log('解析后的 ChatMsg 对象:', ChatMsg);\n\n        // 检查解析后的 ChatMsg 对象是否有效，以及其内部的 msgtype 字段是否存在\n        if (!ChatMsg || !ChatMsg.msgtype) {\n          console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');\n          return;\n        }\n\n        //根据不同消息类型，返回消息信息\n        switch (ChatMsg.msgtype) {\n          case 'text':\n            // return ChatMsg.text || \" \";\n            messageContent.value[msgid] = ChatMsg.text;\n            // console.log('text100:',messageContent[message.msgid].content)\n            break;\n          case 'file':\n            // return ChatMsg.file || \" \";\n            messageContent.value[msgid] = ChatMsg.file;\n            break;\n          case 'image':\n          case 'emotion':\n            break;\n          case 'agree':\n            // return ChatMsg.agree || \" \";\n            messageContent.value[msgid] = ChatMsg.agree;\n            break;\n          case 'disagree':\n            // return ChatMsg.disagree || \" \";\n            messageContent.value[msgid] = ChatMsg.disagree;\n            break;\n          case 'video':\n            // return ChatMsg.video || \" \";\n            messageContent.value[msgid] = ChatMsg.video;\n            console.log('解析后的 ChatMsg 对象:', ChatMsg.video);\n            break;\n          case 'voice':\n            // return ChatMsg.voice || \" \";\n            messageContent.value[msgid] = ChatMsg.voice;\n            console.log('解析后的 ChatMsg 对象:', ChatMsg.voice);\n            break;\n          case 'card':\n            // return ChatMsg.card || \" \";\n            messageContent.value[msgid] = ChatMsg.card;\n            break;\n          case 'link':\n            // return ChatMsg.link || \" \";\n            messageContent.value[msgid] = ChatMsg.link;\n            break;\n          default:\n            break;\n        }\n      } catch (error) {\n        console.error('解析 chatMsg 时出错:', error);\n        return;\n      }\n    };\n\n    // --------------------------------------------------------- 撤回消息相关处理\n\n    //定义一个变量用于存储撤回消息\n    const recall_msg = ref({}); // 修改为对象类型，使用 pre_msgid 作为 key\n    const recall_msg_cache = ref({}); // 新增缓存对象\n\n    // 获取撤回消息的方法\n    const fetchRecallMessage = message => {\n      // console.log('调用获取撤回消息方法')\n      if (!message.chatMsg) return;\n      const ChatMsg = JSON.parse(message.chatMsg);\n      if (!ChatMsg || !ChatMsg.msgtype || !ChatMsg.revoke) return;\n      const pre_msgid = ChatMsg.revoke.pre_msgid; //撤回原消息的id\n      const msgid = ChatMsg.msgid; //撤回消息的id\n      // console.log('撤回消息的id', msgid)\n\n      // 如果缓存中已有该消息，直接返回\n      if (recall_msg_cache.value[msgid]) {\n        recall_msg.value[msgid] = recall_msg_cache.value[msgid];\n        return;\n      }\n      const jwt_token = localStorage.getItem('access_token');\n      axiosInstance.get('/api/chatmessage/revoke_premsg', {\n        params: {\n          pre_msgid\n        },\n        headers: {\n          Authorization: 'Bearer ' + jwt_token\n        }\n      }).then(res => {\n        // console.log('获取到撤回原消息：', res.data.data)\n        if (res.data.code === 0) {\n          recall_msg_cache.value[msgid] = res.data.data;\n          recall_msg.value[msgid] = res.data.data;\n          console.log('写入撤回消息数组,写入msgid：', msgid);\n          handleMessageContent(message);\n        } else {\n          ElMessage.error(res.data.msg || '获取撤回消息失败');\n        }\n      }).catch(error => {\n        console.log(error);\n      }).finally();\n    };\n\n    // --------------------------------------------------------- 图片，表情、语音、视频消息相关处理\n\n    //获取媒体消息的sdkfileid\n    const get_sdkfileid = message => {\n      console.log('get_sdkfileid 开始处理:', message);\n      if (!message?.chatMsg) {\n        console.log('message.chatMsg 不存在');\n        return;\n      }\n      const ChatMsg = JSON.parse(message.chatMsg);\n      console.log(\"解析后的 ChatMsg:\", ChatMsg);\n      if (!ChatMsg || !ChatMsg.msgtype) {\n        console.log('ChatMsg 或 ChatMsg.msgtype 不存在');\n        return;\n      }\n      let sdkfileid = '';\n\n      // 检查消息类型和对应的属性是否存在\n      if (ChatMsg.msgtype === 'image' && ChatMsg.image && ChatMsg.image.sdkfileid) {\n        sdkfileid = ChatMsg.image.sdkfileid;\n        console.log('获取图片 sdkfileid:', sdkfileid);\n      } else if (ChatMsg.msgtype === 'emotion' && ChatMsg.emotion && ChatMsg.emotion.sdkfileid) {\n        sdkfileid = ChatMsg.emotion.sdkfileid;\n        console.log('获取表情 sdkfileid:', sdkfileid);\n      } else if (ChatMsg.msgtype === 'voice') {\n        sdkfileid = ChatMsg.voice.sdkfileid;\n        console.log('获取语音 sdkfileid:', sdkfileid, 'ChatMsg.voice:', ChatMsg.voice);\n      } else if (ChatMsg.msgtype === 'video') {\n        sdkfileid = ChatMsg.video.sdkfileid;\n        console.log('获取视频 sdkfileid:', sdkfileid);\n      } else if (ChatMsg.msgtype === 'file') {\n        sdkfileid = ChatMsg.file.sdkfileid;\n        console.log('获取文件 sdkfileid:', sdkfileid);\n      } else {\n        console.log('无法获取有效的 sdkfileid，msgtype:', ChatMsg.msgtype);\n        return \"\";\n      }\n      console.log('最终返回的 sdkfileid:', sdkfileid);\n      return sdkfileid;\n    };\n\n    // 获取媒体消息的方法\n    const fetchmediaMessage = message => {\n      console.log('fetchmediaMessage 开始处理消息:', message.msgType, message);\n      const sdkfileid = get_sdkfileid(message);\n      console.log('获取到的 sdkfileid:', sdkfileid);\n\n      // 检查缓存\n      if (message.msgType === 'image' || message.msgType === 'emotion') {\n        if (imageUrls_cache.value[sdkfileid]) {\n          console.log('从缓存获取图片/表情媒体:', sdkfileid);\n          imageUrls.value[sdkfileid] = imageUrls_cache.value[sdkfileid];\n          mediaLoadingCount.value++;\n          checkAllMediaLoaded();\n          return;\n        }\n      } else if (message.msgType === 'voice') {\n        console.log('检查语音缓存，sdkfileid:', sdkfileid, '缓存中是否存在:', !!voiceUrls_cache.value[sdkfileid]);\n        if (voiceUrls_cache.value[sdkfileid]) {\n          console.log('从缓存获取语音媒体:', sdkfileid);\n          voiceUrls.value[sdkfileid] = voiceUrls_cache.value[sdkfileid];\n          mediaLoadingCount.value++;\n          checkAllMediaLoaded();\n          return;\n        }\n      } else if (message.msgType === 'video') {\n        if (videoUrls_cache.value[sdkfileid]) {\n          console.log('从缓存获取视频媒体:', sdkfileid);\n          videoUrls.value[sdkfileid] = videoUrls_cache.value[sdkfileid];\n          mediaLoadingCount.value++;\n          checkAllMediaLoaded();\n          return;\n        }\n      } else if (message.msgType === 'file') {\n        if (fileUrls_cache.value[sdkfileid]) {\n          console.log('从缓存获取文件媒体:', sdkfileid);\n          fileUrls.value[sdkfileid] = fileUrls_cache.value[sdkfileid];\n          mediaLoadingCount.value++;\n          checkAllMediaLoaded();\n          return;\n        }\n      }\n      if (!message.chatMsg) {\n        console.log('message.chatMsg 不存在，退出下载流程');\n        return;\n      }\n      const ChatMsg = JSON.parse(message.chatMsg);\n      if (!ChatMsg || !ChatMsg.msgtype) {\n        console.log('ChatMsg 或 msgtype 不存在，退出下载流程');\n        return;\n      }\n      console.log('开始下载媒体文件，msgtype:', ChatMsg.msgtype, 'sdkfileid:', sdkfileid);\n      try {\n        const jwt_token = localStorage.getItem('access_token');\n        axiosInstance.post('/api/chatmessage/chatmedia/download', {\n          sdkfileid: sdkfileid\n        }, {\n          headers: {\n            Authorization: 'Bearer ' + jwt_token\n          },\n          responseType: 'arraybuffer'\n        }).then(async res => {\n          let blob = null;\n          let url = null;\n          if (ChatMsg.msgtype === 'image') {\n            blob = new Blob([res.data], {\n              type: 'image/jpeg'\n            });\n            url = URL.createObjectURL(blob);\n\n            // 将 URL 存储到 imageUrls 中\n            imageUrls.value[sdkfileid] = url;\n            imageUrls_cache.value[sdkfileid] = url;\n            console.log('获取到图片媒体');\n            handleMessageContent(message);\n\n            // console.log(\"附件下载接口调用成功：jpeg\", url);\n          } else if (ChatMsg.msgtype === 'emotion') {\n            // 动态图可能是 gif 格式\n            blob = new Blob([res.data], {\n              type: 'image/gif'\n            });\n            url = URL.createObjectURL(blob);\n\n            // 将 URL 存储到 imageUrls 中\n            imageUrls.value[sdkfileid] = url;\n            imageUrls_cache.value[sdkfileid] = url;\n            console.log('获取到表情媒体');\n            handleMessageContent(message);\n\n            // console.log('表情 URL已设置:', url)\n          } else if (ChatMsg.msgtype === 'voice') {\n            //需要将res.data转换为可播放的音频格式,用benz-amr-recorder将amr的arrybuffer数据流转换成wav链接\n            const audioBlob = await BenzAMRRecorder.decodeAMR(res.data);\n            const audioUrl = URL.createObjectURL(audioBlob);\n            voiceUrls.value[sdkfileid] = audioUrl;\n            voiceUrls_cache.value[sdkfileid] = audioUrl;\n            console.log('获取到语音媒体,audioUrl:', audioUrl);\n            handleMessageContent(message);\n          } else if (ChatMsg.msgtype === 'video') {\n            blob = new Blob([res.data], {\n              type: 'video/mp4'\n            });\n            url = URL.createObjectURL(blob);\n            videoUrls.value[sdkfileid] = url;\n            videoUrls_cache.value[sdkfileid] = url;\n            console.log('获取到视频媒体');\n            handleMessageContent(message);\n          } else if (ChatMsg.msgtype === 'file') {\n            blob = new Blob([res.data], {\n              type: 'application/octet-stream'\n            });\n            url = URL.createObjectURL(blob);\n            fileUrls.value[sdkfileid] = url;\n            fileUrls_cache.value[sdkfileid] = url;\n            console.log('获取到文件媒体');\n            handleMessageContent(message);\n          }\n\n          // 增加媒体加载计数\n          // mediaLoadingCount.value++;\n          // console.log('此处获取媒体消息')\n        }).catch(error => {\n          console.log(error);\n          // 即使加载失败也要计数\n\n          checkAllMediaLoaded();\n          ElMessage.error('附件下载失败，请检查网络或联系管理员');\n        });\n      } catch (error) {\n        console.error('处理媒体消息时出错:', error);\n        // 出错时也要计数\n        mediaLoadingCount.value++;\n        checkAllMediaLoaded();\n      } finally {\n        console.log('获取媒体消息finally');\n        checkAllMediaLoaded();\n        mediaLoadingCount.value++;\n      }\n    };\n\n    // 添加检查所有媒体是否加载完成的方法\n    const checkAllMediaLoaded = () => {\n      // console.log('检查所有媒体消息是否加载完成')\n      if (mediaLoadingCount.value === totalMediaCount.value && totalMediaCount.value > 0) {\n        // 所有媒体加载完成后，执行滚动\n        nextTick(() => {\n          scrollToBottom();\n        });\n      }\n    };\n    const isProcessing = ref(false);\n    // 监听消息列表变化，处理撤回消息,图片、表情消息\n    watch(chatDetailMessages, async newMessages => {\n      if (!newMessages) {\n        isProcessing.value = true;\n        return;\n      }\n      const promises = newMessages.map(async message => {\n        if (message.msgType === 'revoke') {\n          fetchRecallMessage(message);\n        } else if (message.msgType === 'image' || message.msgType === 'emotion' || message.msgType === 'voice' || message.msgType === 'video' || message.msgType === 'file') {\n          fetchmediaMessage(message);\n        } else {\n          handleMessageContent(message);\n        }\n      });\n      try {\n        await Promise.all(promises);\n      } catch (error) {\n        console.log('处理消息失败', error);\n      } finally {\n        console.log('ttttttttttttttttttttttt');\n        scrollToBottom();\n        isProcessing.value = true;\n      }\n    }, {\n      immediate: true\n    });\n\n    //调用高德地图API返回一张静态定位图\n    // const getMapImage = (message) => {\n    //     const key = 'a924f785e2522273c9b4113602e77dd0'\n    //     const image_width = 500\n    //     const image_height = 260\n    //     const longitude = message.longitude//经度\n    //     const latitude = message.latitude//纬度\n    //     const zoom = 15\n\n    //     const url = `https://restapi.amap.com/v3/staticmap?location=${longitude},${latitude}&zoom=${zoom}&size=${image_width}*${image_height}&markers=mid,,A:${longitude},${latitude}&key=${key}`\n\n    //     return url\n    // }\n\n    // 聊天记录弹窗相关\n    const chatRecordVisible = ref(false);\n    const currentChatRecord = ref(null);\n\n    // const showChatRecord = (message) => {\n    //     // 处理聊天记录数据，确保数据格式正确\n    //     const formattedRecords = message.item.map(item => {\n    //         // 根据不同的记录类型进行格式化\n    //         let formattedItem = {\n    //             timestamp: message.item.msgtime,  // 使用记录项的时间\n    //         }\n\n    //         // 根据不同的记录类型设置不同的消息类型和内容\n    //         switch (item.type) {\n    //             case 'ChatRecordText':\n    //                 formattedItem.msgtype = 'text'\n    //                 formattedItem.content = item.content\n    //                 break\n    //             case 'ChatRecordImage':\n    //                 formattedItem.msgtype = 'image'\n    //                 formattedItem.content = item.content\n    //                 break\n    //             case 'ChatRecordFile':\n    //                 formattedItem.msgtype = 'file'\n    //                 formattedItem.filename = item.content.filename\n    //                 formattedItem.filesize = item.content.filesize\n    //                 formattedItem.fileext = item.content.fileext\n    //                 formattedItem.fileurl = item.content.fileurl\n    //                 break\n    //             // 可以根据需要添加其他类型的处理\n    //         }\n\n    //         return formattedItem\n    //     })\n\n    //     currentChatRecord.value = {\n    //         title: message.title,\n    //         item: formattedRecords\n    //     }\n    //     chatRecordVisible.value = true\n    // }\n\n    // 对于会话记录消息类型，展示时将一些非文本类型消息用对应的类型信息进行标识\n    // const nonTextMessage_ToText = (item) => {\n    //     // 若传入的 item 为空，直接返回空字符串\n    //     if (!item) {\n    //         return '';\n    //     }\n    //     // 定义消息类型到文本描述的映射\n    //     const messageTypeMap = {\n    //         'ChatRecordText': item.content,\n    //         'ChatRecordImage': '[图片]',\n    //         'ChatRecordFile': '[文件]',\n    //         'ChatRecordVoice': '[语音]',\n    //         'ChatRecordVideo': '[视频]',\n    //         'ChatRecordLocation': '[位置]',\n    //         'ChatRecordCard': '[名片]',\n    //         'ChatRecordSharing': '[分享]',\n    //         'ChatRecordSystem': '[系统消息]'\n    //     };\n    //     // 根据 item 的 type 属性从映射中获取对应的文本描述\n    //     const text = messageTypeMap[item.type];\n    //     // 如果映射中存在对应的文本描述，则返回该描述；否则返回 '未知消息'\n    //     return text || '[未知消息]';\n    // };\n\n    //------------------------------------------------------------------------------------------动态调整会话详情面板的宽度\n\n    // 从父组件HomePage.vue中获取FunctionBar的开启状态\n    const FunctionBar_isCollapse = inject('FunctionBar_isCollapse');\n    // 获取当前展示的列表类型，如果是groupchat\n    const currentComponent_List = inject('currentComponent_List');\n    const chatBoardStyle = ref({\n      width: 'calc(100vw - 40.5rem)' // 初始宽度 (660px -> 41.25rem)\n    });\n\n    // 更新聊天面板宽度的计算函数\n    const updateChatBoardWidth = totalWidth => {\n      if (currentComponent_List.value == 'GroupList') {\n        totalWidth = totalWidth - 19;\n      }\n      chatBoardStyle.value.width = `calc(100vw - ${totalWidth}rem)`;\n      // console.log('当前计算的面板总宽度：', chatBoardStyle.value.width)\n    };\n\n    // 监听FunctionBar宽度变化\n    watch(() => FunctionBar_isCollapse.value, newWidth => {\n      let totalWidth = 40.5;\n      if (!newWidth) {\n        // console.log('展开状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)\n        totalWidth = 46; // 750px -> 46.875rem\n        updateChatBoardWidth(totalWidth);\n      } else {\n        // console.log('折叠状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)\n        updateChatBoardWidth(totalWidth);\n      }\n    }, {\n      immediate: true\n    });\n\n    // 监听FunctionBar宽度变化\n    watch(() => currentComponent_List.value, () => {\n      let totalWidth = 40.5;\n      if (!FunctionBar_isCollapse.value) {\n        // console.log('展开状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)\n        totalWidth = 46; // 750px -> 46.875rem\n        updateChatBoardWidth(totalWidth);\n      } else {\n        // console.log('折叠状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)\n        updateChatBoardWidth(totalWidth);\n      }\n    }, {\n      immediate: true\n    });\n\n    // 创建聊天内容区域的引用\n    const chatContentRef = ref(null);\n    // 控制加载状态，防止重复加载\n    const isLoading = ref(false);\n    // 标识是否还有更多消息可以加载\n    const hasMore = ref(true);\n    // 当前页码，用于分页请求\n    const currentPage = ref(1);\n    // 距离顶部触发加载的阈值（像素），当滚动到距离顶部100px时触发加载\n    const lastScrollTop = ref(0);\n\n    // 滚动处理函数\n    const handleScroll = async e => {\n      const {\n        scrollTop\n      } = e.target;\n      // 只有向上滚动且接近顶部时才触发加载更多\n      if (scrollTop < lastScrollTop.value && chatDetailMessagesTotal.value < selectedChat.value.limit) {\n        if (hasMore.value) {\n          ElMessage.warning('已加载全部消息');\n          hasMore.value = false;\n        }\n      }\n      if (scrollTop < loadMoreThreshold && !isLoading.value && hasMore.value) {\n        await loadMoreMessages();\n      }\n      // 更新上一次的滚动位置\n      lastScrollTop.value = scrollTop;\n    };\n\n    // 加载更多消息\n    const loadMoreMessages = async () => {\n      if (isLoading.value) {\n        return;\n      }\n      isLoading.value = true;\n      try {\n        currentPage.value += 1;\n        // console.log('获取页码：', currentPage.value)\n\n        // 模拟API调用延迟\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, searchName.value, currentPage.value, selectedChat.value.limit);\n      } catch (error) {\n        console.error('加载消息失败:', error);\n      } finally {\n        isLoading.value = false;\n      }\n    };\n\n    // 滚动到底部\n    const scrollToBottom = async () => {\n      //当前页数不是1的情况下不滚动到底部\n      console.log('滚动到底部');\n      if (currentPage.value !== 1) {\n        return;\n      }\n      //确保在DOM完全更新后再执行滚动，VUE的响应式更新是异步的，消息列表更新之后，DOM不会立即渲染，netTIck会等待VU完成DOM更新后才执行回调，这样就保证了scrollHeight的值是最新的，包含了所有消息的高度\n      await nextTick();\n      const chatContent = chatContentRef.value;\n      if (chatContent) {\n        //scrollTop为元素已经滚动的高度\n        chatContent.scrollTop = chatContent.scrollHeight;\n      }\n    };\n\n    // 在 script setup 部分添加高亮文本的方法\n    const highlightText = (text, keyword) => {\n      if (!keyword || !text) return text;\n      try {\n        // 转义特殊字符\n        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(escapedKeyword, 'gi');\n        return text.replace(regex, match => `<span class=\"highlight\">${match}</span>`);\n      } catch (error) {\n        console.error('高亮处理出错:', error);\n        return text;\n      }\n    };\n\n    // 添加图片预览相关的状态变量\n    const imagePreviewVisible = ref(false);\n    const previewImageUrl = ref('');\n\n    // 添加基础尺寸相关的状态变量\n    const scale = ref(1);\n    const baseWidth = ref(0);\n    const baseHeight = ref(0);\n    const handleImageLoad = e => {\n      const img = e.target;\n      baseWidth.value = img.naturalWidth;\n      baseHeight.value = img.naturalHeight;\n\n      // 计算初始缩放比例，使图片适应屏幕\n      const maxWidth = window.innerWidth * 0.9;\n      const maxHeight = window.innerHeight * 0.9;\n\n      // 计算宽高比\n      const imageRatio = baseWidth.value / baseHeight.value;\n      const screenRatio = maxWidth / maxHeight;\n      let initialScale;\n      if (imageRatio > screenRatio) {\n        // 图片更宽，以宽度为基准\n        initialScale = maxWidth / baseWidth.value;\n      } else {\n        // 图片更高，以高度为基准\n        initialScale = maxHeight / baseHeight.value;\n      }\n\n      // 确保初始缩放比例在合理范围内\n      scale.value = Math.min(Math.max(initialScale, minScale), maxScale);\n    };\n\n    // 处理鼠标滚轮事件\n    const handleWheel = e => {\n      // 计算新的缩放比例\n      const delta = e.deltaY > 0 ? -scaleStep : scaleStep;\n      const newScale = scale.value + delta;\n\n      // 限制缩放范围\n      if (newScale >= minScale && newScale <= maxScale) {\n        scale.value = newScale;\n      }\n    };\n\n    // 修改图片点击处理函数，重置缩放比例\n    const handleImageClick = message => {\n      const imageUrl = getImageUrl(message);\n      if (imageUrl) {\n        previewImageUrl.value = imageUrl;\n        imagePreviewVisible.value = true;\n        scale.value = 1; // 重置缩放比例\n        document.body.style.overflow = 'hidden';\n      }\n    };\n\n    // 修改关闭图片预览函数，重置缩放比例\n    const closeImagePreview = () => {\n      imagePreviewVisible.value = false;\n      scale.value = 1; // 重置缩放比例\n      document.body.style.overflow = 'auto';\n    };\n    const __returned__ = {\n      searchName,\n      selectedChat,\n      chatDetailMessages,\n      chatDetailMessagesTotal,\n      fetchedPages,\n      mediaLoadingCount,\n      totalMediaCount,\n      getDetailChatMessages,\n      chatBoardVisible,\n      old_selectedChat,\n      sender_or_reciever,\n      avatar_from_or_to,\n      getLableClass,\n      getLable,\n      preprocessedNames,\n      checkAndFetchName,\n      sender_nameAndLable,\n      sender_nameAndLable_cache,\n      pending_requests,\n      get_sender_nameAndLable,\n      convertTime,\n      fileUrls,\n      fileUrls_cache,\n      currentFile,\n      currentFileUrl,\n      fileDialogVisible,\n      getFileIcon,\n      formatFileSize,\n      getFileContent,\n      previewDialogVisible,\n      previewFile,\n      handleDocxRendered,\n      handleExcelRendered,\n      handlePdfRendered,\n      handlePreviewError,\n      downloadFile,\n      voiceUrls,\n      voiceUrls_cache,\n      voiceStates,\n      currentPlayingVoice,\n      test,\n      imageUrls,\n      imageUrls_cache,\n      getImageUrl,\n      videoUrls,\n      videoUrls_cache,\n      videoRefs,\n      videoStates,\n      setVideoRef,\n      handleVideoPlay,\n      handleVideoPause,\n      handleVideoEnded,\n      handleVideoError,\n      getVideoUrl,\n      getCardUserId,\n      handleLinkClick,\n      truncateUrl,\n      isRefreshing,\n      overlayTop,\n      calculateOverlayTop,\n      refreshChat,\n      handleMessageType,\n      handleMessageContentType,\n      messageContent,\n      handleMessageContent,\n      recall_msg,\n      recall_msg_cache,\n      fetchRecallMessage,\n      get_sdkfileid,\n      fetchmediaMessage,\n      checkAllMediaLoaded,\n      isProcessing,\n      chatRecordVisible,\n      currentChatRecord,\n      FunctionBar_isCollapse,\n      currentComponent_List,\n      chatBoardStyle,\n      updateChatBoardWidth,\n      chatContentRef,\n      isLoading,\n      hasMore,\n      currentPage,\n      loadMoreThreshold,\n      lastScrollTop,\n      handleScroll,\n      loadMoreMessages,\n      scrollToBottom,\n      highlightText,\n      imagePreviewVisible,\n      previewImageUrl,\n      scale,\n      baseWidth,\n      baseHeight,\n      minScale,\n      maxScale,\n      scaleStep,\n      handleImageLoad,\n      handleWheel,\n      handleImageClick,\n      closeImagePreview,\n      get Refresh() {\n        return Refresh;\n      },\n      get RefreshLeft() {\n        return RefreshLeft;\n      },\n      get Loading() {\n        return Loading;\n      },\n      ref,\n      inject,\n      watch,\n      nextTick,\n      onUnmounted,\n      computed,\n      get Search() {\n        return Search;\n      },\n      get Filter() {\n        return Filter;\n      },\n      get Document() {\n        return Document;\n      },\n      get Picture() {\n        return Picture;\n      },\n      get ChatDotRound() {\n        return ChatDotRound;\n      },\n      ChatRecordPopUp,\n      get axiosInstance() {\n        return axiosInstance;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get VueOfficeDocx() {\n        return VueOfficeDocx;\n      },\n      get VueOfficeExcel() {\n        return VueOfficeExcel;\n      },\n      get VueOfficePdf() {\n        return VueOfficePdf;\n      },\n      get BenzAMRRecorder() {\n        return BenzAMRRecorder;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["Refresh", "RefreshLeft", "Loading", "ref", "inject", "watch", "nextTick", "onUnmounted", "computed", "Search", "Filter", "Document", "Picture", "ChatDotRound", "ChatRecordPopUp", "axiosInstance", "ElMessage", "VueOfficeDocx", "VueOfficeExcel", "VueOfficePdf", "BenzAMRRecorder", "loadMoreThreshold", "minScale", "maxScale", "scaleStep", "searchName", "selectedC<PERSON>", "chatDetailMessages", "chatDetailMessagesTotal", "fetchedPages", "Set", "mediaLoadingCount", "totalMediaCount", "getDetailChatMessages", "from", "to", "type", "page", "limit", "console", "log", "value", "has", "jwt_token", "localStorage", "getItem", "params", "post", "headers", "Authorization", "then", "res", "newMessages", "data", "for<PERSON>ach", "message", "msgType", "length", "sort", "a", "b", "msgTime", "scrollToBottom", "add", "catch", "error", "finally", "chatBoardVisible", "old_selected<PERSON>hat", "newValue", "currentPage", "hasMore", "sender_or_reciever", "chatMessage", "fromUser", "avatar_from_or_to", "fromAvatar", "<PERSON><PERSON><PERSON><PERSON>", "require", "getLableClass", "toType", "sender_nameAndLable_cache", "from_user", "externalUserId", "userid", "getLable", "fromLable", "toLable", "corpName", "preprocessedNames", "namesMap", "Map", "fromName", "set", "to<PERSON>ame", "name", "checkAndFetchName", "sender_nameAndLable", "pending_requests", "get_sender_nameAndLable", "from_userId", "get", "code", "delete", "convertTime", "timestamp", "ts", "toString", "date", "Date", "year", "getFullYear", "month", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "fileUrls", "fileUrls_cache", "currentFile", "currentFileUrl", "fileDialogVisible", "getFileIcon", "fileext", "iconMap", "formatFileSize", "size", "toFixed", "getFileContent", "sdkfileid", "url", "previewDialogVisible", "previewFile", "fileExt", "toLowerCase", "includes", "handleDocxRendered", "handleExcelRendered", "handlePdfRendered", "handlePreviewError", "downloadFile", "link", "document", "createElement", "href", "download", "filename", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "voiceUrls", "voiceUrls_cache", "voiceStates", "currentPlayingVoice", "test", "imageUrls", "imageUrls_cache", "getImageUrl", "get_sdkfileid", "videoUrls", "videoUrls_cache", "videoRefs", "videoStates", "setVideoRef", "msgid", "el", "handleVideoPlay", "currentVideo", "Object", "entries", "video", "paused", "pause", "handleVideoPause", "handleVideoEnded", "handleVideoError", "src", "load", "getVideoUrl", "values", "startsWith", "URL", "revokeObjectURL", "getCardUserId", "id", "slice", "handleLinkClick", "link_url", "window", "open", "truncateUrl", "baseUrl", "displayUrl", "host", "path", "pathname", "substring", "isRefreshing", "overlayTop", "calculateOverlayTop", "chatContentRef", "scrollHeight", "clientHeight", "refreshChat", "response", "seq", "proxy", "password", "timeout", "msg", "success", "Promise", "resolve", "setTimeout", "handleMessageType", "msgtype", "handleMessageContentType", "chatMsg", "ChatMsg", "JSON", "parse", "recall_msg_cache", "messageContent", "handleMessageContent", "recall_msg", "text", "file", "agree", "disagree", "voice", "card", "fetchRecallMessage", "revoke", "pre_msgid", "image", "emotion", "fetchmediaMessage", "checkAllMediaLoaded", "responseType", "blob", "Blob", "createObjectURL", "audioBlob", "decodeAMR", "audioUrl", "isProcessing", "promises", "map", "all", "immediate", "chatRecordVisible", "currentChatRecord", "FunctionBar_isCollapse", "currentComponent_List", "chatBoardStyle", "width", "updateChatBoardWidth", "totalWidth", "newWidth", "isLoading", "lastScrollTop", "handleScroll", "e", "scrollTop", "target", "warning", "loadMoreMessages", "chatContent", "highlightText", "keyword", "escapedKeyword", "replace", "regex", "RegExp", "match", "imagePreviewVisible", "previewImageUrl", "scale", "baseWidth", "baseHeight", "handleImageLoad", "img", "naturalWidth", "naturalHeight", "max<PERSON><PERSON><PERSON>", "innerWidth", "maxHeight", "innerHeight", "imageRatio", "screenRatio", "initialScale", "Math", "min", "max", "handleWheel", "delta", "deltaY", "newScale", "handleImageClick", "imageUrl", "style", "overflow", "closeImagePreview"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/src/components/DetailChatBoard.vue"], "sourcesContent": ["<template>\n\n    <div class=\"chat-board\" :style=\"chatBoardStyle\" v-if=\"chatBoardVisible\">\n        <div class=\"operate-area\">\n            <div class=\"detailChat-info\" v-if=\"selectedChat.type == ''\">\n                <!-- <span class=\"sender\">{{ whichDetailChat.sender }}</span> -->\n                <span class=\"sender\">{{ selectedChat.fromName }}</span>\n                <span> 与 </span>\n                <!-- <span class=\"receiver\">{{ whichDetailChat.receiver }}</span> -->\n                <span class=\"receiver\">{{ selectedChat.toName }}</span>\n                <span> 的会话记录</span>\n            </div>\n            <div class=\"detailChat-info\" v-if=\"selectedChat.type == 'groupchat'\">\n                <!-- <span class=\"sender\">{{ whichDetailChat.sender }}</span> -->\n                <span class=\"sender\">群聊 {{ selectedChat.fromName }} 的会话记录</span>\n            </div>\n            <div class=\"refresh-area\">\n                <el-icon size=\"18px\" @click=\"refreshChat\">\n                    <Refresh />\n                </el-icon>\n            </div>\n        </div>\n        <!-- 顶部搜索区域 -->\n        <div class=\"search-area\">\n            <!-- <el-tooltip content=\"搜索功能开发中，敬请期待\" placement=\"top\" effect=\"light\"> -->\n            <el-input v-model=\"searchName\" placeholder=\"搜索聊天记录\" class=\"search-input\" clearable>\n                <template #prefix>\n                    <el-icon class=\"search-icon\">\n                        <Search />\n                    </el-icon>\n                </template>\n            </el-input>\n            <!-- </el-tooltip> -->\n\n            <div class=\"filter-options\">\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <Filter />\n                        </el-icon>\n                        筛选条件\n                    </el-button>\n                </el-tooltip>\n            </div>\n\n            <div class=\"filter-type\">\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <ChatDotRound />\n                        </el-icon>\n                        消息\n                    </el-button>\n                </el-tooltip>\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <Document />\n                        </el-icon>\n                        文件\n                    </el-button>\n                </el-tooltip>\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <Picture />\n                        </el-icon>\n                        图片\n                    </el-button>\n                </el-tooltip>\n            </div>\n\n        </div>\n\n        <!-- 聊天内容区域 -->\n        <div class=\"chat-content\" ref=\"chatContentRef\" @scroll=\"handleScroll\"\n            :style=\"{ pointerEvents: isRefreshing ? 'none' : 'auto' }\">\n            <!-- 添加遮罩层 -->\n            <div class=\"content-overlay\" v-if=\"isRefreshing\" :style=\"{ top: overlayTop + 'px' }\">\n                <el-icon class=\"loading-icon\" :size=\"30\">\n                    <Loading />\n                </el-icon>\n                <span class=\"loading-text\">刷新中...</span>\n            </div>\n            <!-- 加载更多的提示 -->\n            <div v-if=\"isLoading\" class=\"loading-more\">\n                <el-icon class=\"loading-icon\" :size=\"20\">\n                    <Loading />\n                </el-icon>\n                <span>加载更多消息...</span>\n            </div>\n            <div v-if=\"isProcessing\">\n                <div v-for=\"(message, index) in chatDetailMessages\" :key=\"index\">\n                    <!-- 消息内容 -->\n\n                    <div :class=\"['message-container', handleMessageType(message)]\" v-if=\"message.action !== 'switch'\">\n                        <!-- 头像 -->\n                        <!-- 头像、时间、具体消息内容的显示与否直接通过message.msgtype是否等于aggree、disagree控制 -->\n                        <div class=\"avatar\" v-if=\"message.msgType !== 'agree' && message.msgType !== 'disagree'\">\n                            <img :src=\"avatar_from_or_to(message)\" alt=\"人员头像\">\n                        </div>\n\n                        <!-- 消息气泡 -->\n                        <div class=\"message-content\"\n                            v-if=\"message.msgType !== 'agree' && message.msgType !== 'disagree'\">\n                            <div class=\"sender-info\">\n                                <span class=\"sender-name\">{{ preprocessedNames.get(message.fromUser) }}</span>\n                                <span class=\"sender-lable\" :class=\"getLableClass(message)\">{{ getLable(message)\n                                }}</span>\n                                <span class=\"message-time\">{{ convertTime(message.msgTime) }}</span>\n                            </div>\n                            <!-- 此处区分不同消息类型 -->\n                            <!-- 纯文本消息类型 -->\n                            <div class=\"message-bubble text-content\"\n                                v-if=\"handleMessageContentType(message) === 'text'\">\n                                <p v-html=\"highlightText(messageContent[message.msgid]?.content || '', searchName)\"></p>\n                            </div>\n\n                            <!-- 文件消息类型 -->\n                            <div class=\"message-bubble file-content\" v-if=\"handleMessageContentType(message) === 'file'\"\n                                @click=\"getFileContent(messageContent[message.msgid])\">\n                                <div class=\"left\">\n                                    <p class=\"file-name\">{{ messageContent[message.msgid]?.filename }}</p>\n                                    <p class=\"file-size\">{{ formatFileSize(messageContent[message.msgid]?.filesize) }}\n                                    </p>\n                                </div>\n                                <div class=\"right\">\n                                    <img :src=\"getFileIcon(messageContent[message.msgid]?.fileext)\" alt=\"文件类型\">\n                                </div>\n                            </div>\n\n                            <!-- 图片消息类型  -->\n                            <div class=\"message-bubble image-content\"\n                                v-if=\"handleMessageContentType(message) === 'image'\" @click=\"handleImageClick(message)\">\n                                <img :src=\"getImageUrl(message)\" alt=\"图片\">\n                            </div>\n\n                            <!-- emotion消息类型 ：通过handleMessageContentType方法转换使用图片消息类型的模板-->\n\n                            <!-- 语音类型消息 -->\n                            <div class=\"message-bubble voice-content\"\n                                v-if=\"handleMessageContentType(message) === 'voice'\">\n                                <div class=\"voice-wrapper\" @click=\"test(voiceUrls[messageContent[message.msgid]?.sdkfileid])\">\n                                    <av-waveform :src=\"voiceUrls[messageContent[message.msgid]?.sdkfileid]\"></av-waveform>\n                                </div>\n                            </div>\n\n                            <!-- 视频消息类型  -->\n                            <div class=\"message-bubble video-content\"\n                                v-if=\"handleMessageContentType(message) === 'video'\">\n                                <div class=\"video-container\">\n                                    <video controls class=\"video-player\" :src=\"getVideoUrl(message)\"\n                                        @play=\"handleVideoPlay(message)\" @pause=\"handleVideoPause(message)\"\n                                        @ended=\"handleVideoEnded(message)\" @error=\"handleVideoError(message)\"\n                                        :ref=\"el => setVideoRef(message.msgid, el)\">\n                                        <span>您的浏览器不支持视频播放</span>\n                                    </video>\n                                </div>\n                            </div>\n\n                            <!-- 卡片/名片类型消息 -->\n                            <div class=\"message-bubble card-content\"\n                                v-if=\"handleMessageContentType(message) === 'card'\">\n                                <div class=\"card-container\">\n                                    <div class=\"top\">\n                                        <div class=\"left\">\n                                            <div class=\"card-company\">\n                                                <span>{{ messageContent[message.msgid]?.corpname }}</span>\n                                            </div>\n                                            <div class=\"card-userId\">\n                                                <span>{{ getCardUserId(messageContent[message.msgid]?.userid) }}</span>\n                                            </div>\n                                            <div class=\"card-Name\">\n                                                <span>{{ preprocessedNames.get(message.fromUser) }}</span>\n                                            </div>\n                                        </div>\n                                        <div class=\"right\">\n                                            <div class=\"card-avatar\">\n                                                <img :src=\"avatar_from_or_to(message)\" alt=\"卡片头像\">\n                                            </div>\n                                        </div>\n                                    </div>\n                                    <div class=\"bottom\">\n                                        <span>个人名片</span>\n                                    </div>\n                                </div>\n                            </div>\n\n                            <!-- 链接消息 -->\n                            <div class=\"message-bubble link-content\"\n                                v-if=\"handleMessageContentType(message) === 'link'\">\n                                <div class=\"link-card-container\"\n                                    @click=\"handleLinkClick(messageContent[message.msgid]?.link_url)\">\n                                    <div class=\"link-image-container\">\n                                        <img :src=\"messageContent[message.msgid]?.image_url\" alt=\"链接图片\"\n                                            class=\"link-image\">\n                                    </div>\n                                    <div class=\"link-content\">\n                                        <div class=\"link-title\" :class=\"hasSpecialSymbol ? 'title-with-symbol' : ''\">\n                                            {{ messageContent[message.msgid]?.title }}\n                                        </div>\n                                        <div class=\"link-description\">\n                                            {{ messageContent[message.msgid]?.description }}\n                                        </div>\n                                        <div class=\"link-url\">\n                                            <span class=\"url-text\">{{\n                                                truncateUrl(messageContent[message.msgid]?.link_url)\n                                            }}</span>\n                                            <span class=\"icon-external-link\">→</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n\n                        </div>\n\n                        <div class=\"recall-lable\" v-if=\"message.msgType === 'revoke'\">\n                            <el-icon>\n                                <RefreshLeft />\n                            </el-icon>\n                            <span>撤回消息</span>\n                        </div>\n\n                        <!-- 同意会话聊天内容 -->\n                        <div class=\"agree\" v-if=\"handleMessageContentType(message) === 'agree'\">\n                            <div class=\"agree-time\">{{ convertTime(messageContent[message.msgid]?.agree_time) }}</div>\n                            <p>客户同意会话存档</p>\n                        </div>\n\n                        <!-- 不同意会话聊天内容 -->\n                        <div class=\"disagree\" v-if=\"handleMessageContentType(message) === 'disagree'\">\n                            <div class=\"disagree-time\">{{ convertTime(messageContent[message.msgid]?.disagree_time) }}\n                            </div>\n                            <p>客户不同意会话存档</p>\n                        </div>\n\n                    </div>\n\n                    <!-- 切换企业日志 -->\n                    <div class=\"switch-label\" v-if=\"message.action === 'switch'\">\n                        <div class=\"switch-time\">{{ convertTime(message.time) }}</div>\n                        <p>{{ message.user }} 切换了企业</p>\n                    </div>\n\n\n\n                </div>\n            </div>\n\n        </div>\n    </div>\n\n    <div class=\"chat-board-nonShow\" :style=\"chatBoardStyle\" v-if=\"!chatBoardVisible\">\n        <h3 class=\"non-picked\">未选择查看任何会话</h3>\n        <img src=\"../assets/会话详情面板空页.png\" alt=\"会话详情面板空页\">\n    </div>\n    <!-- 添加聊天记录弹窗组件 -->\n    <ChatRecordPopUp v-model:visible=\"chatRecordVisible\" :title=\"currentChatRecord?.title\"\n        :chat-records=\"currentChatRecord?.item || []\" @close=\"chatRecordVisible = false\" />\n\n    <!-- 添加图片预览遮罩层 -->\n    <div class=\"image-preview-mask\" v-if=\"imagePreviewVisible\" @click=\"closeImagePreview\">\n        <div class=\"image-preview-container\" @click.stop @wheel.prevent=\"handleWheel\">\n            <img :src=\"previewImageUrl\" alt=\"预览图片\" class=\"preview-image\" :style=\"{\n                width: `${baseWidth * scale}px`,\n                height: `${baseHeight * scale}px`,\n                minWidth: '100px',\n                minHeight: '100px'\n            }\" @load=\"handleImageLoad\">\n        </div>\n    </div>\n\n    <!-- 文件操作弹窗 -->\n    <el-dialog v-model=\"fileDialogVisible\" title=\"文件操作\" width=\"30%\" :show-close=\"true\" :close-on-click-modal=\"true\"\n        :close-on-press-escape=\"true\" class=\"file-dialog\">\n        <div class=\"file-dialog-content\">\n            <div class=\"file-info\">\n                <img :src=\"getFileIcon(currentFile?.fileext)\" alt=\"文件类型\" class=\"file-icon\">\n                <div class=\"file-details\">\n                    <p class=\"file-name\">{{ currentFile?.filename }}</p>\n                    <p class=\"file-size\">{{ formatFileSize(currentFile?.filesize) }}</p>\n                </div>\n            </div>\n            <el-alert title=\"当前文件预览只支持docx、xlsx、pdf格式文件\" type=\"info\" :closable=\"false\" show-icon\n                style=\"margin-bottom: 20px;\" />\n            <div class=\"file-actions\">\n                <el-button type=\"primary\" @click=\"previewFile\"\n                    :disabled=\"!['docx', 'xlsx', 'pdf'].includes(currentFile?.fileext?.toLowerCase())\">\n                    预览\n                </el-button>\n                <el-button type=\"success\" @click=\"downloadFile\">下载</el-button>\n            </div>\n        </div>\n    </el-dialog>\n\n    <!-- 文件预览弹窗 -->\n    <el-dialog v-model=\"previewDialogVisible\" :title=\"currentFile?.filename\" width=\"80%\" :show-close=\"true\"\n        :close-on-click-modal=\"false\" :close-on-press-escape=\"true\" class=\"preview-dialog\">\n        <div class=\"preview-container\">\n            <!-- docx文件预览 -->\n            <vue-office-docx v-if=\"currentFile?.fileext?.toLowerCase() === 'docx'\" :src=\"currentFileUrl\"\n                @rendered=\"handleDocxRendered\" @error=\"handlePreviewError\" />\n            <!-- excel文件预览 -->\n            <vue-office-excel v-if=\"currentFile?.fileext?.toLowerCase() === 'xlsx'\" :src=\"currentFileUrl\"\n                @rendered=\"handleExcelRendered\" @error=\"handlePreviewError\" />\n            <!-- pdf文件预览 -->\n            <vue-office-pdf v-if=\"currentFile?.fileext?.toLowerCase() === 'pdf'\" :src=\"currentFileUrl\"\n                @rendered=\"handlePdfRendered\" @error=\"handlePreviewError\" />\n        </div>\n    </el-dialog>\n</template>\n\n<script setup>\nimport { Refresh, RefreshLeft, Loading } from '@element-plus/icons-vue'\nimport { ref, inject, watch, nextTick, onUnmounted, computed } from 'vue'\n// , Clock, Location, User, CollectionTag, Microphone\nimport { Search, Filter, Document, Picture, ChatDotRound } from '@element-plus/icons-vue'\nimport ChatRecordPopUp from './ChatRecordPopUp.vue'\nimport axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例\nimport { ElMessage } from 'element-plus';\n\n\n//文件在线预览方案\nimport VueOfficeDocx from '@vue-office/docx';\nimport VueOfficeExcel from '@vue-office/excel';\nimport VueOfficePdf from '@vue-office/pdf';\n\nimport BenzAMRRecorder from 'benz-amr-recorder';\n\n\n// 搜索关键词\nconst searchName = ref('')\n\n//----------------------------------------- chat-board显示以及会话详情数据获取 ----------------------------------------\n\n\n// 获取会话详情接口的参数信息,调用接口获取会话详情数据\nconst selectedChat = inject('selectedChat', ref(null))\n\nconst chatDetailMessages = ref([]) //存储详情会话数组\n\nconst chatDetailMessagesTotal = ref(null)//记录获取到的详情会话总数\n\nconst fetchedPages = ref(new Set());//记录已经获取过的页码\n\nconst mediaLoadingCount = ref(0); // 添加媒体加载计数器\nconst totalMediaCount = ref(0);   // 添加媒体总数计数器\n\n// 调用获取会话详情接口\nconst getDetailChatMessages = (from, to, type, searchName, page, limit) => {\n\n    console.log(\"调用获取会话详情接口\")\n\n\n    if (fetchedPages.value.has(page)) {\n        return;//如果该页数据已经获取过，直接返回\n    }\n\n    const jwt_token = localStorage.getItem('access_token')\n    // 构建请求参数\n    const params = {\n        from: from,\n        to: to,\n        type: type || '',  // 确保type不为undefined\n        searchName: searchName,\n        page: page,\n        limit: limit\n    }\n    // 只有当searchName有值且不为空字符串时才添加到参数中\n    // if (searchName && searchName.trim() !== '') {\n    //     params.searchName = searchName.trim()\n    // }\n    axiosInstance.post('/api/chatmessage/detail', params, {\n        headers: { Authorization: 'Bearer ' + jwt_token }\n    }).then(res => {\n        const newMessages = res.data.data.data\n\n        // 重置媒体计数器\n        mediaLoadingCount.value = 0;\n        totalMediaCount.value = 0;\n\n        // 计算需要加载的媒体总数\n        newMessages.forEach(message => {\n            if (message.msgType === 'image' ||\n                message.msgType === 'emotion' ||\n                message.msgType === 'voice' ||\n                message.msgType === 'video' ||\n                message.msgType === 'file') {\n                totalMediaCount.value++;\n            }\n        });\n\n        // 检查chatDetailMessages.value是否为空数组\n        if (chatDetailMessages.value.length === 0) {\n            chatDetailMessages.value = newMessages.sort((a, b) => {\n                return a.msgTime - b.msgTime;\n            });\n            // 只有在没有媒体需要加载时才立即滚动\n            if (totalMediaCount.value === 0) {\n                scrollToBottom();\n            }\n        } else {\n            chatDetailMessages.value = [...chatDetailMessages.value, ...newMessages].sort((a, b) => {\n                return a.msgTime - b.msgTime;\n            });\n        }\n\n        chatDetailMessagesTotal.value = newMessages.length\n\n        //记录已经获取过的页码\n        fetchedPages.value.add(page);\n\n    }).catch(error => {\n        console.log(error);\n        ElMessage.error('获取会话详情失败，请检查网络或联系管理员');\n    }).finally(\n    );\n}\n\n// ------------------------------------------------------------------------------------------- Utils\n// ----------------------------------------------------- 显示相关\n//控制chat-board的显示\nconst chatBoardVisible = ref(false)\n\nconst old_selectedChat = ref(null)\n\nwatch(selectedChat, (newValue) => {\n    console.log('DetailChatBoard检测到selectedChat存在变化', newValue);\n\n    if (newValue) {\n        currentPage.value = 1\n        chatBoardVisible.value = true\n\n        chatDetailMessages.value = [] //清空消息数组\n        fetchedPages.value = new Set() //清空已获取页码的记录\n        searchName.value = ''\n        hasMore.value = true\n        old_selectedChat.value = newValue\n\n        getDetailChatMessages(newValue.from, newValue.to, newValue.type, searchName.value, newValue.page, newValue.limit)\n    }\n    else {\n        chatBoardVisible.value = false\n        // console.log('selectedChat为空');\n    }\n\n})\n\n// 监听搜索关键词变化\nwatch(searchName, (newValue) => {\n    if ((selectedChat.value && newValue != \"\" && newValue.length != 0 && newValue != null) || (old_selectedChat.value == selectedChat.value)) {\n        currentPage.value = 1\n        chatDetailMessages.value = [] //清空消息数组\n        fetchedPages.value = new Set() //清空已获取页码的记录\n        hasMore.value = true\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, newValue, selectedChat.value.page, selectedChat.value.limit)\n    }\n})\n\n// 控制使用sent还是receive样式\nconst sender_or_reciever = (chatMessage) => {\n    //当为群聊时，selectedChat.value.from=群聊id，selectedChat.value.to=''\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return 'sent'\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return 'received'\n    }\n    return 'received' //群聊默认\n}\n\n//控制使用员工头像还是用户头像\nconst avatar_from_or_to = (chatMessage) => {\n\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return selectedChat.value.fromAvatar\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return selectedChat.value.toAvatar\n    }\n    return require('@/assets/人员头像.png') //目前客户会话功能暂时无法拉取客户头像，所以会话详情面板界面展示的头像均使用静态图\n}\n\n//控制人员标签颜色 \nconst getLableClass = (chatMessage) => {\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return 'employee'\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        if (selectedChat.value.toType == 1) {\n            return 'wechat'\n        } else {\n            return 'other'\n        }\n    } else {\n        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {\n            const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type\n            switch (type) {\n                case 1:\n                    return 'wechat';\n                case 2:\n                    return 'other';\n                default:\n                    return 'other'\n            }\n        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {\n            return 'employee';\n        }\n        return 'other'\n    }\n\n\n}\n\n//控制人员标签显示内容\nconst getLable = (chatMessage) => {\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return selectedChat.value.fromLable\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return selectedChat.value.toLable\n    } else {\n        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {\n            const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type\n            switch (type) {\n                case 1:\n                    return '@微信';\n                case 2:\n                    return sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName ? `@${sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName}` : '@未知企业';\n                default:\n                    return ''\n            }\n        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {\n            return '@员工';\n        }\n        return ''\n    }\n\n}\n\n//处理显示的发送方、接收方名称\n// const handle_sender_reciever_name = (fromName) => {\n//     if (fromName == selectedChat.value.from) {\n//         return selectedChat.value.fromName\n//     } else if (fromName == selectedChat.value.to) {\n//         return selectedChat.value.toName\n//     } else {\n\n//         // 先检查缓存中是否已有该用户的名称\n//         if (sender_nameAndLable.value[fromName]) {\n//             return sender_nameAndLable.value[fromName]?.from_user.name || '加载中...';\n//         }\n//         // 如果缓存中没有且不在请求中，则调用接口获取\n//         if (!pending_requests.value.has(fromName)) {\n//             get_sender_nameAndLable(fromName);\n//         }\n//         return '加载中...';\n//     }\n// }\n\n//使用计算属性优化handle_sender_reciever_name方法反复调用\nconst preprocessedNames = computed(() => {\n    // console.log('处理显示的发送方、接收方名称，chatmessages数组的长度为：', chatDetailMessages.value.length)\n    const namesMap = new Map();\n    chatDetailMessages.value.forEach(message => {\n        const fromName = message.fromUser\n        if (namesMap.has(fromName)) return;\n        if (fromName == selectedChat.value.from) {\n            namesMap.set(fromName, selectedChat.value.fromName)\n        } else if (fromName == selectedChat.value.to) {\n            namesMap.set(fromName, selectedChat.value.toName)\n        } else {\n            namesMap.set(fromName,\n                sender_nameAndLable_cache.value[fromName]?.from_user.name || (checkAndFetchName(fromName) ? '加载中...' : '未知用户')\n            )\n        }\n    });\n    return namesMap;\n})\n\n//提取判断逻辑\nconst checkAndFetchName = (fromUser) => {\n    if (!sender_nameAndLable.value[fromUser] && !pending_requests.value.has(fromUser)) {\n        get_sender_nameAndLable(fromUser);\n        return true;\n    }\n    return false;\n}\n\n\n// 键值存储已获取的人员的 id, 避免接口冗余调用\nconst sender_nameAndLable = ref({});\nconst sender_nameAndLable_cache = ref({});\n// 记录正在请求中的用户ID\nconst pending_requests = ref(new Set());\n\n//一般情况下只有选中群聊会话才会调用此方法\nconst get_sender_nameAndLable = (from_userId) => {\n    // 如果已经在获取中或已有缓存，则不重复获取\n    if (pending_requests.value.has(from_userId) || sender_nameAndLable_cache.value[from_userId]) {\n        return;\n    }\n\n\n    // 添加到正在请求的集合中\n    pending_requests.value.add(from_userId);\n\n    // console.log('调用真实名称、标签获取接口')\n    const jwt_token = localStorage.getItem('access_token');\n    axiosInstance.get('/api/chatmessage/detail/getNameAndLable', {\n        params: { from_userId },\n        headers: { Authorization: 'Bearer ' + jwt_token }\n    }).then(res => {\n        if (res.data.code === 0) {\n            const data = res.data.data;\n            sender_nameAndLable.value[from_userId] = data;\n            sender_nameAndLable_cache.value[from_userId] = data;\n        }\n    }).catch(error => {\n        console.error('获取真实人员信息失败:', error);\n        sender_nameAndLable.value[from_userId] = { from_user: { name: '未知用户' } };\n        sender_nameAndLable_cache.value[from_userId] = { from_user: { name: '未知用户' } };\n    }).finally(\n        // 请求完成后从集合中移除\n        pending_requests.value.delete(from_userId)\n\n    );\n\n}\n\n//将时间戳转换为时间格式\nconst convertTime = (timestamp) => {\n    if (!timestamp) return '';\n\n    // 判断时间戳是否为13位，如果是10位则转换为13位\n    const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;\n\n    const date = new Date(ts);\n\n    // 获取年月日时分秒\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    const seconds = date.getSeconds().toString().padStart(2, '0');\n\n    // 拼接成指定格式\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n}\n\n//-----------------------------------------------------------------------------------------------------文件消息\nconst fileUrls = ref([''])\nconst fileUrls_cache = ref([''])\nconst currentFile = ref(null)\nconst currentFileUrl = ref('')\n\n\n// 添加文件操作相关的状态变量\nconst fileDialogVisible = ref(false)\n\n// 根据文件扩展名获取对应的图标\nconst getFileIcon = (fileext) => {\n    const iconMap = {\n        // 文档类\n        'doc': require('@/assets/文件类型图片/docx.png'),\n        'docx': require('@/assets/文件类型图片/docx.png'),\n        'pdf': require('@/assets/文件类型图片/pdf.png'),\n        'txt': require('@/assets/文件类型图片/txt.png'),\n        // 表格类\n        'xls': require('@/assets/文件类型图片/xlsx.png'),\n        'xlsx': require('@/assets/文件类型图片/xlsx.png'),\n        // 演示类\n        'ppt': require('@/assets/文件类型图片/ppt.png'),\n        'pptx': require('@/assets/文件类型图片/ppt.png'),\n        // 压缩包类\n        'zip': require('@/assets/文件类型图片/zip.png'),\n        'rar': require('@/assets/文件类型图片/zip.png'),\n        '7z': require('@/assets/文件类型图片/zip.png'),\n        // 图片类\n        'jpg': require('@/assets/文件类型图片/jpg.png'),\n        'jpeg': require('@/assets/文件类型图片/jpg.png'),\n        'png': require('@/assets/文件类型图片/jpg.png'),\n        'gif': require('@/assets/文件类型图片/jpg.png'),\n        // 视频类\n        'mp4': require('@/assets/文件类型图片/mp4.png'),\n        'avi': require('@/assets/文件类型图片/mp4.png'),\n        'mov': require('@/assets/文件类型图片/mp4.png'),\n        // 音频类\n        'mp3': require('@/assets/文件类型图片/mp3.png'),\n        'wav': require('@/assets/文件类型图片/mp3.png'),\n    }\n\n\n    // 返回对应的图标，如果没有匹配则返回默认图标\n    return iconMap[fileext] || require('@/assets/文件类型图片/default.png')\n}\n\n// 格式化文件大小\nconst formatFileSize = (size) => {\n    if (size < 1024) {\n        return size + 'B'\n    } else if (size < 1024 * 1024) {\n        return (size / 1024).toFixed(2) + 'KB'\n    } else if (size < 1024 * 1024 * 1024) {\n        return (size / (1024 * 1024)).toFixed(2) + 'MB'\n    } else {\n        return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'\n    }\n}\n\nconst getFileContent = (message) => {\n    // console.log('点击',message.sdkfileid)\n\n\n    const sdkfileid = message.sdkfileid\n    // console.log('sdkfileid:', sdkfileid)\n    const url = fileUrls.value[sdkfileid]\n    if (url) {\n        currentFileUrl.value = url\n        currentFile.value = message\n        fileDialogVisible.value = true\n    }\n}\n\nconst previewDialogVisible = ref(false)\n\n// 修改预览文件方法\nconst previewFile = () => {\n    if (currentFileUrl.value && currentFile.value) {\n        const fileExt = currentFile.value.fileext?.toLowerCase()\n        if (['docx', 'xlsx', 'pdf'].includes(fileExt)) {\n            previewDialogVisible.value = true\n            fileDialogVisible.value = false\n        }\n    }\n}\n\n// 添加预览组件的回调方法\nconst handleDocxRendered = () => {\n    console.log('docx渲染完成')\n}\n\nconst handleExcelRendered = () => {\n    console.log('excel渲染完成')\n}\n\nconst handlePdfRendered = () => {\n    console.log('pdf渲染完成')\n}\n\nconst handlePreviewError = (error) => {\n    console.error('预览出错:', error)\n    ElMessage.error('文件预览失败，请尝试下载后查看')\n    previewDialogVisible.value = false\n}\n\n// 下载文件\nconst downloadFile = () => {\n    if (currentFileUrl.value && currentFile.value) {\n        const link = document.createElement('a')\n        link.href = currentFileUrl.value\n        link.download = currentFile.value.filename\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n    }\n    fileDialogVisible.value = false\n}\n\n\n// ----------------------------------------------------------------------------------------语音消息\n//定义一个变量用于存储语音\nconst voiceUrls = ref([''])\nconst voiceUrls_cache = ref([''])\n\n// 语音播放状态管理\nconst voiceStates = ref({})\nconst currentPlayingVoice = ref(null)\n\n\n\nconst test = (url) => {\n    console.log('test',url)\n}\n\n\n\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------------------------图片消息\n//定义一个变量用于存储图片url\nconst imageUrls = ref([''])\nconst imageUrls_cache = ref([''])\n\nconst getImageUrl = (message) => {\n    const sdkfileid = get_sdkfileid(message)\n    return imageUrls.value[sdkfileid]\n\n}\n\n// ----------------------------------------------------------------------------------------视频消息\nconst videoUrls = ref([''])\nconst videoUrls_cache = ref([''])\n\n// 添加视频相关的状态管理\nconst videoRefs = ref({});  // 存储视频元素引用\nconst videoStates = ref({}); // 存储视频状态\n\n// 设置视频引用\nconst setVideoRef = (msgid, el) => {\n    if (el) {\n        videoRefs.value[msgid] = el;\n    }\n};\n\n// 处理视频播放\nconst handleVideoPlay = (message) => {\n    try {\n        const currentVideo = videoRefs.value[message.msgid];\n        if (!currentVideo) return;\n\n        // 暂停其他正在播放的视频\n        Object.entries(videoRefs.value).forEach(([msgid, video]) => {\n            if (msgid !== message.msgid && !video.paused) {\n                video.pause();\n            }\n        });\n\n        // 更新当前视频状态\n        videoStates.value[message.msgid] = 'playing';\n        console.log('视频开始播放:', message.msgid);\n    } catch (error) {\n        console.error('视频播放处理失败:', error);\n    }\n};\n\n// 处理视频暂停\nconst handleVideoPause = (message) => {\n    try {\n        videoStates.value[message.msgid] = 'paused';\n        console.log('视频暂停:', message.msgid);\n    } catch (error) {\n        console.error('视频暂停处理失败:', error);\n    }\n};\n\n// 处理视频播放结束\nconst handleVideoEnded = (message) => {\n    try {\n        videoStates.value[message.msgid] = 'ended';\n        console.log('视频播放结束:', message.msgid);\n    } catch (error) {\n        console.error('视频结束处理失败:', error);\n    }\n};\n\n// 处理视频错误\nconst handleVideoError = (message) => {\n    try {\n        const video = videoRefs.value[message?.msgid];\n        if (!video) return;\n\n        console.error('视频加载失败:', video.error);\n        videoStates.value[message?.msgid] = 'error';\n\n        // 尝试重新加载视频\n        const sdkfileid = get_sdkfileid(message);\n        if (videoUrls_cache.value[sdkfileid]) {\n            console.log('尝试从缓存重新加载视频');\n            video.src = videoUrls_cache.value[sdkfileid];\n            video.load();\n        }\n\n        // ElMessage.error('视频加载失败，请重试');\n    } catch (error) {\n        console.error('视频错误处理失败:', error);\n    }\n};\n\n// 获取视频URL的方法\nconst getVideoUrl = (message) => {\n    console.log('获取视频url', message)\n    try {\n        const sdkfileid = get_sdkfileid(message);\n        const url = videoUrls.value[sdkfileid];\n\n        // 如果URL不存在但有缓存，使用缓存\n        if (!url && videoUrls_cache.value[sdkfileid]) {\n            return videoUrls_cache.value[sdkfileid];\n        }\n\n        return url || '';\n    } catch (error) {\n        console.error('获取视频URL失败:', error);\n        return '';\n    }\n};\n\n// 在组件卸载时清理资源\nonUnmounted(() => {\n    // 清理视频URL\n    Object.values(videoUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n            URL.revokeObjectURL(url);\n        }\n    });\n\n    // 清理语音URL\n    Object.values(voiceUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n            URL.revokeObjectURL(url);\n        }\n    });\n\n    // 清理图片URL\n    Object.values(imageUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n            URL.revokeObjectURL(url);\n        }\n    });\n\n    // 清理视频状态\n    videoStates.value = {};\n    videoRefs.value = {};\n\n    // 清理语音状态\n    voiceStates.value = {};\n    currentPlayingVoice.value = null;\n});\n\n// ----------------------------------------------------------------------------------------名片消息\nconst getCardUserId = (id) => {\n    if (id && id.length > 20) {\n        return id.slice(0, 20) + '...'\n    }\n    return id\n}\n\n// ----------------------------------------------------------------------------------------链接消息\n// 处理链接点击事件\nconst handleLinkClick = (link_url) => {\n    if (link_url) {\n        window.open(link_url, '_blank');\n    }\n};\n\n// 截断URL显示\nconst truncateUrl = (url) => {\n    if (!url) return '';\n    const baseUrl = new URL(url);\n    let displayUrl = baseUrl.host;\n    const path = baseUrl.pathname;\n\n    if (path.length > 10) {\n        displayUrl += path.substring(0, 10) + '...';\n    } else {\n        displayUrl += path;\n    }\n\n    return displayUrl;\n};\n\n\n\n// ----------------------------------------------------- 功能相关\n//刷新按钮\n// 添加刷新状态控制\nconst isRefreshing = ref(false)\n\nconst overlayTop = ref(0) // 遮罩层距离顶部的距离\n\n// 计算超出可视区域的高度（scrollHeight - clientHeight）\nconst calculateOverlayTop = () => {\n    if (!chatContentRef.value) return;\n    if (chatContentRef.value) {\n        const scrollHeight = chatContentRef.value.scrollHeight;\n        const clientHeight = chatContentRef.value.clientHeight;\n        overlayTop.value = scrollHeight - clientHeight;\n    }\n}\n\nconst refreshChat = async () => {\n    if (isRefreshing.value) return; // 防止重复点击\n    calculateOverlayTop();\n\n    try {\n        isRefreshing.value = true;\n        const jwt_token = localStorage.getItem('access_token');\n        currentPage.value = 1\n        searchName.value = ''\n\n        // 调用刷新接口\n        const response = await axiosInstance.post('/api/wechat/chatdata/download', {\n            seq: 0,\n            limit: 200,\n            proxy: \"\",\n            password: \"\",\n            timeout: 30,\n            type: \"AUTO_MODE\"\n        }, {\n            headers: { Authorization: 'Bearer ' + jwt_token }\n        });\n\n\n        if (response.data.code !== 0) {\n            ElMessage.error(response.data.msg || '刷新失败');\n            return;\n        }\n\n        ElMessage.success('刷新成功');\n\n    } catch (error) {\n        console.error('刷新失败:', error);\n        ElMessage.error('刷新失败，请检查网络或联系管理员');\n    } finally {\n        searchName.value = ''\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, '', selectedChat.value.page, selectedChat.value.limit)\n        // 请求后等待5秒\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        isRefreshing.value = false;\n    }\n}\n\n// ---------------------------------------------------------------------------------------------------- 消息处理\n//消息位置：如果消息类型是同意会话类型，显示在中间；\n// 如果是其他消息类型，需判断发送/接收方，发送方显示在右边，反之\nconst handleMessageType = (message) => {\n    if (message.msgtype === 'agree' || message.msgtype === 'disagree') {\n        return message.msgtype\n    }\n    else {\n        return sender_or_reciever(message)\n    }\n}\n\n\n//处理消息内容类型，若为撤回消息，可以通过此方法读取pre_msgid，从而进一步获取被撤回的消息，调用相应的渲染方法\nconst handleMessageContentType = (message) => {\n    if (message.msgType == 'revoke') {\n        // 获取chatMsg中的pre_msgid\n        if (!message.chatMsg) {\n            console.log('检查 message.chatMsg 是否存在: 该字段不存在');\n            return \" \";\n        }\n        const ChatMsg = JSON.parse(message.chatMsg);\n        if (!ChatMsg || !ChatMsg.msgtype) {\n            console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');\n            return \" \";\n        }\n\n        // 如果缓存中已有该消息，直接返回\n        if (recall_msg_cache.value[ChatMsg.msgid]) {\n            return recall_msg_cache.value[ChatMsg.msgid].msgType;\n        }\n\n        return \"text\"; // 默认返回text类型\n    } else if (message.msgType === 'emotion') {\n        return 'image'\n    } else if (message.msgType === 'video') {\n        return 'video'\n    }\n    else {\n        return message.msgType\n    }\n}\n\n//处理消息内容，若为撤回消息，可以通过此方法读取premise_id，从而获取被撤回的消息内容类型，调用相应的渲染方法\nconst messageContent = ref([])\nconst handleMessageContent = (message) => {\n    const msgid = message.msgid\n    try {\n        //判断message.msgType是不是revoke,如果是的话message替换成从recall_msg中message.msgid对应的值\n        if (message.msgType === 'revoke' && recall_msg.value[msgid]) {\n            console.log('替换撤回消息内容');\n            message = recall_msg.value[msgid];\n        }\n\n        // 检查 message 对象中 chatMsg 字段是否存在\n        if (!message.chatMsg) {\n            console.log('检查 message.chatMsg 是否存在: 该字段不存在');\n            return;\n        }\n\n        // 若 chatMsg 字段存在，尝试将其从字符串解析为 JSON 对象\n        const ChatMsg = JSON.parse(message.chatMsg);\n\n        // console.log('解析后的 ChatMsg 对象:', ChatMsg);\n\n        // 检查解析后的 ChatMsg 对象是否有效，以及其内部的 msgtype 字段是否存在\n        if (!ChatMsg || !ChatMsg.msgtype) {\n            console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');\n            return;\n        }\n\n        //根据不同消息类型，返回消息信息\n        switch (ChatMsg.msgtype) {\n            case 'text':\n                // return ChatMsg.text || \" \";\n                messageContent.value[msgid] = ChatMsg.text;\n                // console.log('text100:',messageContent[message.msgid].content)\n                break;\n            case 'file':\n                // return ChatMsg.file || \" \";\n                messageContent.value[msgid] = ChatMsg.file;\n                break;\n            case 'image':\n            case 'emotion':\n                break;\n            case 'agree':\n                // return ChatMsg.agree || \" \";\n                messageContent.value[msgid] = ChatMsg.agree;\n                break;\n            case 'disagree':\n                // return ChatMsg.disagree || \" \";\n                messageContent.value[msgid] = ChatMsg.disagree;\n                break;\n            case 'video':\n                // return ChatMsg.video || \" \";\n                messageContent.value[msgid] = ChatMsg.video;\n                console.log('解析后的 ChatMsg 对象:', ChatMsg.video);\n                break;\n            case 'voice':\n                // return ChatMsg.voice || \" \";\n                messageContent.value[msgid] = ChatMsg.voice;\n                console.log('解析后的 ChatMsg 对象:', ChatMsg.voice);\n                break;\n            case 'card':\n                // return ChatMsg.card || \" \";\n                messageContent.value[msgid] = ChatMsg.card;\n                break;\n            case 'link':\n                // return ChatMsg.link || \" \";\n                messageContent.value[msgid] = ChatMsg.link;\n                break;\n            default:\n                break;\n        }\n    } catch (error) {\n        console.error('解析 chatMsg 时出错:', error);\n        return;\n    }\n}\n\n// --------------------------------------------------------- 撤回消息相关处理\n\n//定义一个变量用于存储撤回消息\nconst recall_msg = ref({})  // 修改为对象类型，使用 pre_msgid 作为 key\nconst recall_msg_cache = ref({}) // 新增缓存对象\n\n// 获取撤回消息的方法\nconst fetchRecallMessage = (message) => {\n    // console.log('调用获取撤回消息方法')\n    if (!message.chatMsg) return;\n\n    const ChatMsg = JSON.parse(message.chatMsg);\n    if (!ChatMsg || !ChatMsg.msgtype || !ChatMsg.revoke) return;\n\n    const pre_msgid = ChatMsg.revoke.pre_msgid;//撤回原消息的id\n    const msgid = ChatMsg.msgid;//撤回消息的id\n    // console.log('撤回消息的id', msgid)\n\n    // 如果缓存中已有该消息，直接返回\n    if (recall_msg_cache.value[msgid]) {\n        recall_msg.value[msgid] = recall_msg_cache.value[msgid];\n        return;\n    }\n\n    const jwt_token = localStorage.getItem('access_token');\n    axiosInstance.get('/api/chatmessage/revoke_premsg', {\n        params: { pre_msgid },\n        headers: { Authorization: 'Bearer ' + jwt_token }\n    }).then(res => {\n        // console.log('获取到撤回原消息：', res.data.data)\n        if (res.data.code === 0) {\n\n            recall_msg_cache.value[msgid] = res.data.data;\n            recall_msg.value[msgid] = res.data.data;\n            console.log('写入撤回消息数组,写入msgid：', msgid)\n            handleMessageContent(message)\n        } else {\n            ElMessage.error(res.data.msg || '获取撤回消息失败');\n        }\n    }).catch(error => {\n        console.log(error)\n    }).finally(\n    );\n\n}\n\n\n// --------------------------------------------------------- 图片，表情、语音、视频消息相关处理\n\n//获取媒体消息的sdkfileid\nconst get_sdkfileid = (message) => {\n    console.log('get_sdkfileid 开始处理:', message);\n    if (!message?.chatMsg) {\n        console.log('message.chatMsg 不存在');\n        return;\n    }\n\n    const ChatMsg = JSON.parse(message.chatMsg);\n    console.log(\"解析后的 ChatMsg:\", ChatMsg);\n    if (!ChatMsg || !ChatMsg.msgtype) {\n        console.log('ChatMsg 或 ChatMsg.msgtype 不存在');\n        return;\n    }\n\n    let sdkfileid = '';\n\n    // 检查消息类型和对应的属性是否存在\n    if (ChatMsg.msgtype === 'image' && ChatMsg.image && ChatMsg.image.sdkfileid) {\n        sdkfileid = ChatMsg.image.sdkfileid;\n        console.log('获取图片 sdkfileid:', sdkfileid);\n    } else if (ChatMsg.msgtype === 'emotion' && ChatMsg.emotion && ChatMsg.emotion.sdkfileid) {\n        sdkfileid = ChatMsg.emotion.sdkfileid;\n        console.log('获取表情 sdkfileid:', sdkfileid);\n    } else if (ChatMsg.msgtype === 'voice') {\n        sdkfileid = ChatMsg.voice.sdkfileid;\n        console.log('获取语音 sdkfileid:', sdkfileid, 'ChatMsg.voice:', ChatMsg.voice);\n    } else if (ChatMsg.msgtype === 'video') {\n        sdkfileid = ChatMsg.video.sdkfileid;\n        console.log('获取视频 sdkfileid:', sdkfileid);\n    } else if (ChatMsg.msgtype === 'file') {\n        sdkfileid = ChatMsg.file.sdkfileid;\n        console.log('获取文件 sdkfileid:', sdkfileid);\n    } else {\n        console.log('无法获取有效的 sdkfileid，msgtype:', ChatMsg.msgtype);\n        return \"\";\n    }\n\n    console.log('最终返回的 sdkfileid:', sdkfileid);\n    return sdkfileid\n}\n\n\n// 获取媒体消息的方法\nconst fetchmediaMessage = (message) => {\n    console.log('fetchmediaMessage 开始处理消息:', message.msgType, message);\n    const sdkfileid = get_sdkfileid(message)\n    console.log('获取到的 sdkfileid:', sdkfileid);\n\n    // 检查缓存\n    if (message.msgType === 'image' || message.msgType === 'emotion') {\n        if (imageUrls_cache.value[sdkfileid]) {\n            console.log('从缓存获取图片/表情媒体:', sdkfileid);\n            imageUrls.value[sdkfileid] = imageUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    } else if (message.msgType === 'voice') {\n        console.log('检查语音缓存，sdkfileid:', sdkfileid, '缓存中是否存在:', !!voiceUrls_cache.value[sdkfileid]);\n        if (voiceUrls_cache.value[sdkfileid]) {\n            console.log('从缓存获取语音媒体:', sdkfileid);\n            voiceUrls.value[sdkfileid] = voiceUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    } else if (message.msgType === 'video') {\n        if (videoUrls_cache.value[sdkfileid]) {\n            console.log('从缓存获取视频媒体:', sdkfileid);\n            videoUrls.value[sdkfileid] = videoUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    } else if (message.msgType === 'file') {\n        if (fileUrls_cache.value[sdkfileid]) {\n            console.log('从缓存获取文件媒体:', sdkfileid);\n            fileUrls.value[sdkfileid] = fileUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    }\n\n    if (!message.chatMsg) {\n        console.log('message.chatMsg 不存在，退出下载流程');\n        return;\n    }\n\n    const ChatMsg = JSON.parse(message.chatMsg);\n    if (!ChatMsg || !ChatMsg.msgtype) {\n        console.log('ChatMsg 或 msgtype 不存在，退出下载流程');\n        return;\n    }\n\n    console.log('开始下载媒体文件，msgtype:', ChatMsg.msgtype, 'sdkfileid:', sdkfileid);\n\n    try {\n\n        const jwt_token = localStorage.getItem('access_token');\n        axiosInstance.post('/api/chatmessage/chatmedia/download', {\n            sdkfileid: sdkfileid\n        }, {\n            headers: { Authorization: 'Bearer ' + jwt_token },\n            responseType: 'arraybuffer'\n        }).then(async res => {\n            let blob = null;\n            let url = null;\n            if (ChatMsg.msgtype === 'image') {\n                blob = new Blob([res.data], { type: 'image/jpeg' });\n                url = URL.createObjectURL(blob);\n\n                // 将 URL 存储到 imageUrls 中\n                imageUrls.value[sdkfileid] = url;\n                imageUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到图片媒体')\n                handleMessageContent(message)\n\n                // console.log(\"附件下载接口调用成功：jpeg\", url);\n            } else if (ChatMsg.msgtype === 'emotion') {\n                // 动态图可能是 gif 格式\n                blob = new Blob([res.data], { type: 'image/gif' });\n                url = URL.createObjectURL(blob);\n\n                // 将 URL 存储到 imageUrls 中\n                imageUrls.value[sdkfileid] = url;\n                imageUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到表情媒体')\n                handleMessageContent(message)\n\n                // console.log('表情 URL已设置:', url)\n            } else if (ChatMsg.msgtype === 'voice') {\n                //需要将res.data转换为可播放的音频格式,用benz-amr-recorder将amr的arrybuffer数据流转换成wav链接\n                const audioBlob = await BenzAMRRecorder.decodeAMR(res.data);\n                const audioUrl = URL.createObjectURL(audioBlob);\n                voiceUrls.value[sdkfileid] = audioUrl;\n                voiceUrls_cache.value[sdkfileid] = audioUrl;\n\n                console.log('获取到语音媒体,audioUrl:', audioUrl)\n                handleMessageContent(message)\n            } else if (ChatMsg.msgtype === 'video') {\n                blob = new Blob([res.data], { type: 'video/mp4' });\n                url = URL.createObjectURL(blob);\n                videoUrls.value[sdkfileid] = url;\n                videoUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到视频媒体')\n                handleMessageContent(message)\n            } else if (ChatMsg.msgtype === 'file') {\n                blob = new Blob([res.data], { type: 'application/octet-stream' });\n                url = URL.createObjectURL(blob);\n                fileUrls.value[sdkfileid] = url;\n                fileUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到文件媒体')\n                handleMessageContent(message)\n            }\n\n            // 增加媒体加载计数\n            // mediaLoadingCount.value++;\n            // console.log('此处获取媒体消息')\n\n        }).catch(error => {\n            console.log(error);\n            // 即使加载失败也要计数\n\n            checkAllMediaLoaded();\n            ElMessage.error('附件下载失败，请检查网络或联系管理员');\n        });\n    } catch (error) {\n        console.error('处理媒体消息时出错:', error);\n        // 出错时也要计数\n        mediaLoadingCount.value++;\n        checkAllMediaLoaded();\n    } finally {\n        console.log('获取媒体消息finally')\n        checkAllMediaLoaded();\n        mediaLoadingCount.value++;\n    }\n}\n\n// 添加检查所有媒体是否加载完成的方法\nconst checkAllMediaLoaded = () => {\n    // console.log('检查所有媒体消息是否加载完成')\n    if (mediaLoadingCount.value === totalMediaCount.value && totalMediaCount.value > 0) {\n        // 所有媒体加载完成后，执行滚动\n        nextTick(() => {\n            scrollToBottom();\n        });\n    }\n}\n\nconst isProcessing = ref(false)\n// 监听消息列表变化，处理撤回消息,图片、表情消息\nwatch(chatDetailMessages, async (newMessages) => {\n    if (!newMessages) {\n        isProcessing.value = true;\n        return;\n    }\n\n    const promises = newMessages.map(async message => {\n        if (message.msgType === 'revoke') {\n            fetchRecallMessage(message);\n        } else if (message.msgType === 'image' || message.msgType === 'emotion' || message.msgType === 'voice' || message.msgType === 'video' || message.msgType === 'file') {\n            fetchmediaMessage(message);\n        } else {\n            handleMessageContent(message);\n        }\n\n    });\n\n    try {\n        await Promise.all(promises);\n\n    } catch (error) {\n        console.log('处理消息失败', error);\n    } finally {\n        console.log('ttttttttttttttttttttttt')\n        scrollToBottom()\n        isProcessing.value = true;\n    }\n}, { immediate: true });\n\n\n\n\n//调用高德地图API返回一张静态定位图\n// const getMapImage = (message) => {\n//     const key = 'a924f785e2522273c9b4113602e77dd0'\n//     const image_width = 500\n//     const image_height = 260\n//     const longitude = message.longitude//经度\n//     const latitude = message.latitude//纬度\n//     const zoom = 15\n\n//     const url = `https://restapi.amap.com/v3/staticmap?location=${longitude},${latitude}&zoom=${zoom}&size=${image_width}*${image_height}&markers=mid,,A:${longitude},${latitude}&key=${key}`\n\n//     return url\n// }\n\n\n\n\n\n\n\n\n\n\n// 聊天记录弹窗相关\nconst chatRecordVisible = ref(false)\nconst currentChatRecord = ref(null)\n\n// const showChatRecord = (message) => {\n//     // 处理聊天记录数据，确保数据格式正确\n//     const formattedRecords = message.item.map(item => {\n//         // 根据不同的记录类型进行格式化\n//         let formattedItem = {\n//             timestamp: message.item.msgtime,  // 使用记录项的时间\n//         }\n\n//         // 根据不同的记录类型设置不同的消息类型和内容\n//         switch (item.type) {\n//             case 'ChatRecordText':\n//                 formattedItem.msgtype = 'text'\n//                 formattedItem.content = item.content\n//                 break\n//             case 'ChatRecordImage':\n//                 formattedItem.msgtype = 'image'\n//                 formattedItem.content = item.content\n//                 break\n//             case 'ChatRecordFile':\n//                 formattedItem.msgtype = 'file'\n//                 formattedItem.filename = item.content.filename\n//                 formattedItem.filesize = item.content.filesize\n//                 formattedItem.fileext = item.content.fileext\n//                 formattedItem.fileurl = item.content.fileurl\n//                 break\n//             // 可以根据需要添加其他类型的处理\n//         }\n\n//         return formattedItem\n//     })\n\n//     currentChatRecord.value = {\n//         title: message.title,\n//         item: formattedRecords\n//     }\n//     chatRecordVisible.value = true\n// }\n\n// 对于会话记录消息类型，展示时将一些非文本类型消息用对应的类型信息进行标识\n// const nonTextMessage_ToText = (item) => {\n//     // 若传入的 item 为空，直接返回空字符串\n//     if (!item) {\n//         return '';\n//     }\n//     // 定义消息类型到文本描述的映射\n//     const messageTypeMap = {\n//         'ChatRecordText': item.content,\n//         'ChatRecordImage': '[图片]',\n//         'ChatRecordFile': '[文件]',\n//         'ChatRecordVoice': '[语音]',\n//         'ChatRecordVideo': '[视频]',\n//         'ChatRecordLocation': '[位置]',\n//         'ChatRecordCard': '[名片]',\n//         'ChatRecordSharing': '[分享]',\n//         'ChatRecordSystem': '[系统消息]'\n//     };\n//     // 根据 item 的 type 属性从映射中获取对应的文本描述\n//     const text = messageTypeMap[item.type];\n//     // 如果映射中存在对应的文本描述，则返回该描述；否则返回 '未知消息'\n//     return text || '[未知消息]';\n// };\n\n//------------------------------------------------------------------------------------------动态调整会话详情面板的宽度\n\n// 从父组件HomePage.vue中获取FunctionBar的开启状态\nconst FunctionBar_isCollapse = inject('FunctionBar_isCollapse')\n// 获取当前展示的列表类型，如果是groupchat\nconst currentComponent_List = inject('currentComponent_List')\nconst chatBoardStyle = ref({\n    width: 'calc(100vw - 40.5rem)' // 初始宽度 (660px -> 41.25rem)\n})\n\n// 更新聊天面板宽度的计算函数\nconst updateChatBoardWidth = (totalWidth) => {\n    if (currentComponent_List.value == 'GroupList') {\n        totalWidth = totalWidth - 19\n    }\n    chatBoardStyle.value.width = `calc(100vw - ${totalWidth}rem)`\n    // console.log('当前计算的面板总宽度：', chatBoardStyle.value.width)\n}\n\n// 监听FunctionBar宽度变化\nwatch(() => FunctionBar_isCollapse.value, (newWidth) => {\n    let totalWidth = 40.5\n    if (!newWidth) {\n        // console.log('展开状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)\n        totalWidth = 46  // 750px -> 46.875rem\n        updateChatBoardWidth(totalWidth)\n    } else {\n        // console.log('折叠状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)\n        updateChatBoardWidth(totalWidth)\n\n    }\n}, { immediate: true })\n\n// 监听FunctionBar宽度变化\nwatch(() => currentComponent_List.value, () => {\n    let totalWidth = 40.5\n    if (!FunctionBar_isCollapse.value) {\n        // console.log('展开状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)\n        totalWidth = 46  // 750px -> 46.875rem\n        updateChatBoardWidth(totalWidth)\n    } else {\n        // console.log('折叠状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)\n        updateChatBoardWidth(totalWidth)\n\n    }\n}, { immediate: true })\n\n\n\n\n\n// 创建聊天内容区域的引用\nconst chatContentRef = ref(null)\n// 控制加载状态，防止重复加载\nconst isLoading = ref(false)\n// 标识是否还有更多消息可以加载\nconst hasMore = ref(true)\n// 当前页码，用于分页请求\nconst currentPage = ref(1)\n// 距离顶部触发加载的阈值（像素），当滚动到距离顶部100px时触发加载\nconst loadMoreThreshold = 100\n// 记录上一次的滚动位置\nconst lastScrollTop = ref(0)\n\n// 滚动处理函数\nconst handleScroll = async (e) => {\n    const { scrollTop } = e.target\n    // 只有向上滚动且接近顶部时才触发加载更多\n    if (scrollTop < lastScrollTop.value && chatDetailMessagesTotal.value < selectedChat.value.limit) {\n        if (hasMore.value) {\n            ElMessage.warning('已加载全部消息');\n            hasMore.value = false\n        }\n    }\n\n    if (scrollTop < loadMoreThreshold && !isLoading.value && hasMore.value) {\n        await loadMoreMessages()\n    }\n    // 更新上一次的滚动位置\n    lastScrollTop.value = scrollTop\n}\n\n// 加载更多消息\nconst loadMoreMessages = async () => {\n    if (isLoading.value) {\n        return\n    }\n\n    isLoading.value = true\n    try {\n        currentPage.value += 1\n        // console.log('获取页码：', currentPage.value)\n\n        // 模拟API调用延迟\n        await new Promise(resolve => setTimeout(resolve, 1000))\n\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, searchName.value, currentPage.value, selectedChat.value.limit)\n\n    } catch (error) {\n        console.error('加载消息失败:', error)\n    } finally {\n        isLoading.value = false\n    }\n}\n\n// 滚动到底部\nconst scrollToBottom = async () => {\n    //当前页数不是1的情况下不滚动到底部\n    console.log('滚动到底部')\n    if (currentPage.value !== 1) {\n        return\n    }\n    //确保在DOM完全更新后再执行滚动，VUE的响应式更新是异步的，消息列表更新之后，DOM不会立即渲染，netTIck会等待VU完成DOM更新后才执行回调，这样就保证了scrollHeight的值是最新的，包含了所有消息的高度\n    await nextTick()\n    const chatContent = chatContentRef.value\n    if (chatContent) {\n        //scrollTop为元素已经滚动的高度\n        chatContent.scrollTop = chatContent.scrollHeight\n    }\n}\n\n\n\n// 在 script setup 部分添加高亮文本的方法\nconst highlightText = (text, keyword) => {\n\n    if (!keyword || !text) return text;\n    try {\n        // 转义特殊字符\n        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(escapedKeyword, 'gi');\n        return text.replace(regex, match => `<span class=\"highlight\">${match}</span>`);\n    } catch (error) {\n        console.error('高亮处理出错:', error);\n        return text;\n    }\n}\n\n// 添加图片预览相关的状态变量\nconst imagePreviewVisible = ref(false)\nconst previewImageUrl = ref('')\n\n// 添加基础尺寸相关的状态变量\nconst scale = ref(1)\nconst baseWidth = ref(0)\nconst baseHeight = ref(0)\nconst minScale = 0.1  // 降低最小缩放比例\nconst maxScale = 5    // 提高最大缩放比例\nconst scaleStep = 0.1\n\n// 处理图片加载完成事件\nconst handleImageLoad = (e) => {\n    const img = e.target\n    baseWidth.value = img.naturalWidth\n    baseHeight.value = img.naturalHeight\n\n    // 计算初始缩放比例，使图片适应屏幕\n    const maxWidth = window.innerWidth * 0.9\n    const maxHeight = window.innerHeight * 0.9\n\n    // 计算宽高比\n    const imageRatio = baseWidth.value / baseHeight.value\n    const screenRatio = maxWidth / maxHeight\n\n    let initialScale\n    if (imageRatio > screenRatio) {\n        // 图片更宽，以宽度为基准\n        initialScale = maxWidth / baseWidth.value\n    } else {\n        // 图片更高，以高度为基准\n        initialScale = maxHeight / baseHeight.value\n    }\n\n    // 确保初始缩放比例在合理范围内\n    scale.value = Math.min(Math.max(initialScale, minScale), maxScale)\n}\n\n// 处理鼠标滚轮事件\nconst handleWheel = (e) => {\n    // 计算新的缩放比例\n    const delta = e.deltaY > 0 ? -scaleStep : scaleStep\n    const newScale = scale.value + delta\n\n    // 限制缩放范围\n    if (newScale >= minScale && newScale <= maxScale) {\n        scale.value = newScale\n    }\n}\n\n// 修改图片点击处理函数，重置缩放比例\nconst handleImageClick = (message) => {\n    const imageUrl = getImageUrl(message)\n    if (imageUrl) {\n        previewImageUrl.value = imageUrl\n        imagePreviewVisible.value = true\n        scale.value = 1 // 重置缩放比例\n        document.body.style.overflow = 'hidden'\n    }\n}\n\n// 修改关闭图片预览函数，重置缩放比例\nconst closeImagePreview = () => {\n    imagePreviewVisible.value = false\n    scale.value = 1 // 重置缩放比例\n    document.body.style.overflow = 'auto'\n}\n\n\n\n\n\n\n</script>\n\n<style>\n/* 整个会话详情面板 */\n.chat-board {\n    height: calc(100vh - 5rem);\n    /* width 由 JavaScript 动态计算 */\n    display: flex;\n    flex-direction: column;\n    background-color: #f7f7f7;\n    border-left: 0.0625rem solid #e6e6e6;\n    transition: 0.4s;\n}\n\n.chat-board-nonShow {\n    height: calc(100vh - 5rem);\n    /* width 由 JavaScript 动态计算 */\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n\n    background-color: #f7f7f7;\n    border-left: 0.0625rem solid #e6e6e6;\n    transition: 0.4s;\n}\n\n.chat-board-nonShow img {\n    width: 30rem;\n    height: 30rem;\n}\n\n.chat-board-nonShow h3 {\n    font-size: 1.4rem;\n    color: #5478eb;\n}\n\n/* 顶部操作区域 */\n.operate-area {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    height: 2.0625rem;\n    padding: 0.125rem 0.8125rem;\n    background-color: #ffffff;\n}\n\n\n/* 会话详情面中顶部的收发信人的信息 */\n.operate-area .detailChat-info {\n    margin-right: 0.625rem;\n}\n\n.operate-area .detailChat-info .sender,\n.operate-area .detailChat-info .receiver {\n    color: #2c2c2c;\n    font-size: 1.04rem;\n    font-weight: 500;\n}\n\n\n/* 会话详情面中顶部的刷新按钮 */\n.operate-area .refresh-area {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n}\n\n\n/* 搜索区域 */\n.search-area {\n    padding: 0 1rem;\n    padding-top: 0.3125rem;\n    padding-bottom: 0.8125rem;\n    background-color: #ffffff;\n    border-bottom: 0.0625rem solid #e6e6e6;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n}\n\n/* 搜索输入框 */\n.search-input {\n    width: 25%;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n}\n\n/* 搜索图标 */\n.search-icon {\n    color: #909399;\n}\n\n/* 筛选条件按钮 */\n.filter-options {\n    margin-left: 0.625rem;\n}\n\n/* 筛选条件按钮组：消息、文件、图片 */\n.filter-type {\n    margin-left: 0.625rem;\n    border: 0.0625rem solid #add9f5;\n}\n\n/* 筛选条件按钮 */\n.filter-type .el-button {\n    margin-left: 0;\n    border-radius: 0;\n    border: none;\n}\n\n/* 整体聊天内容区域 */\n.chat-content {\n    flex: 1;\n    overflow-y: auto;\n    padding: 1.25rem;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n}\n\n.chat-content::-webkit-scrollbar {\n    width: 0.375rem;\n}\n\n.chat-content::-webkit-scrollbar-thumb {\n    background-color: #e0e0e0;\n    border-radius: 0.1875rem;\n}\n\n.chat-content::-webkit-scrollbar-track {\n    background-color: transparent;\n}\n\n\n/* 控制单条消息的默认位置，靠左*/\n.message-container {\n    display: flex;\n    align-items: flex-start;\n    gap: 0.75rem;\n    margin-top: 1.875rem;\n    width: 100%;\n}\n\n/* 控制发送方单条消息位置，靠右 */\n.message-container.sent {\n    flex-direction: row-reverse;\n}\n\n/* 单条消息的头像默认样式 */\n.avatar {\n    width: 40px;\n    height: 40px;\n    border-radius: 4px;\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\n    /* color: white; */\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-weight: 500;\n    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);\n\n}\n\n/* 发送方单条消息的div样式 */\n.sent .avatar {\n    background: linear-gradient(135deg, #ffffff, #f0f0f0);\n    border-radius: 0.5rem;\n    flex-shrink: 0;\n}\n\n.avatar img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 0.5rem;\n}\n\n\n\n/* 单条消息的气泡内容、发送方的名字、标签、时间 */\n.message-content {\n    max-width: 60%;\n}\n\n/* 发送方名字、标签、时间样式 */\n.sent .sender-info {\n    text-align: right;\n    margin-bottom: 5px;\n}\n\n\n/* 单条消息的时间内容默认样式 */\n.sender-info .message-time {\n    text-align: center;\n    margin: 16px 0;\n    color: #909399;\n    font-size: 12px;\n}\n\n/* 单条消息的发送/接收方的标签默认样式 */\n.sender-info .sender-lable {\n    font-size: 13px;\n\n    margin-left: 5px;\n    margin-right: 5px;\n}\n\n.sender-info .sender-lable.employee {\n    color: #1890ff;\n}\n\n.sender-info .sender-lable.wechat {\n    color: #13d153;\n}\n\n.sender-info .sender-lable.other {\n    color: #ff8432;\n}\n\n/* 单条消息的发送/接收方的名称默认样式 */\n.sender-info .sender-name {\n    font-size: 12px;\n    color: #606266;\n    margin-bottom: 4px;\n}\n\n/* 单条消息的气泡默认样式 */\n.message-bubble {\n    padding: 12px 16px;\n    background-color: #ffffff;\n    border-radius: 4px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n    font-size: 14px;\n    line-height: 1.5;\n    word-break: break-word;\n\n    margin-top: 6px;\n}\n\n/* 发送方单条消息的气泡颜色样式 */\n.sent .message-bubble {\n    background-color: #eef8ff;\n}\n\n/* 图片类型消息 */\n.message-bubble img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n\n\n\n\n/* 卡片样式 */\n.message-bubble .card-content {\n    /* 给卡片内容设置基础样式，可根据整体布局调整 */\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n\n    max-width: 500px;\n    /* 限制卡片最大宽度，可按需改 */\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n    padding: 15px;\n}\n\n.top {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n}\n\n.left {\n    display: flex;\n    flex-direction: column;\n}\n\n.card-company {\n    font-size: 16px;\n    color: #333;\n    margin-bottom: 15px;\n}\n\n.card-userId {\n    font-size: 14px;\n    color: #666;\n    margin-bottom: 4px;\n}\n\n.card-Name {\n    font-size: 14px;\n    color: #999;\n}\n\n.right {\n    display: flex;\n    align-items: center;\n}\n\n.card-avatar img {\n    /* 添加背景阴影 */\n    box-shadow: 0 1px 2px rgba(94, 94, 94, 0.3);\n    width: 40px;\n    height: 40px;\n    margin-left: 20px;\n}\n\n.bottom {\n    margin-top: 8px;\n    font-size: 12px;\n    color: #999;\n    padding-top: 10px;\n    border-top: solid 1px rgba(150, 150, 150, 0.1);\n}\n\n/* 链接样式 */\n.message-bubble.link-content {\n    padding: 0;\n    margin-bottom: 16px;\n    transition: all 0.3s ease;\n}\n\n.link-card-container {\n    display: flex;\n\n    background-color: #ffffff;\n    border-radius: 12px;\n    overflow: hidden;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n    cursor: pointer;\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.link-card-container:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\n}\n\n.link-image-container {\n    /* background-color: #f5f7fa; */\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n    width: 120px;\n    /* 保持原始宽度 */\n    height: 80px;\n    /* 保持原始高度 */\n    overflow: hidden;\n    /* 防止图片溢出容器 */\n\n    padding: 13px;\n\n}\n\n.link-image {\n    max-width: 100%;\n    max-height: 100%;\n    object-fit: contain;\n    /* 保持图片宽高比，完整显示在容器内 */\n    width: auto;\n    /* 让宽度自适应高度 */\n    height: auto;\n    /* 让高度自适应宽度 */\n}\n\n.link-content {\n    flex: 1;\n    padding: 12px 0px;\n    padding-right: 18px;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n}\n\n.link-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: #1a1a1a;\n    margin-bottom: 8px;\n    line-height: 1.4;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n}\n\n.title-with-symbol {\n    padding-left: 24px;\n    position: relative;\n}\n\n.title-with-symbol::before {\n    content: attr(data-symbol);\n    position: absolute;\n    left: 0;\n    top: 0;\n    background-color: #ff4d4f;\n    color: white;\n    font-size: 12px;\n    padding: 2px 6px;\n    border-radius: 4px;\n}\n\n.link-description {\n    font-size: 14px;\n    color: #4d4d4d;\n    margin-bottom: 8px;\n    line-height: 1.5;\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n}\n\n.link-url {\n    font-size: 12px;\n    color: #0070f3;\n    display: flex;\n    align-items: center;\n}\n\n.url-text {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    flex: 1;\n}\n\n.icon-external-link {\n    margin-left: 4px;\n    font-size: 12px;\n}\n\n\n\n\n/* 地图样式 */\n.message-bubble.map-content {\n    box-sizing: border-box;\n    padding: 0;\n\n    border: 1px solid #e6e6e6;\n}\n\n\n.message-bubble .location-address {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    background-color: #f1f1f1;\n    padding: 3px 0;\n}\n\n/* 链接消息 */\n\n\n\n\n\n\n/* 小程序消息 */\n.message-bubble.weapp-content {\n    width: 100%;\n    padding: 10px;\n}\n\n.weapp-content .top .weapp-info {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    margin-bottom: 5px;\n}\n\n.weapp-content .top .weapp-info .weapp-default-icon {\n    width: 24px;\n    height: 24px;\n}\n\n.weapp-content .top .weapp-info .weapp-title {\n    margin-left: 5px;\n}\n\n.weapp-content .top .weapp-default-image {\n    background-color: #eeeeee;\n\n    width: 100%;\n    height: 200px;\n\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n\n    margin-top: 5px;\n}\n\n.weapp-content .bottom {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    margin-top: 8px;\n\n    border-top: 1px solid #e6e6e6;\n}\n\n.weapp-content .bottom .weapp-mini-icon {\n    width: 12px;\n    height: 12px;\n}\n\n.weapp-content .bottom span {\n    font-size: 12px;\n    color: #909399;\n    margin-left: 3px;\n    margin-top: 2px;\n}\n\n/* 会话记录消息 */\n.message-bubble.chatrecord-content {\n    cursor: pointer;\n}\n\n.chatrecord-content .chatrecord-part {\n    padding: 0 3px;\n    margin-top: 4px;\n    color: #909399;\n    font-weight: 300;\n}\n\n/* 待办消息样式 */\n.message-bubble.todo-content {\n    width: 300px;\n    padding: 0;\n    border: 1px solid #e6e6e6;\n    /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); */\n    transition: all 0.3s ease;\n}\n\n.todo-content .todo-title {\n    padding: 12px 16px;\n    background-color: #f0f7ff;\n    color: #1677ff;\n    font-size: 15px;\n    font-weight: 500;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n}\n\n.todo-content .todo-title::before {\n    content: '';\n    display: inline-block;\n    width: 4px;\n    height: 16px;\n    background-color: #1677ff;\n    margin-right: 12px;\n    border-radius: 2px;\n}\n\n.todo-content .todo-content {\n    padding: 12px 16px;\n    color: #666;\n    font-size: 14px;\n    line-height: 1.6;\n    background-color: #ffffff;\n}\n\n/* 发送方待办消息样式覆盖 */\n.sent .message-bubble.todo-content {\n    background-color: #ffffff;\n}\n\n.sent .todo-content .todo-title {\n    background-color: #e6f4ff;\n}\n\n/* 投票消息样式 */\n.message-bubble.vote-content {\n    width: 300px;\n    padding: 0;\n    border: 1px solid #e6e6e6;\n    background-color: #ffffff;\n    border-radius: 8px;\n}\n\n/* 发起投票样式 */\n.vote-initiate {\n    display: flex;\n    flex-direction: column;\n}\n\n.vote-header {\n    padding: 12px 16px;\n    border-bottom: 1px solid #f0f0f0;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.vote-title {\n    font-size: 15px;\n    font-weight: 500;\n    color: #333;\n}\n\n.vote-options {\n    padding: 12px 16px;\n}\n\n.vote-option {\n    margin-bottom: 12px;\n}\n\n.vote-option:last-child {\n    margin-bottom: 0;\n}\n\n/* 参与投票样式 */\n.vote-participate {\n    display: flex;\n    flex-direction: column;\n}\n\n.vote-selected {\n    padding: 12px 16px;\n}\n\n.selected-option {\n    display: flex;\n    align-items: center;\n    margin-bottom: 8px;\n    color: #67c23a;\n}\n\n.selected-option .el-icon {\n    margin-right: 8px;\n}\n\n/* 投票底部样式 */\n.vote-footer {\n    padding: 8px 16px;\n    border-top: 1px solid #f0f0f0;\n    background-color: #fafafa;\n}\n\n.vote-id {\n    font-size: 12px;\n    color: #999;\n}\n\n/* 发送方投票消息样式覆盖 */\n.sent .vote-content {\n    background-color: #ffffff;\n}\n\n.sent .vote-header {\n    background-color: #f0f7ff;\n}\n\n.sent .vote-footer {\n    background-color: #f0f7ff;\n}\n\n/* 填表消息样式 */\n.message-bubble.collect-content {\n    width: 350px;\n    padding: 0;\n    border: 1px solid #e6e6e6;\n    background-color: #ffffff;\n    border-radius: 8px;\n}\n\n/* 表单头部 */\n.collect-header {\n    padding: 16px;\n    border-bottom: 1px solid #f0f0f0;\n    background-color: #f9f9f9;\n}\n\n.collect-title {\n    font-size: 16px;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 8px;\n}\n\n.collect-info {\n    display: flex;\n    justify-content: space-between;\n    font-size: 12px;\n    color: #666;\n    margin-bottom: 4px;\n}\n\n.collect-room {\n    font-size: 12px;\n    color: #666;\n}\n\n/* 表单主体 */\n.collect-body {\n    padding: 16px;\n}\n\n.collect-item {\n    margin-bottom: 16px;\n}\n\n.collect-item:last-child {\n    margin-bottom: 0;\n}\n\n.item-label {\n    margin-bottom: 8px;\n    font-size: 14px;\n    color: #333;\n}\n\n.item-required {\n    color: #f56c6c;\n    margin-right: 4px;\n}\n\n.item-input {\n    width: 100%;\n}\n\n.item-input :deep(.el-input),\n.item-input :deep(.el-input-number),\n.item-input :deep(.el-date-picker),\n.item-input :deep(.el-time-picker) {\n    width: 100%;\n}\n\n/* 表单底部 */\n.collect-footer {\n    padding: 12px 16px;\n    border-top: 1px solid #f0f0f0;\n    text-align: right;\n    background-color: #f9f9f9;\n}\n\n/* 发送方填表消息样式覆盖 */\n.sent .collect-content {\n    background-color: #ffffff;\n}\n\n.sent .collect-header,\n.sent .collect-footer {\n    background-color: #f0f7ff;\n}\n\n\n/* TODO：红包消息样式  */\n.message-bubble.redpacket-content {\n    padding: 0;\n    width: 240px;\n    background-color: #f43f3b;\n    border-radius: 8px;\n    overflow: hidden;\n}\n\n.redpacket-wrapper {\n    display: flex;\n    flex-direction: column;\n    color: #fff;\n    cursor: pointer;\n}\n\n.redpacket-icon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 16px 0;\n}\n\n.redpacket-icon i {\n    font-size: 36px;\n}\n\n.redpacket-info {\n    padding: 0 16px 16px;\n    text-align: center;\n}\n\n.redpacket-wish {\n    font-size: 14px;\n    margin-bottom: 8px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.redpacket-amount {\n    font-size: 24px;\n    font-weight: bold;\n}\n\n.redpacket-type {\n    background-color: rgba(0, 0, 0, 0.1);\n    padding: 8px 0;\n    text-align: center;\n    font-size: 12px;\n    color: rgba(255, 255, 255, 0.9);\n}\n\n/* 发送方红包样式 */\n.sent .redpacket-content {\n    background-color: #ff6b6b;\n}\n\n.redpacket-wrapper:hover {\n    opacity: 0.95;\n}\n\n/* 会议消息样式 */\n.message-bubble.meeting-content {\n    width: 320px;\n    padding: 0;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    border-radius: 8px;\n}\n\n.meeting-header {\n    padding: 12px 16px;\n    background-color: #f0f7ff;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n}\n\n.meeting-header i {\n    color: #1677ff;\n    font-size: 18px;\n    margin-right: 8px;\n}\n\n.meeting-title {\n    font-size: 15px;\n    font-weight: 500;\n    color: #1677ff;\n    flex: 1;\n}\n\n.meeting-type {\n    font-size: 12px;\n    color: #1677ff;\n    background-color: rgba(22, 119, 255, 0.1);\n    padding: 2px 8px;\n    border-radius: 4px;\n}\n\n.meeting-body {\n    padding: 12px 16px;\n}\n\n.meeting-time,\n.meeting-address,\n.meeting-remarks {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 12px;\n    color: #606266;\n    font-size: 14px;\n}\n\n.meeting-time:last-child,\n.meeting-address:last-child,\n.meeting-remarks:last-child {\n    margin-bottom: 0;\n}\n\n.meeting-time i,\n.meeting-address i,\n.meeting-remarks i {\n    font-size: 14px;\n    margin-right: 8px;\n    margin-top: 3px;\n    color: #909399;\n}\n\n.time-details {\n    flex: 1;\n}\n\n.time-row {\n    display: flex;\n    margin-bottom: 4px;\n}\n\n.time-row:last-child {\n    margin-bottom: 0;\n}\n\n.time-label {\n    color: #909399;\n    margin-right: 4px;\n    min-width: 70px;\n}\n\n.remarks-label {\n    color: #909399;\n    margin-right: 4px;\n}\n\n.remarks-content {\n    flex: 1;\n    word-break: break-all;\n}\n\n.meeting-footer {\n    padding: 8px 16px;\n    background-color: #f7f7f7;\n    border-top: 1px solid #e6e6e6;\n}\n\n.meeting-id {\n    font-size: 12px;\n    color: #909399;\n}\n\n/* 发送方会议消息样式覆盖 */\n.sent .meeting-content {\n    background-color: #ffffff;\n}\n\n.sent .meeting-header {\n    background-color: #f0f7ff;\n}\n\n.sent .meeting-footer {\n    background-color: #f7f7f7;\n}\n\n/* 切换企业日志消息样式 */\n.message-container .switch-label {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding-top: 10px;\n    width: 100%;\n}\n\n.switch-label .switch-time {\n    font-size: 12px;\n    color: #909399;\n    margin-bottom: 7px;\n    text-align: center;\n    margin-top: 30px;\n}\n\n.switch-label p {\n    font-size: 13px;\n    color: #606266;\n    text-align: center;\n    background-color: #f2f6fc;\n    width: 150px;\n    height: 25px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 4px;\n    margin: 0 auto;\n}\n\n/* 在线文档消息样式 */\n.docmsg-content {\n    width: 280px;\n    padding: 0;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    cursor: pointer;\n}\n\n.docmsg-content .top {\n    padding: 12px 16px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n}\n\n.docmsg-content .top .docmsg-icon-mini {\n    width: 20px;\n    height: 20px;\n    margin-right: 8px;\n}\n\n.docmsg-content .top .docmsg-type {\n    font-size: 13px;\n    color: #606266;\n}\n\n.docmsg-content .bottom {\n    padding: 12px 16px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.docmsg-content .bottom .docmsg-msg {\n    flex: 1;\n    margin-right: 12px;\n}\n\n.docmsg-content .bottom .docmsg-title {\n    display: block;\n    font-size: 14px;\n    color: #303133;\n    margin-bottom: 4px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.docmsg-content .bottom .docmsg-creator {\n    display: block;\n    font-size: 12px;\n    color: #909399;\n}\n\n.docmsg-content .bottom .docmsg-icon {\n    width: 32px;\n    height: 32px;\n    flex-shrink: 0;\n}\n\n/* 发送方在线文档消息样式覆盖 */\n.sent .docmsg-content .top {\n    background-color: #f0f7ff;\n}\n\n.docmsg-content:hover {\n    background-color: #f5f7fa;\n}\n\n/* 日程消息样式 */\n.calendar-content {\n    width: 300px;\n    padding: 0;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    border-radius: 8px;\n    overflow: hidden;\n}\n\n.calendar-header {\n    padding: 12px 16px;\n    background-color: #f0f7ff;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n}\n\n.calendar-header img {\n    width: 20px;\n    height: 20px;\n    object-fit: contain;\n}\n\n.calendar-type {\n    font-size: 14px;\n    color: #1677ff;\n    font-weight: 500;\n}\n\n.calendar-body {\n    padding: 16px;\n}\n\n.calendar-title {\n    font-size: 15px;\n    font-weight: 500;\n    color: #303133;\n    margin-bottom: 16px;\n}\n\n.calendar-info .info-item {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 12px;\n    color: #606266;\n    font-size: 13px;\n    line-height: 1.5;\n}\n\n.calendar-info .info-item:last-child {\n    margin-bottom: 0;\n}\n\n.calendar-info .info-item .el-icon {\n    margin-right: 8px;\n    margin-top: 3px;\n    color: #909399;\n    flex-shrink: 0;\n    font-size: 16px;\n}\n\n.calendar-info .info-item span {\n    flex: 1;\n    word-break: break-all;\n}\n\n.calendar-info .info-item span+span {\n    margin-left: 8px;\n}\n\n/* 发送方日程消息样式覆盖 */\n.sent .calendar-content {\n    background-color: #ffffff;\n}\n\n.sent .calendar-header {\n    background-color: #f0f7ff;\n}\n\n.calendar-info .info-item.time-info {\n    align-items: flex-start;\n}\n\n.calendar-info .time-details {\n    flex: 1;\n}\n\n.calendar-info .time-details .time-row {\n    color: #606266;\n    margin-bottom: 4px;\n}\n\n.calendar-info .time-details .time-row:last-child {\n    margin-bottom: 0;\n}\n\n\n\n/* 加载更多提示样式 */\n.loading-more {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 1rem;\n    color: #909399;\n    font-size: 0.875rem;\n}\n\n.loading-more .loading-icon {\n    margin-right: 0.5rem;\n    animation: rotate 1s linear infinite;\n}\n\n@keyframes rotate {\n    from {\n        transform: rotate(0deg);\n    }\n\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n\n\n/* ----------------------------------------------------------------------------- */\n/* 文本消息内容样式 */\n.text-content {\n    max-width: 100%;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n}\n\n.text-content p {\n    margin: 0;\n    padding: 0;\n    line-height: 1.6;\n    color: #333;\n}\n\n\n/* 发送方文本消息样式 */\n.sent .text-content {\n    background-color: #eef8ff;\n}\n\n.sent .text-content p {\n    color: #333;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 图片消息样式 */\n.message-bubble.image-content {\n    max-width: 300px;\n    /* max-height: 400px; */\n    padding: 0;\n    overflow: hidden;\n    border-radius: 4px;\n    background-color: transparent;\n}\n\n.message-bubble.image-content img {\n    width: 100%;\n    height: 100%;\n    /*保持图片比例，确保完整显示 */\n    object-fit: contain;\n    display: block;\n}\n\n/* 发送方图片消息样式 */\n.sent .message-bubble.image-content {\n    background-color: transparent;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 文件消息样式  */\n.message-bubble.file-content {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 16px;\n    max-width: 90%;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    border-radius: 4px;\n    cursor: pointer;\n    transition: background-color 0.2s ease;\n}\n\n.file-content:hover {\n    background-color: #f5f7fa;\n}\n\n.file-content .left {\n    flex: 1;\n    overflow: hidden;\n    margin-right: 5px;\n    min-width: 0;\n    /* 确保flex子元素可以正确收缩 */\n}\n\n.file-content .left .file-name {\n    font-size: 14px;\n    color: #303133;\n    margin: 0 0 4px 0;\n    word-break: break-all;\n    white-space: normal;\n    overflow-wrap: break-word;\n    line-height: 1.4;\n}\n\n.file-content .left .file-size {\n    font-size: 12px;\n    color: #909399;\n    margin: 0;\n}\n\n.file-content .right {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n    margin-left: 8px;\n}\n\n.file-content .right img {\n    width: 32px;\n    height: 32px;\n    object-fit: contain;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 撤回消息样式  */\n.message-container .recall-lable {\n    display: flex;\n    flex-direction: row;\n\n    margin: auto;\n    margin-left: 2px;\n\n    background-color: #d1d1d1;\n    border-radius: 4px;\n    padding: 3px 4px;\n\n    color: #fdfdfd;\n    font-size: 12px;\n    font-weight: 200;\n    letter-spacing: 0.5px;\n\n    cursor: default;\n\n}\n\n.message-container .recall-lable .el-icon {\n    margin-right: 3px;\n}\n\n.message-container.sent .recall-lable {\n    display: flex;\n    flex-direction: row;\n\n    margin: auto;\n    margin-right: 2px;\n\n    background-color: #d1d1d1;\n    border-radius: 4px;\n    padding: 3px 4px;\n\n    color: #fdfdfd;\n    font-weight: 200;\n    letter-spacing: 0.5px;\n\n    cursor: default;\n\n}\n\n/* ----------------------------------------------------------------------------- */\n/* agree 类型消息*/\n.message-container .agree,\n.message-container .disagree {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n\n    padding-top: 10px;\n}\n\n.agree .agree-time,\n.disagree .disagree-time {\n    font-size: 12px;\n    color: #909399;\n    margin-bottom: 7px;\n    text-align: center;\n}\n\n.agree p,\n.disagree p {\n    font-size: 14px;\n    color: #359632;\n    text-align: center;\n    background-color: #adff94;\n\n    width: 150px;\n    height: 25px;\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    border-radius: 4px;\n\n}\n\n.disagree p {\n    background-color: #ff9191;\n    color: #cc3a3a;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 语音消息样式 */\n.voice-content {\n    min-width: 200px;\n    max-width: 350px;\n    padding: 12px 16px;\n    transition: all 0.3s ease;\n    border-radius: 12px;\n    background-color: #ffffff;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n    border: 1px solid #e4e7ed;\n}\n\n.voice-content:hover {\n    background-color: #f5f7fa;\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.voice-wrapper {\n    display: flex;\n    flex-direction: column;\n    gap: 8px;\n}\n\n.voice-controls {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n}\n\n.voice-play-btn {\n    width: 36px;\n    height: 36px;\n    border: none;\n    background-color: #5188ff;\n    color: white;\n    transition: all 0.3s ease;\n}\n\n.voice-play-btn:hover {\n    background-color: #4070ff;\n    transform: scale(1.05);\n}\n\n.voice-play-btn:active {\n    transform: scale(0.95);\n}\n\n.voice-duration {\n    color: #606266;\n    font-size: 14px;\n    font-weight: 500;\n    margin-left: auto;\n}\n\n.voice-visual-container {\n    width: 100%;\n    height: 40px;\n    position: relative;\n    background-color: #f8f9fa;\n    border-radius: 6px;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.voice-waveform {\n    width: 100%;\n    height: 100%;\n}\n\n.voice-loading {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    background-color: #f0f0f0;\n    width: 100%;\n    height: 100%;\n    color: #999;\n    font-size: 12px;\n}\n\n.voice-loading .loading-icon {\n    animation: rotate 1s linear infinite;\n}\n\n.voice-error {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    background-color: #fef0f0;\n    width: 100%;\n    height: 100%;\n    color: #f56c6c;\n    font-size: 12px;\n    border: 1px solid #fbc4c4;\n    border-radius: 4px;\n}\n\n.voice-error {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    gap: 8px;\n    background-color: #fef0f0;\n    width: 100%;\n    height: 100%;\n    color: #f56c6c;\n    font-size: 12px;\n    border: 1px solid #fbc4c4;\n    border-radius: 4px;\n}\n\n.voice-waveform-placeholder {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    width: 100%;\n    height: 100%;\n    padding: 0 8px;\n}\n\n.voice-bars {\n    display: flex;\n    align-items: center;\n    gap: 2px;\n    flex: 1;\n}\n\n.voice-bars .bar {\n    width: 3px;\n    background-color: #ddd;\n    border-radius: 2px;\n    transition: all 0.3s ease;\n}\n\n.voice-bars .bar:nth-child(odd) {\n    height: 20px;\n}\n\n.voice-bars .bar:nth-child(even) {\n    height: 15px;\n}\n\n.voice-bars .bar:nth-child(3n) {\n    height: 25px;\n}\n\n.voice-content.playing .voice-bars .bar {\n    background-color: #5188ff;\n    animation: wave 1.5s infinite ease-in-out;\n}\n\n.sent .voice-content.playing .voice-bars .bar {\n    background-color: #1976d2;\n}\n\n.voice-text {\n    font-size: 12px;\n    color: #666;\n    white-space: nowrap;\n}\n\n@keyframes wave {\n\n    0%,\n    100% {\n        transform: scaleY(1);\n    }\n\n    50% {\n        transform: scaleY(1.5);\n    }\n}\n\n/* 发送方语音消息样式覆盖 */\n.sent .voice-content {\n    background-color: #e3f2fd;\n    border-color: #bbdefb;\n}\n\n.sent .voice-content:hover {\n    background-color: #d6eafb;\n}\n\n.sent .voice-play-btn {\n    background-color: #1976d2;\n}\n\n.sent .voice-play-btn:hover {\n    background-color: #1565c0;\n}\n\n.sent .voice-visual-container {\n    background-color: #e8f4fd;\n}\n\n/* 音频播放时的动画效果 */\n@keyframes pulse {\n    0% {\n        transform: scale(1);\n    }\n\n    50% {\n        transform: scale(1.05);\n    }\n\n    100% {\n        transform: scale(1);\n    }\n}\n\n.voice-content.playing .voice-play-btn {\n    animation: pulse 1.5s infinite;\n}\n\n/* vue-audio-visual 样式覆盖 */\n.voice-waveform canvas {\n    border-radius: 4px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n    .voice-content {\n        min-width: 180px;\n        max-width: 280px;\n        padding: 10px 12px;\n    }\n\n    .voice-play-btn {\n        width: 32px;\n        height: 32px;\n    }\n\n    .voice-visual-container {\n        height: 35px;\n    }\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 视频消息样式 */\n.video-wrapper {\n    position: relative;\n    max-width: 300px;\n    border-radius: 8px;\n    overflow: hidden;\n    margin-bottom: 5px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    background-color: #2a2a2a;\n}\n\n.video-container {\n    position: relative;\n    width: 100%;\n}\n\n.video-player {\n    width: 100%;\n    max-height: 200px;\n    object-fit: contain;\n    display: block;\n    background-color: #000;\n    border-radius: 8px;\n}\n\n.video-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background-color: rgba(0, 0, 0, 0.3);\n    opacity: 0;\n    transition: opacity 0.3s ease;\n    cursor: pointer;\n}\n\n.video-overlay:hover {\n    opacity: 1;\n}\n\n.play-button {\n    width: 50px;\n    height: 50px;\n    border-radius: 50%;\n    background-color: rgba(255, 255, 255, 0.8);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    transition: transform 0.3s ease;\n}\n\n.play-button:hover {\n    transform: scale(1.1);\n}\n\n.play-button .el-icon {\n    font-size: 24px;\n    color: #409eff;\n}\n\n.video-info {\n    padding: 8px 10px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    background-color: #3a3a3a;\n}\n\n.video-duration {\n    font-size: 12px;\n    color: #fff;\n    padding: 2px 5px;\n    border-radius: 3px;\n    background-color: rgba(0, 0, 0, 0.3);\n}\n\n.chat-msg-sent .video-wrapper {\n    background-color: #2b82ff;\n}\n\n.chat-msg-sent .video-info {\n    background-color: #1a70e8;\n}\n\n.chat-msg-received .video-wrapper {\n    background-color: #383838;\n}\n\n.chat-msg-received .video-info {\n    background-color: #2a2a2a;\n}\n\n/* 遮罩层样式 */\n.content-overlay {\n    position: absolute;\n    top: auto;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.8);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    z-index: 10;\n    height: 100%;\n    /* width: 100vw; */\n}\n\n.content-overlay .loading-icon {\n    color: #409EFF;\n    animation: rotate 1s linear infinite;\n}\n\n.content-overlay .loading-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n}\n\n@keyframes rotate {\n    from {\n        transform: rotate(0deg);\n    }\n\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n/* 确保chat-content是relative定位，这样遮罩层的绝对定位才能正确覆盖 */\n.chat-content {\n    position: relative;\n    /* ... 保持原有样式 ... */\n}\n\n/* 添加高亮样式 */\n.highlight {\n    background-color: #fff131;\n    padding: 2px 4px;\n    border-radius: 2px;\n    color: #000;\n    /*font-weight: bold;*/\n}\n\n/* 添加图片预览相关样式 */\n.image-preview-dialog {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.image-preview-dialog :deep(.el-dialog__body) {\n    padding: 0;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background-color: rgba(0, 0, 0, 0.9);\n}\n\n.preview-image {\n    max-width: 100%;\n    max-height: 80vh;\n    object-fit: contain;\n}\n\n/* 修改图片消息样式，添加鼠标手型 */\n.message-bubble.image-content {\n    cursor: pointer;\n}\n\n/* 图片预览相关样式 */\n.image-preview-mask {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.85);\n    z-index: 2000;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.image-preview-container {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    overflow: visible;\n    /* 允许内容溢出 */\n}\n\n.preview-image {\n    object-fit: contain;\n    border-radius: 4px;\n    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);\n    transition: width 0.1s ease-out, height 0.1s ease-out;\n    /* 添加平滑过渡效果 */\n}\n\n/* 图片消息样式 */\n.message-bubble.image-content {\n    cursor: pointer;\n    transition: transform 0.2s ease;\n}\n\n.message-bubble.image-content:hover {\n    transform: scale(1.02);\n}\n\n/* 修改图片预览相关样式 */\n.image-preview-container {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    overflow: visible;\n    min-width: 100px;\n    min-height: 100px;\n}\n\n.preview-image {\n    object-fit: contain;\n    border-radius: 4px;\n    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);\n    transition: width 0.1s ease-out, height 0.1s ease-out;\n    will-change: width, height;\n    /* 优化性能 */\n}\n\n/* 文件操作弹窗样式 */\n.file-dialog-content {\n    padding: 20px;\n}\n\n.file-info {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-radius: 8px;\n}\n\n.file-icon {\n    width: 40px;\n    height: 40px;\n    margin-right: 15px;\n}\n\n.file-details {\n    flex: 1;\n}\n\n.file-details .file-name {\n    font-size: 16px;\n    color: #303133;\n    margin: 0 0 5px 0;\n    word-break: break-all;\n}\n\n.file-details .file-size {\n    font-size: 14px;\n    color: #909399;\n    margin: 0;\n}\n\n.file-actions {\n    display: flex;\n    justify-content: center;\n    gap: 20px;\n}\n\n.file-actions .el-button {\n    min-width: 100px;\n}\n\n.preview-dialog {\n    :deep(.el-dialog__body) {\n        padding: 0;\n        height: calc(80vh - 120px);\n        overflow: hidden;\n    }\n}\n\n.preview-container {\n    height: 100%;\n    overflow: auto;\n    background-color: #f5f7fa;\n    padding: 20px;\n}\n\n:deep(.vue-office-docx),\n:deep(.vue-office-excel),\n:deep(.vue-office-pdf) {\n    height: 100%;\n    background-color: #fff;\n    border-radius: 4px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;AAyTA,SAASA,OAAO,EAAEC,WAAW,EAAEC,OAAO,QAAQ,yBAAwB;AACtE,SAASC,GAAG,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,KAAI;AACxE;AACA,SAASC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,YAAY,QAAQ,yBAAwB;AACxF,OAAOC,eAAe,MAAM,uBAAsB;AAClD,OAAOC,aAAa,MAAM,kBAAkB,CAAC,CAAC;AAC9C,SAASC,SAAS,QAAQ,cAAc;;AAGxC;AACA,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,cAAc,MAAM,mBAAmB;AAC9C,OAAOC,YAAY,MAAM,iBAAiB;AAE1C,OAAOC,eAAe,MAAM,mBAAmB;;AAG/C;AA2tCA,MAAMC,iBAAiB,GAAG,GAAE;AAC5B;AAqFA,MAAMC,QAAQ,GAAG,GAAG,EAAE;AACtB,MAAMC,QAAQ,GAAG,CAAC,EAAI;AACtB,MAAMC,SAAS,GAAG,GAAE;;AAEpB;;;;;;;;IApzCA,MAAMC,UAAU,GAAGtB,GAAG,CAAC,EAAE;;IAEzB;;IAGA;IACA,MAAMuB,YAAY,GAAGtB,MAAM,CAAC,cAAc,EAAED,GAAG,CAAC,IAAI,CAAC;IAErD,MAAMwB,kBAAkB,GAAGxB,GAAG,CAAC,EAAE,CAAC,EAAC;;IAEnC,MAAMyB,uBAAuB,GAAGzB,GAAG,CAAC,IAAI,CAAC;;IAEzC,MAAM0B,YAAY,GAAG1B,GAAG,CAAC,IAAI2B,GAAG,CAAC,CAAC,CAAC,CAAC;;IAEpC,MAAMC,iBAAiB,GAAG5B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IAClC,MAAM6B,eAAe,GAAG7B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAG;;IAElC;IACA,MAAM8B,qBAAqB,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEC,IAAI,EAAEX,UAAU,EAAEY,IAAI,EAAEC,KAAK,KAAK;MAEvEC,OAAO,CAACC,GAAG,CAAC,YAAY;MAGxB,IAAIX,YAAY,CAACY,KAAK,CAACC,GAAG,CAACL,IAAI,CAAC,EAAE;QAC9B,OAAO;MACX;MAEA,MAAMM,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc;MACrD;MACA,MAAMC,MAAM,GAAG;QACXZ,IAAI,EAAEA,IAAI;QACVC,EAAE,EAAEA,EAAE;QACNC,IAAI,EAAEA,IAAI,IAAI,EAAE;QAAG;QACnBX,UAAU,EAAEA,UAAU;QACtBY,IAAI,EAAEA,IAAI;QACVC,KAAK,EAAEA;MACX;MACA;MACA;MACA;MACA;MACAvB,aAAa,CAACgC,IAAI,CAAC,yBAAyB,EAAED,MAAM,EAAE;QAClDE,OAAO,EAAE;UAAEC,aAAa,EAAE,SAAS,GAAGN;QAAU;MACpD,CAAC,CAAC,CAACO,IAAI,CAACC,GAAG,IAAI;QACX,MAAMC,WAAW,GAAGD,GAAG,CAACE,IAAI,CAACA,IAAI,CAACA,IAAG;;QAErC;QACAtB,iBAAiB,CAACU,KAAK,GAAG,CAAC;QAC3BT,eAAe,CAACS,KAAK,GAAG,CAAC;;QAEzB;QACAW,WAAW,CAACE,OAAO,CAACC,OAAO,IAAI;UAC3B,IAAIA,OAAO,CAACC,OAAO,KAAK,OAAO,IAC3BD,OAAO,CAACC,OAAO,KAAK,SAAS,IAC7BD,OAAO,CAACC,OAAO,KAAK,OAAO,IAC3BD,OAAO,CAACC,OAAO,KAAK,OAAO,IAC3BD,OAAO,CAACC,OAAO,KAAK,MAAM,EAAE;YAC5BxB,eAAe,CAACS,KAAK,EAAE;UAC3B;QACJ,CAAC,CAAC;;QAEF;QACA,IAAId,kBAAkB,CAACc,KAAK,CAACgB,MAAM,KAAK,CAAC,EAAE;UACvC9B,kBAAkB,CAACc,KAAK,GAAGW,WAAW,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YAClD,OAAOD,CAAC,CAACE,OAAO,GAAGD,CAAC,CAACC,OAAO;UAChC,CAAC,CAAC;UACF;UACA,IAAI7B,eAAe,CAACS,KAAK,KAAK,CAAC,EAAE;YAC7BqB,cAAc,CAAC,CAAC;UACpB;QACJ,CAAC,MAAM;UACHnC,kBAAkB,CAACc,KAAK,GAAG,CAAC,GAAGd,kBAAkB,CAACc,KAAK,EAAE,GAAGW,WAAW,CAAC,CAACM,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;YACpF,OAAOD,CAAC,CAACE,OAAO,GAAGD,CAAC,CAACC,OAAO;UAChC,CAAC,CAAC;QACN;QAEAjC,uBAAuB,CAACa,KAAK,GAAGW,WAAW,CAACK,MAAK;;QAEjD;QACA5B,YAAY,CAACY,KAAK,CAACsB,GAAG,CAAC1B,IAAI,CAAC;MAEhC,CAAC,CAAC,CAAC2B,KAAK,CAACC,KAAK,IAAI;QACd1B,OAAO,CAACC,GAAG,CAACyB,KAAK,CAAC;QAClBjD,SAAS,CAACiD,KAAK,CAAC,sBAAsB,CAAC;MAC3C,CAAC,CAAC,CAACC,OAAO,CACV,CAAC;IACL;;IAEA;IACA;IACA;IACA,MAAMC,gBAAgB,GAAGhE,GAAG,CAAC,KAAK;IAElC,MAAMiE,gBAAgB,GAAGjE,GAAG,CAAC,IAAI;IAEjCE,KAAK,CAACqB,YAAY,EAAG2C,QAAQ,IAAK;MAC9B9B,OAAO,CAACC,GAAG,CAAC,oCAAoC,EAAE6B,QAAQ,CAAC;MAE3D,IAAIA,QAAQ,EAAE;QACVC,WAAW,CAAC7B,KAAK,GAAG;QACpB0B,gBAAgB,CAAC1B,KAAK,GAAG,IAAG;QAE5Bd,kBAAkB,CAACc,KAAK,GAAG,EAAE,EAAC;QAC9BZ,YAAY,CAACY,KAAK,GAAG,IAAIX,GAAG,CAAC,CAAC,EAAC;QAC/BL,UAAU,CAACgB,KAAK,GAAG,EAAC;QACpB8B,OAAO,CAAC9B,KAAK,GAAG,IAAG;QACnB2B,gBAAgB,CAAC3B,KAAK,GAAG4B,QAAO;QAEhCpC,qBAAqB,CAACoC,QAAQ,CAACnC,IAAI,EAAEmC,QAAQ,CAAClC,EAAE,EAAEkC,QAAQ,CAACjC,IAAI,EAAEX,UAAU,CAACgB,KAAK,EAAE4B,QAAQ,CAAChC,IAAI,EAAEgC,QAAQ,CAAC/B,KAAK;MACpH,OACK;QACD6B,gBAAgB,CAAC1B,KAAK,GAAG,KAAI;QAC7B;MACJ;IAEJ,CAAC;;IAED;IACApC,KAAK,CAACoB,UAAU,EAAG4C,QAAQ,IAAK;MAC5B,IAAK3C,YAAY,CAACe,KAAK,IAAI4B,QAAQ,IAAI,EAAE,IAAIA,QAAQ,CAACZ,MAAM,IAAI,CAAC,IAAIY,QAAQ,IAAI,IAAI,IAAMD,gBAAgB,CAAC3B,KAAK,IAAIf,YAAY,CAACe,KAAM,EAAE;QACtI6B,WAAW,CAAC7B,KAAK,GAAG;QACpBd,kBAAkB,CAACc,KAAK,GAAG,EAAE,EAAC;QAC9BZ,YAAY,CAACY,KAAK,GAAG,IAAIX,GAAG,CAAC,CAAC,EAAC;QAC/ByC,OAAO,CAAC9B,KAAK,GAAG,IAAG;QACnBR,qBAAqB,CAACP,YAAY,CAACe,KAAK,CAACP,IAAI,EAAER,YAAY,CAACe,KAAK,CAACN,EAAE,EAAET,YAAY,CAACe,KAAK,CAACL,IAAI,EAAEiC,QAAQ,EAAE3C,YAAY,CAACe,KAAK,CAACJ,IAAI,EAAEX,YAAY,CAACe,KAAK,CAACH,KAAK;MAC9J;IACJ,CAAC;;IAED;IACA,MAAMkC,kBAAkB,GAAIC,WAAW,IAAK;MACxC;MACA,IAAIA,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACP,IAAI,EAAE;QACjD,OAAO,MAAK;MAChB,CAAC,MAAM,IAAIuC,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACN,EAAE,EAAE;QACtD,OAAO,UAAS;MACpB;MACA,OAAO,UAAU,EAAC;IACtB;;IAEA;IACA,MAAMwC,iBAAiB,GAAIF,WAAW,IAAK;MAEvC,IAAIA,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACP,IAAI,EAAE;QACjD,OAAOR,YAAY,CAACe,KAAK,CAACmC,UAAS;MACvC,CAAC,MAAM,IAAIH,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACN,EAAE,EAAE;QACtD,OAAOT,YAAY,CAACe,KAAK,CAACoC,QAAO;MACrC;MACA,OAAOC,OAAO,CAAC,mBAAmB,CAAC,EAAC;IACxC;;IAEA;IACA,MAAMC,aAAa,GAAIN,WAAW,IAAK;MACnC,IAAIA,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACP,IAAI,EAAE;QACjD,OAAO,UAAS;MACpB,CAAC,MAAM,IAAIuC,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACN,EAAE,EAAE;QACtD,IAAIT,YAAY,CAACe,KAAK,CAACuC,MAAM,IAAI,CAAC,EAAE;UAChC,OAAO,QAAO;QAClB,CAAC,MAAM;UACH,OAAO,OAAM;QACjB;MACJ,CAAC,MAAM;QACH,IAAIC,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,EAAEQ,SAAS,CAACC,cAAc,EAAE;UACjF,MAAM/C,IAAI,GAAG6C,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,CAACQ,SAAS,CAAC9C,IAAG;UAChF,QAAQA,IAAI;YACR,KAAK,CAAC;cACF,OAAO,QAAQ;YACnB,KAAK,CAAC;cACF,OAAO,OAAO;YAClB;cACI,OAAO,OAAM;UACrB;QACJ,CAAC,MAAM,IAAI6C,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,EAAEQ,SAAS,CAACE,MAAM,EAAE;UAChF,OAAO,UAAU;QACrB;QACA,OAAO,OAAM;MACjB;IAGJ;;IAEA;IACA,MAAMC,QAAQ,GAAIZ,WAAW,IAAK;MAC9B,IAAIA,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACP,IAAI,EAAE;QACjD,OAAOR,YAAY,CAACe,KAAK,CAAC6C,SAAQ;MACtC,CAAC,MAAM,IAAIb,WAAW,CAACC,QAAQ,IAAIhD,YAAY,CAACe,KAAK,CAACN,EAAE,EAAE;QACtD,OAAOT,YAAY,CAACe,KAAK,CAAC8C,OAAM;MACpC,CAAC,MAAM;QACH,IAAIN,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,EAAEQ,SAAS,CAACC,cAAc,EAAE;UACjF,MAAM/C,IAAI,GAAG6C,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,CAACQ,SAAS,CAAC9C,IAAG;UAChF,QAAQA,IAAI;YACR,KAAK,CAAC;cACF,OAAO,KAAK;YAChB,KAAK,CAAC;cACF,OAAO6C,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,CAACQ,SAAS,CAACM,QAAQ,GAAG,IAAIP,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,CAACQ,SAAS,CAACM,QAAQ,EAAE,GAAG,OAAO;YAC9K;cACI,OAAO,EAAC;UAChB;QACJ,CAAC,MAAM,IAAIP,yBAAyB,CAACxC,KAAK,CAACgC,WAAW,CAACC,QAAQ,CAAC,EAAEQ,SAAS,CAACE,MAAM,EAAE;UAChF,OAAO,KAAK;QAChB;QACA,OAAO,EAAC;MACZ;IAEJ;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,MAAMK,iBAAiB,GAAGjF,QAAQ,CAAC,MAAM;MACrC;MACA,MAAMkF,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;MAC1BhE,kBAAkB,CAACc,KAAK,CAACa,OAAO,CAACC,OAAO,IAAI;QACxC,MAAMqC,QAAQ,GAAGrC,OAAO,CAACmB,QAAO;QAChC,IAAIgB,QAAQ,CAAChD,GAAG,CAACkD,QAAQ,CAAC,EAAE;QAC5B,IAAIA,QAAQ,IAAIlE,YAAY,CAACe,KAAK,CAACP,IAAI,EAAE;UACrCwD,QAAQ,CAACG,GAAG,CAACD,QAAQ,EAAElE,YAAY,CAACe,KAAK,CAACmD,QAAQ;QACtD,CAAC,MAAM,IAAIA,QAAQ,IAAIlE,YAAY,CAACe,KAAK,CAACN,EAAE,EAAE;UAC1CuD,QAAQ,CAACG,GAAG,CAACD,QAAQ,EAAElE,YAAY,CAACe,KAAK,CAACqD,MAAM;QACpD,CAAC,MAAM;UACHJ,QAAQ,CAACG,GAAG,CAACD,QAAQ,EACjBX,yBAAyB,CAACxC,KAAK,CAACmD,QAAQ,CAAC,EAAEV,SAAS,CAACa,IAAI,KAAKC,iBAAiB,CAACJ,QAAQ,CAAC,GAAG,QAAQ,GAAG,MAAM,CACjH;QACJ;MACJ,CAAC,CAAC;MACF,OAAOF,QAAQ;IACnB,CAAC;;IAED;IACA,MAAMM,iBAAiB,GAAItB,QAAQ,IAAK;MACpC,IAAI,CAACuB,mBAAmB,CAACxD,KAAK,CAACiC,QAAQ,CAAC,IAAI,CAACwB,gBAAgB,CAACzD,KAAK,CAACC,GAAG,CAACgC,QAAQ,CAAC,EAAE;QAC/EyB,uBAAuB,CAACzB,QAAQ,CAAC;QACjC,OAAO,IAAI;MACf;MACA,OAAO,KAAK;IAChB;;IAGA;IACA,MAAMuB,mBAAmB,GAAG9F,GAAG,CAAC,CAAC,CAAC,CAAC;IACnC,MAAM8E,yBAAyB,GAAG9E,GAAG,CAAC,CAAC,CAAC,CAAC;IACzC;IACA,MAAM+F,gBAAgB,GAAG/F,GAAG,CAAC,IAAI2B,GAAG,CAAC,CAAC,CAAC;;IAEvC;IACA,MAAMqE,uBAAuB,GAAIC,WAAW,IAAK;MAC7C;MACA,IAAIF,gBAAgB,CAACzD,KAAK,CAACC,GAAG,CAAC0D,WAAW,CAAC,IAAInB,yBAAyB,CAACxC,KAAK,CAAC2D,WAAW,CAAC,EAAE;QACzF;MACJ;;MAGA;MACAF,gBAAgB,CAACzD,KAAK,CAACsB,GAAG,CAACqC,WAAW,CAAC;;MAEvC;MACA,MAAMzD,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACtD9B,aAAa,CAACsF,GAAG,CAAC,yCAAyC,EAAE;QACzDvD,MAAM,EAAE;UAAEsD;QAAY,CAAC;QACvBpD,OAAO,EAAE;UAAEC,aAAa,EAAE,SAAS,GAAGN;QAAU;MACpD,CAAC,CAAC,CAACO,IAAI,CAACC,GAAG,IAAI;QACX,IAAIA,GAAG,CAACE,IAAI,CAACiD,IAAI,KAAK,CAAC,EAAE;UACrB,MAAMjD,IAAI,GAAGF,GAAG,CAACE,IAAI,CAACA,IAAI;UAC1B4C,mBAAmB,CAACxD,KAAK,CAAC2D,WAAW,CAAC,GAAG/C,IAAI;UAC7C4B,yBAAyB,CAACxC,KAAK,CAAC2D,WAAW,CAAC,GAAG/C,IAAI;QACvD;MACJ,CAAC,CAAC,CAACW,KAAK,CAACC,KAAK,IAAI;QACd1B,OAAO,CAAC0B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnCgC,mBAAmB,CAACxD,KAAK,CAAC2D,WAAW,CAAC,GAAG;UAAElB,SAAS,EAAE;YAAEa,IAAI,EAAE;UAAO;QAAE,CAAC;QACxEd,yBAAyB,CAACxC,KAAK,CAAC2D,WAAW,CAAC,GAAG;UAAElB,SAAS,EAAE;YAAEa,IAAI,EAAE;UAAO;QAAE,CAAC;MAClF,CAAC,CAAC,CAAC7B,OAAO;MACN;MACAgC,gBAAgB,CAACzD,KAAK,CAAC8D,MAAM,CAACH,WAAW,CAE7C,CAAC;IAEL;;IAEA;IACA,MAAMI,WAAW,GAAIC,SAAS,IAAK;MAC/B,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;;MAEzB;MACA,MAAMC,EAAE,GAAGD,SAAS,CAACE,QAAQ,CAAC,CAAC,CAAClD,MAAM,KAAK,EAAE,GAAGgD,SAAS,GAAG,IAAI,GAAGA,SAAS;MAE5E,MAAMG,IAAI,GAAG,IAAIC,IAAI,CAACH,EAAE,CAAC;;MAEzB;MACA,MAAMI,IAAI,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;MAC/B,MAAMC,KAAK,GAAG,CAACJ,IAAI,CAACK,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAEN,QAAQ,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC/D,MAAMC,GAAG,GAAGP,IAAI,CAACQ,OAAO,CAAC,CAAC,CAACT,QAAQ,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACtD,MAAMG,KAAK,GAAGT,IAAI,CAACU,QAAQ,CAAC,CAAC,CAACX,QAAQ,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MACzD,MAAMK,OAAO,GAAGX,IAAI,CAACY,UAAU,CAAC,CAAC,CAACb,QAAQ,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;MAC7D,MAAMO,OAAO,GAAGb,IAAI,CAACc,UAAU,CAAC,CAAC,CAACf,QAAQ,CAAC,CAAC,CAACO,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;;MAE7D;MACA,OAAO,GAAGJ,IAAI,IAAIE,KAAK,IAAIG,GAAG,IAAIE,KAAK,IAAIE,OAAO,IAAIE,OAAO,EAAE;IACnE;;IAEA;IACA,MAAME,QAAQ,GAAGxH,GAAG,CAAC,CAAC,EAAE,CAAC;IACzB,MAAMyH,cAAc,GAAGzH,GAAG,CAAC,CAAC,EAAE,CAAC;IAC/B,MAAM0H,WAAW,GAAG1H,GAAG,CAAC,IAAI;IAC5B,MAAM2H,cAAc,GAAG3H,GAAG,CAAC,EAAE;;IAG7B;IACA,MAAM4H,iBAAiB,GAAG5H,GAAG,CAAC,KAAK;;IAEnC;IACA,MAAM6H,WAAW,GAAIC,OAAO,IAAK;MAC7B,MAAMC,OAAO,GAAG;QACZ;QACA,KAAK,EAAEpD,OAAO,CAAC,0BAA0B,CAAC;QAC1C,MAAM,EAAEA,OAAO,CAAC,0BAA0B,CAAC;QAC3C,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC;QACA,KAAK,EAAEA,OAAO,CAAC,0BAA0B,CAAC;QAC1C,MAAM,EAAEA,OAAO,CAAC,0BAA0B,CAAC;QAC3C;QACA,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,MAAM,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QAC1C;QACA,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,IAAI,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACxC;QACA,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,MAAM,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QAC1C,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC;QACA,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC;QACA,KAAK,EAAEA,OAAO,CAAC,yBAAyB,CAAC;QACzC,KAAK,EAAEA,OAAO,CAAC,yBAAyB;MAC5C;;MAGA;MACA,OAAOoD,OAAO,CAACD,OAAO,CAAC,IAAInD,OAAO,CAAC,6BAA6B;IACpE;;IAEA;IACA,MAAMqD,cAAc,GAAIC,IAAI,IAAK;MAC7B,IAAIA,IAAI,GAAG,IAAI,EAAE;QACb,OAAOA,IAAI,GAAG,GAAE;MACpB,CAAC,MAAM,IAAIA,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;QAC3B,OAAO,CAACA,IAAI,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAG;MACzC,CAAC,MAAM,IAAID,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,IAAI,EAAE;QAClC,OAAO,CAACA,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAG;MAClD,CAAC,MAAM;QACH,OAAO,CAACD,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,IAAG;MACzD;IACJ;IAEA,MAAMC,cAAc,GAAI/E,OAAO,IAAK;MAChC;;MAGA,MAAMgF,SAAS,GAAGhF,OAAO,CAACgF,SAAQ;MAClC;MACA,MAAMC,GAAG,GAAGb,QAAQ,CAAClF,KAAK,CAAC8F,SAAS;MACpC,IAAIC,GAAG,EAAE;QACLV,cAAc,CAACrF,KAAK,GAAG+F,GAAE;QACzBX,WAAW,CAACpF,KAAK,GAAGc,OAAM;QAC1BwE,iBAAiB,CAACtF,KAAK,GAAG,IAAG;MACjC;IACJ;IAEA,MAAMgG,oBAAoB,GAAGtI,GAAG,CAAC,KAAK;;IAEtC;IACA,MAAMuI,WAAW,GAAGA,CAAA,KAAM;MACtB,IAAIZ,cAAc,CAACrF,KAAK,IAAIoF,WAAW,CAACpF,KAAK,EAAE;QAC3C,MAAMkG,OAAO,GAAGd,WAAW,CAACpF,KAAK,CAACwF,OAAO,EAAEW,WAAW,CAAC;QACvD,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,CAACC,QAAQ,CAACF,OAAO,CAAC,EAAE;UAC3CF,oBAAoB,CAAChG,KAAK,GAAG,IAAG;UAChCsF,iBAAiB,CAACtF,KAAK,GAAG,KAAI;QAClC;MACJ;IACJ;;IAEA;IACA,MAAMqG,kBAAkB,GAAGA,CAAA,KAAM;MAC7BvG,OAAO,CAACC,GAAG,CAAC,UAAU;IAC1B;IAEA,MAAMuG,mBAAmB,GAAGA,CAAA,KAAM;MAC9BxG,OAAO,CAACC,GAAG,CAAC,WAAW;IAC3B;IAEA,MAAMwG,iBAAiB,GAAGA,CAAA,KAAM;MAC5BzG,OAAO,CAACC,GAAG,CAAC,SAAS;IACzB;IAEA,MAAMyG,kBAAkB,GAAIhF,KAAK,IAAK;MAClC1B,OAAO,CAAC0B,KAAK,CAAC,OAAO,EAAEA,KAAK;MAC5BjD,SAAS,CAACiD,KAAK,CAAC,iBAAiB;MACjCwE,oBAAoB,CAAChG,KAAK,GAAG,KAAI;IACrC;;IAEA;IACA,MAAMyG,YAAY,GAAGA,CAAA,KAAM;MACvB,IAAIpB,cAAc,CAACrF,KAAK,IAAIoF,WAAW,CAACpF,KAAK,EAAE;QAC3C,MAAM0G,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG;QACvCF,IAAI,CAACG,IAAI,GAAGxB,cAAc,CAACrF,KAAI;QAC/B0G,IAAI,CAACI,QAAQ,GAAG1B,WAAW,CAACpF,KAAK,CAAC+G,QAAO;QACzCJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,IAAI;QAC9BA,IAAI,CAACQ,KAAK,CAAC;QACXP,QAAQ,CAACK,IAAI,CAACG,WAAW,CAACT,IAAI;MAClC;MACApB,iBAAiB,CAACtF,KAAK,GAAG,KAAI;IAClC;;IAGA;IACA;IACA,MAAMoH,SAAS,GAAG1J,GAAG,CAAC,CAAC,EAAE,CAAC;IAC1B,MAAM2J,eAAe,GAAG3J,GAAG,CAAC,CAAC,EAAE,CAAC;;IAEhC;IACA,MAAM4J,WAAW,GAAG5J,GAAG,CAAC,CAAC,CAAC;IAC1B,MAAM6J,mBAAmB,GAAG7J,GAAG,CAAC,IAAI;IAIpC,MAAM8J,IAAI,GAAIzB,GAAG,IAAK;MAClBjG,OAAO,CAACC,GAAG,CAAC,MAAM,EAACgG,GAAG;IAC1B;;IAYA;IACA;IACA,MAAM0B,SAAS,GAAG/J,GAAG,CAAC,CAAC,EAAE,CAAC;IAC1B,MAAMgK,eAAe,GAAGhK,GAAG,CAAC,CAAC,EAAE,CAAC;IAEhC,MAAMiK,WAAW,GAAI7G,OAAO,IAAK;MAC7B,MAAMgF,SAAS,GAAG8B,aAAa,CAAC9G,OAAO;MACvC,OAAO2G,SAAS,CAACzH,KAAK,CAAC8F,SAAS;IAEpC;;IAEA;IACA,MAAM+B,SAAS,GAAGnK,GAAG,CAAC,CAAC,EAAE,CAAC;IAC1B,MAAMoK,eAAe,GAAGpK,GAAG,CAAC,CAAC,EAAE,CAAC;;IAEhC;IACA,MAAMqK,SAAS,GAAGrK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE;IAC5B,MAAMsK,WAAW,GAAGtK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B;IACA,MAAMuK,WAAW,GAAGA,CAACC,KAAK,EAAEC,EAAE,KAAK;MAC/B,IAAIA,EAAE,EAAE;QACJJ,SAAS,CAAC/H,KAAK,CAACkI,KAAK,CAAC,GAAGC,EAAE;MAC/B;IACJ,CAAC;;IAED;IACA,MAAMC,eAAe,GAAItH,OAAO,IAAK;MACjC,IAAI;QACA,MAAMuH,YAAY,GAAGN,SAAS,CAAC/H,KAAK,CAACc,OAAO,CAACoH,KAAK,CAAC;QACnD,IAAI,CAACG,YAAY,EAAE;;QAEnB;QACAC,MAAM,CAACC,OAAO,CAACR,SAAS,CAAC/H,KAAK,CAAC,CAACa,OAAO,CAAC,CAAC,CAACqH,KAAK,EAAEM,KAAK,CAAC,KAAK;UACxD,IAAIN,KAAK,KAAKpH,OAAO,CAACoH,KAAK,IAAI,CAACM,KAAK,CAACC,MAAM,EAAE;YAC1CD,KAAK,CAACE,KAAK,CAAC,CAAC;UACjB;QACJ,CAAC,CAAC;;QAEF;QACAV,WAAW,CAAChI,KAAK,CAACc,OAAO,CAACoH,KAAK,CAAC,GAAG,SAAS;QAC5CpI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEe,OAAO,CAACoH,KAAK,CAAC;MACzC,CAAC,CAAC,OAAO1G,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACrC;IACJ,CAAC;;IAED;IACA,MAAMmH,gBAAgB,GAAI7H,OAAO,IAAK;MAClC,IAAI;QACAkH,WAAW,CAAChI,KAAK,CAACc,OAAO,CAACoH,KAAK,CAAC,GAAG,QAAQ;QAC3CpI,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEe,OAAO,CAACoH,KAAK,CAAC;MACvC,CAAC,CAAC,OAAO1G,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACrC;IACJ,CAAC;;IAED;IACA,MAAMoH,gBAAgB,GAAI9H,OAAO,IAAK;MAClC,IAAI;QACAkH,WAAW,CAAChI,KAAK,CAACc,OAAO,CAACoH,KAAK,CAAC,GAAG,OAAO;QAC1CpI,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEe,OAAO,CAACoH,KAAK,CAAC;MACzC,CAAC,CAAC,OAAO1G,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACrC;IACJ,CAAC;;IAED;IACA,MAAMqH,gBAAgB,GAAI/H,OAAO,IAAK;MAClC,IAAI;QACA,MAAM0H,KAAK,GAAGT,SAAS,CAAC/H,KAAK,CAACc,OAAO,EAAEoH,KAAK,CAAC;QAC7C,IAAI,CAACM,KAAK,EAAE;QAEZ1I,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEgH,KAAK,CAAChH,KAAK,CAAC;QACrCwG,WAAW,CAAChI,KAAK,CAACc,OAAO,EAAEoH,KAAK,CAAC,GAAG,OAAO;;QAE3C;QACA,MAAMpC,SAAS,GAAG8B,aAAa,CAAC9G,OAAO,CAAC;QACxC,IAAIgH,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC,EAAE;UAClChG,OAAO,CAACC,GAAG,CAAC,aAAa,CAAC;UAC1ByI,KAAK,CAACM,GAAG,GAAGhB,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC;UAC5C0C,KAAK,CAACO,IAAI,CAAC,CAAC;QAChB;;QAEA;MACJ,CAAC,CAAC,OAAOvH,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACrC;IACJ,CAAC;;IAED;IACA,MAAMwH,WAAW,GAAIlI,OAAO,IAAK;MAC7BhB,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEe,OAAO;MAC9B,IAAI;QACA,MAAMgF,SAAS,GAAG8B,aAAa,CAAC9G,OAAO,CAAC;QACxC,MAAMiF,GAAG,GAAG8B,SAAS,CAAC7H,KAAK,CAAC8F,SAAS,CAAC;;QAEtC;QACA,IAAI,CAACC,GAAG,IAAI+B,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC,EAAE;UAC1C,OAAOgC,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC;QAC3C;QAEA,OAAOC,GAAG,IAAI,EAAE;MACpB,CAAC,CAAC,OAAOvE,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC,OAAO,EAAE;MACb;IACJ,CAAC;;IAED;IACA1D,WAAW,CAAC,MAAM;MACd;MACAwK,MAAM,CAACW,MAAM,CAACpB,SAAS,CAAC7H,KAAK,CAAC,CAACa,OAAO,CAACkF,GAAG,IAAI;QAC1C,IAAIA,GAAG,IAAIA,GAAG,CAACmD,UAAU,CAAC,OAAO,CAAC,EAAE;UAChCC,GAAG,CAACC,eAAe,CAACrD,GAAG,CAAC;QAC5B;MACJ,CAAC,CAAC;;MAEF;MACAuC,MAAM,CAACW,MAAM,CAAC7B,SAAS,CAACpH,KAAK,CAAC,CAACa,OAAO,CAACkF,GAAG,IAAI;QAC1C,IAAIA,GAAG,IAAIA,GAAG,CAACmD,UAAU,CAAC,OAAO,CAAC,EAAE;UAChCC,GAAG,CAACC,eAAe,CAACrD,GAAG,CAAC;QAC5B;MACJ,CAAC,CAAC;;MAEF;MACAuC,MAAM,CAACW,MAAM,CAACxB,SAAS,CAACzH,KAAK,CAAC,CAACa,OAAO,CAACkF,GAAG,IAAI;QAC1C,IAAIA,GAAG,IAAIA,GAAG,CAACmD,UAAU,CAAC,OAAO,CAAC,EAAE;UAChCC,GAAG,CAACC,eAAe,CAACrD,GAAG,CAAC;QAC5B;MACJ,CAAC,CAAC;;MAEF;MACAiC,WAAW,CAAChI,KAAK,GAAG,CAAC,CAAC;MACtB+H,SAAS,CAAC/H,KAAK,GAAG,CAAC,CAAC;;MAEpB;MACAsH,WAAW,CAACtH,KAAK,GAAG,CAAC,CAAC;MACtBuH,mBAAmB,CAACvH,KAAK,GAAG,IAAI;IACpC,CAAC,CAAC;;IAEF;IACA,MAAMqJ,aAAa,GAAIC,EAAE,IAAK;MAC1B,IAAIA,EAAE,IAAIA,EAAE,CAACtI,MAAM,GAAG,EAAE,EAAE;QACtB,OAAOsI,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAI;MACjC;MACA,OAAOD,EAAC;IACZ;;IAEA;IACA;IACA,MAAME,eAAe,GAAIC,QAAQ,IAAK;MAClC,IAAIA,QAAQ,EAAE;QACVC,MAAM,CAACC,IAAI,CAACF,QAAQ,EAAE,QAAQ,CAAC;MACnC;IACJ,CAAC;;IAED;IACA,MAAMG,WAAW,GAAI7D,GAAG,IAAK;MACzB,IAAI,CAACA,GAAG,EAAE,OAAO,EAAE;MACnB,MAAM8D,OAAO,GAAG,IAAIV,GAAG,CAACpD,GAAG,CAAC;MAC5B,IAAI+D,UAAU,GAAGD,OAAO,CAACE,IAAI;MAC7B,MAAMC,IAAI,GAAGH,OAAO,CAACI,QAAQ;MAE7B,IAAID,IAAI,CAAChJ,MAAM,GAAG,EAAE,EAAE;QAClB8I,UAAU,IAAIE,IAAI,CAACE,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;MAC/C,CAAC,MAAM;QACHJ,UAAU,IAAIE,IAAI;MACtB;MAEA,OAAOF,UAAU;IACrB,CAAC;;IAID;IACA;IACA;IACA,MAAMK,YAAY,GAAGzM,GAAG,CAAC,KAAK;IAE9B,MAAM0M,UAAU,GAAG1M,GAAG,CAAC,CAAC,CAAC,EAAC;;IAE1B;IACA,MAAM2M,mBAAmB,GAAGA,CAAA,KAAM;MAC9B,IAAI,CAACC,cAAc,CAACtK,KAAK,EAAE;MAC3B,IAAIsK,cAAc,CAACtK,KAAK,EAAE;QACtB,MAAMuK,YAAY,GAAGD,cAAc,CAACtK,KAAK,CAACuK,YAAY;QACtD,MAAMC,YAAY,GAAGF,cAAc,CAACtK,KAAK,CAACwK,YAAY;QACtDJ,UAAU,CAACpK,KAAK,GAAGuK,YAAY,GAAGC,YAAY;MAClD;IACJ;IAEA,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAIN,YAAY,CAACnK,KAAK,EAAE,OAAO,CAAC;MAChCqK,mBAAmB,CAAC,CAAC;MAErB,IAAI;QACAF,YAAY,CAACnK,KAAK,GAAG,IAAI;QACzB,MAAME,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QACtDyB,WAAW,CAAC7B,KAAK,GAAG;QACpBhB,UAAU,CAACgB,KAAK,GAAG,EAAC;;QAEpB;QACA,MAAM0K,QAAQ,GAAG,MAAMpM,aAAa,CAACgC,IAAI,CAAC,+BAA+B,EAAE;UACvEqK,GAAG,EAAE,CAAC;UACN9K,KAAK,EAAE,GAAG;UACV+K,KAAK,EAAE,EAAE;UACTC,QAAQ,EAAE,EAAE;UACZC,OAAO,EAAE,EAAE;UACXnL,IAAI,EAAE;QACV,CAAC,EAAE;UACCY,OAAO,EAAE;YAAEC,aAAa,EAAE,SAAS,GAAGN;UAAU;QACpD,CAAC,CAAC;QAGF,IAAIwK,QAAQ,CAAC9J,IAAI,CAACiD,IAAI,KAAK,CAAC,EAAE;UAC1BtF,SAAS,CAACiD,KAAK,CAACkJ,QAAQ,CAAC9J,IAAI,CAACmK,GAAG,IAAI,MAAM,CAAC;UAC5C;QACJ;QAEAxM,SAAS,CAACyM,OAAO,CAAC,MAAM,CAAC;MAE7B,CAAC,CAAC,OAAOxJ,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;QAC7BjD,SAAS,CAACiD,KAAK,CAAC,kBAAkB,CAAC;MACvC,CAAC,SAAS;QACNxC,UAAU,CAACgB,KAAK,GAAG,EAAC;QACpBR,qBAAqB,CAACP,YAAY,CAACe,KAAK,CAACP,IAAI,EAAER,YAAY,CAACe,KAAK,CAACN,EAAE,EAAET,YAAY,CAACe,KAAK,CAACL,IAAI,EAAE,EAAE,EAAEV,YAAY,CAACe,KAAK,CAACJ,IAAI,EAAEX,YAAY,CAACe,KAAK,CAACH,KAAK;QACpJ;QACA,MAAM,IAAIoL,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;QACvDf,YAAY,CAACnK,KAAK,GAAG,KAAK;MAC9B;IACJ;;IAEA;IACA;IACA;IACA,MAAMoL,iBAAiB,GAAItK,OAAO,IAAK;MACnC,IAAIA,OAAO,CAACuK,OAAO,KAAK,OAAO,IAAIvK,OAAO,CAACuK,OAAO,KAAK,UAAU,EAAE;QAC/D,OAAOvK,OAAO,CAACuK,OAAM;MACzB,OACK;QACD,OAAOtJ,kBAAkB,CAACjB,OAAO;MACrC;IACJ;;IAGA;IACA,MAAMwK,wBAAwB,GAAIxK,OAAO,IAAK;MAC1C,IAAIA,OAAO,CAACC,OAAO,IAAI,QAAQ,EAAE;QAC7B;QACA,IAAI,CAACD,OAAO,CAACyK,OAAO,EAAE;UAClBzL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C,OAAO,GAAG;QACd;QACA,MAAMyL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC5K,OAAO,CAACyK,OAAO,CAAC;QAC3C,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACH,OAAO,EAAE;UAC9BvL,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C,OAAO,GAAG;QACd;;QAEA;QACA,IAAI4L,gBAAgB,CAAC3L,KAAK,CAACwL,OAAO,CAACtD,KAAK,CAAC,EAAE;UACvC,OAAOyD,gBAAgB,CAAC3L,KAAK,CAACwL,OAAO,CAACtD,KAAK,CAAC,CAACnH,OAAO;QACxD;QAEA,OAAO,MAAM,CAAC,CAAC;MACnB,CAAC,MAAM,IAAID,OAAO,CAACC,OAAO,KAAK,SAAS,EAAE;QACtC,OAAO,OAAM;MACjB,CAAC,MAAM,IAAID,OAAO,CAACC,OAAO,KAAK,OAAO,EAAE;QACpC,OAAO,OAAM;MACjB,OACK;QACD,OAAOD,OAAO,CAACC,OAAM;MACzB;IACJ;;IAEA;IACA,MAAM6K,cAAc,GAAGlO,GAAG,CAAC,EAAE;IAC7B,MAAMmO,oBAAoB,GAAI/K,OAAO,IAAK;MACtC,MAAMoH,KAAK,GAAGpH,OAAO,CAACoH,KAAI;MAC1B,IAAI;QACA;QACA,IAAIpH,OAAO,CAACC,OAAO,KAAK,QAAQ,IAAI+K,UAAU,CAAC9L,KAAK,CAACkI,KAAK,CAAC,EAAE;UACzDpI,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;UACvBe,OAAO,GAAGgL,UAAU,CAAC9L,KAAK,CAACkI,KAAK,CAAC;QACrC;;QAEA;QACA,IAAI,CAACpH,OAAO,CAACyK,OAAO,EAAE;UAClBzL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;UAC9C;QACJ;;QAEA;QACA,MAAMyL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC5K,OAAO,CAACyK,OAAO,CAAC;;QAE3C;;QAEA;QACA,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACH,OAAO,EAAE;UAC9BvL,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;UAC/C;QACJ;;QAEA;QACA,QAAQyL,OAAO,CAACH,OAAO;UACnB,KAAK,MAAM;YACP;YACAO,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAACO,IAAI;YAC1C;YACA;UACJ,KAAK,MAAM;YACP;YACAH,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAACQ,IAAI;YAC1C;UACJ,KAAK,OAAO;UACZ,KAAK,SAAS;YACV;UACJ,KAAK,OAAO;YACR;YACAJ,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAACS,KAAK;YAC3C;UACJ,KAAK,UAAU;YACX;YACAL,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAACU,QAAQ;YAC9C;UACJ,KAAK,OAAO;YACR;YACAN,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAAChD,KAAK;YAC3C1I,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyL,OAAO,CAAChD,KAAK,CAAC;YAC9C;UACJ,KAAK,OAAO;YACR;YACAoD,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAACW,KAAK;YAC3CrM,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyL,OAAO,CAACW,KAAK,CAAC;YAC9C;UACJ,KAAK,MAAM;YACP;YACAP,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAACY,IAAI;YAC1C;UACJ,KAAK,MAAM;YACP;YACAR,cAAc,CAAC5L,KAAK,CAACkI,KAAK,CAAC,GAAGsD,OAAO,CAAC9E,IAAI;YAC1C;UACJ;YACI;QACR;MACJ,CAAC,CAAC,OAAOlF,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvC;MACJ;IACJ;;IAEA;;IAEA;IACA,MAAMsK,UAAU,GAAGpO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5B,MAAMiO,gBAAgB,GAAGjO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAC;;IAEjC;IACA,MAAM2O,kBAAkB,GAAIvL,OAAO,IAAK;MACpC;MACA,IAAI,CAACA,OAAO,CAACyK,OAAO,EAAE;MAEtB,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC5K,OAAO,CAACyK,OAAO,CAAC;MAC3C,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACH,OAAO,IAAI,CAACG,OAAO,CAACc,MAAM,EAAE;MAErD,MAAMC,SAAS,GAAGf,OAAO,CAACc,MAAM,CAACC,SAAS,CAAC;MAC3C,MAAMrE,KAAK,GAAGsD,OAAO,CAACtD,KAAK,CAAC;MAC5B;;MAEA;MACA,IAAIyD,gBAAgB,CAAC3L,KAAK,CAACkI,KAAK,CAAC,EAAE;QAC/B4D,UAAU,CAAC9L,KAAK,CAACkI,KAAK,CAAC,GAAGyD,gBAAgB,CAAC3L,KAAK,CAACkI,KAAK,CAAC;QACvD;MACJ;MAEA,MAAMhI,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACtD9B,aAAa,CAACsF,GAAG,CAAC,gCAAgC,EAAE;QAChDvD,MAAM,EAAE;UAAEkM;QAAU,CAAC;QACrBhM,OAAO,EAAE;UAAEC,aAAa,EAAE,SAAS,GAAGN;QAAU;MACpD,CAAC,CAAC,CAACO,IAAI,CAACC,GAAG,IAAI;QACX;QACA,IAAIA,GAAG,CAACE,IAAI,CAACiD,IAAI,KAAK,CAAC,EAAE;UAErB8H,gBAAgB,CAAC3L,KAAK,CAACkI,KAAK,CAAC,GAAGxH,GAAG,CAACE,IAAI,CAACA,IAAI;UAC7CkL,UAAU,CAAC9L,KAAK,CAACkI,KAAK,CAAC,GAAGxH,GAAG,CAACE,IAAI,CAACA,IAAI;UACvCd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmI,KAAK;UACtC2D,oBAAoB,CAAC/K,OAAO;QAChC,CAAC,MAAM;UACHvC,SAAS,CAACiD,KAAK,CAACd,GAAG,CAACE,IAAI,CAACmK,GAAG,IAAI,UAAU,CAAC;QAC/C;MACJ,CAAC,CAAC,CAACxJ,KAAK,CAACC,KAAK,IAAI;QACd1B,OAAO,CAACC,GAAG,CAACyB,KAAK;MACrB,CAAC,CAAC,CAACC,OAAO,CACV,CAAC;IAEL;;IAGA;;IAEA;IACA,MAAMmG,aAAa,GAAI9G,OAAO,IAAK;MAC/BhB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,OAAO,CAAC;MAC3C,IAAI,CAACA,OAAO,EAAEyK,OAAO,EAAE;QACnBzL,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;QAClC;MACJ;MAEA,MAAMyL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC5K,OAAO,CAACyK,OAAO,CAAC;MAC3CzL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyL,OAAO,CAAC;MACrC,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACH,OAAO,EAAE;QAC9BvL,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;QAC5C;MACJ;MAEA,IAAI+F,SAAS,GAAG,EAAE;;MAElB;MACA,IAAI0F,OAAO,CAACH,OAAO,KAAK,OAAO,IAAIG,OAAO,CAACgB,KAAK,IAAIhB,OAAO,CAACgB,KAAK,CAAC1G,SAAS,EAAE;QACzEA,SAAS,GAAG0F,OAAO,CAACgB,KAAK,CAAC1G,SAAS;QACnChG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,SAAS,CAAC;MAC7C,CAAC,MAAM,IAAI0F,OAAO,CAACH,OAAO,KAAK,SAAS,IAAIG,OAAO,CAACiB,OAAO,IAAIjB,OAAO,CAACiB,OAAO,CAAC3G,SAAS,EAAE;QACtFA,SAAS,GAAG0F,OAAO,CAACiB,OAAO,CAAC3G,SAAS;QACrChG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,SAAS,CAAC;MAC7C,CAAC,MAAM,IAAI0F,OAAO,CAACH,OAAO,KAAK,OAAO,EAAE;QACpCvF,SAAS,GAAG0F,OAAO,CAACW,KAAK,CAACrG,SAAS;QACnChG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,SAAS,EAAE,gBAAgB,EAAE0F,OAAO,CAACW,KAAK,CAAC;MAC9E,CAAC,MAAM,IAAIX,OAAO,CAACH,OAAO,KAAK,OAAO,EAAE;QACpCvF,SAAS,GAAG0F,OAAO,CAAChD,KAAK,CAAC1C,SAAS;QACnChG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,SAAS,CAAC;MAC7C,CAAC,MAAM,IAAI0F,OAAO,CAACH,OAAO,KAAK,MAAM,EAAE;QACnCvF,SAAS,GAAG0F,OAAO,CAACQ,IAAI,CAAClG,SAAS;QAClChG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,SAAS,CAAC;MAC7C,CAAC,MAAM;QACHhG,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEyL,OAAO,CAACH,OAAO,CAAC;QAC1D,OAAO,EAAE;MACb;MAEAvL,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE+F,SAAS,CAAC;MAC1C,OAAOA,SAAQ;IACnB;;IAGA;IACA,MAAM4G,iBAAiB,GAAI5L,OAAO,IAAK;MACnChB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEe,OAAO,CAACC,OAAO,EAAED,OAAO,CAAC;MAClE,MAAMgF,SAAS,GAAG8B,aAAa,CAAC9G,OAAO;MACvChB,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE+F,SAAS,CAAC;;MAEzC;MACA,IAAIhF,OAAO,CAACC,OAAO,KAAK,OAAO,IAAID,OAAO,CAACC,OAAO,KAAK,SAAS,EAAE;QAC9D,IAAI2G,eAAe,CAAC1H,KAAK,CAAC8F,SAAS,CAAC,EAAE;UAClChG,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE+F,SAAS,CAAC;UACvC2B,SAAS,CAACzH,KAAK,CAAC8F,SAAS,CAAC,GAAG4B,eAAe,CAAC1H,KAAK,CAAC8F,SAAS,CAAC;UAC7DxG,iBAAiB,CAACU,KAAK,EAAE;UACzB2M,mBAAmB,CAAC,CAAC;UACrB;QACJ;MACJ,CAAC,MAAM,IAAI7L,OAAO,CAACC,OAAO,KAAK,OAAO,EAAE;QACpCjB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE+F,SAAS,EAAE,UAAU,EAAE,CAAC,CAACuB,eAAe,CAACrH,KAAK,CAAC8F,SAAS,CAAC,CAAC;QAC3F,IAAIuB,eAAe,CAACrH,KAAK,CAAC8F,SAAS,CAAC,EAAE;UAClChG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+F,SAAS,CAAC;UACpCsB,SAAS,CAACpH,KAAK,CAAC8F,SAAS,CAAC,GAAGuB,eAAe,CAACrH,KAAK,CAAC8F,SAAS,CAAC;UAC7DxG,iBAAiB,CAACU,KAAK,EAAE;UACzB2M,mBAAmB,CAAC,CAAC;UACrB;QACJ;MACJ,CAAC,MAAM,IAAI7L,OAAO,CAACC,OAAO,KAAK,OAAO,EAAE;QACpC,IAAI+G,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC,EAAE;UAClChG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+F,SAAS,CAAC;UACpC+B,SAAS,CAAC7H,KAAK,CAAC8F,SAAS,CAAC,GAAGgC,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC;UAC7DxG,iBAAiB,CAACU,KAAK,EAAE;UACzB2M,mBAAmB,CAAC,CAAC;UACrB;QACJ;MACJ,CAAC,MAAM,IAAI7L,OAAO,CAACC,OAAO,KAAK,MAAM,EAAE;QACnC,IAAIoE,cAAc,CAACnF,KAAK,CAAC8F,SAAS,CAAC,EAAE;UACjChG,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+F,SAAS,CAAC;UACpCZ,QAAQ,CAAClF,KAAK,CAAC8F,SAAS,CAAC,GAAGX,cAAc,CAACnF,KAAK,CAAC8F,SAAS,CAAC;UAC3DxG,iBAAiB,CAACU,KAAK,EAAE;UACzB2M,mBAAmB,CAAC,CAAC;UACrB;QACJ;MACJ;MAEA,IAAI,CAAC7L,OAAO,CAACyK,OAAO,EAAE;QAClBzL,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QACzC;MACJ;MAEA,MAAMyL,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAC5K,OAAO,CAACyK,OAAO,CAAC;MAC3C,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACH,OAAO,EAAE;QAC9BvL,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;QAC3C;MACJ;MAEAD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEyL,OAAO,CAACH,OAAO,EAAE,YAAY,EAAEvF,SAAS,CAAC;MAE1E,IAAI;QAEA,MAAM5F,SAAS,GAAGC,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;QACtD9B,aAAa,CAACgC,IAAI,CAAC,qCAAqC,EAAE;UACtDwF,SAAS,EAAEA;QACf,CAAC,EAAE;UACCvF,OAAO,EAAE;YAAEC,aAAa,EAAE,SAAS,GAAGN;UAAU,CAAC;UACjD0M,YAAY,EAAE;QAClB,CAAC,CAAC,CAACnM,IAAI,CAAC,MAAMC,GAAG,IAAI;UACjB,IAAImM,IAAI,GAAG,IAAI;UACf,IAAI9G,GAAG,GAAG,IAAI;UACd,IAAIyF,OAAO,CAACH,OAAO,KAAK,OAAO,EAAE;YAC7BwB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACpM,GAAG,CAACE,IAAI,CAAC,EAAE;cAAEjB,IAAI,EAAE;YAAa,CAAC,CAAC;YACnDoG,GAAG,GAAGoD,GAAG,CAAC4D,eAAe,CAACF,IAAI,CAAC;;YAE/B;YACApF,SAAS,CAACzH,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAChC2B,eAAe,CAAC1H,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAEtCjG,OAAO,CAACC,GAAG,CAAC,SAAS;YACrB8L,oBAAoB,CAAC/K,OAAO;;YAE5B;UACJ,CAAC,MAAM,IAAI0K,OAAO,CAACH,OAAO,KAAK,SAAS,EAAE;YACtC;YACAwB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACpM,GAAG,CAACE,IAAI,CAAC,EAAE;cAAEjB,IAAI,EAAE;YAAY,CAAC,CAAC;YAClDoG,GAAG,GAAGoD,GAAG,CAAC4D,eAAe,CAACF,IAAI,CAAC;;YAE/B;YACApF,SAAS,CAACzH,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAChC2B,eAAe,CAAC1H,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAEtCjG,OAAO,CAACC,GAAG,CAAC,SAAS;YACrB8L,oBAAoB,CAAC/K,OAAO;;YAE5B;UACJ,CAAC,MAAM,IAAI0K,OAAO,CAACH,OAAO,KAAK,OAAO,EAAE;YACpC;YACA,MAAM2B,SAAS,GAAG,MAAMrO,eAAe,CAACsO,SAAS,CAACvM,GAAG,CAACE,IAAI,CAAC;YAC3D,MAAMsM,QAAQ,GAAG/D,GAAG,CAAC4D,eAAe,CAACC,SAAS,CAAC;YAC/C5F,SAAS,CAACpH,KAAK,CAAC8F,SAAS,CAAC,GAAGoH,QAAQ;YACrC7F,eAAe,CAACrH,KAAK,CAAC8F,SAAS,CAAC,GAAGoH,QAAQ;YAE3CpN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEmN,QAAQ;YACzCrB,oBAAoB,CAAC/K,OAAO;UAChC,CAAC,MAAM,IAAI0K,OAAO,CAACH,OAAO,KAAK,OAAO,EAAE;YACpCwB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACpM,GAAG,CAACE,IAAI,CAAC,EAAE;cAAEjB,IAAI,EAAE;YAAY,CAAC,CAAC;YAClDoG,GAAG,GAAGoD,GAAG,CAAC4D,eAAe,CAACF,IAAI,CAAC;YAC/BhF,SAAS,CAAC7H,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAChC+B,eAAe,CAAC9H,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAEtCjG,OAAO,CAACC,GAAG,CAAC,SAAS;YACrB8L,oBAAoB,CAAC/K,OAAO;UAChC,CAAC,MAAM,IAAI0K,OAAO,CAACH,OAAO,KAAK,MAAM,EAAE;YACnCwB,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACpM,GAAG,CAACE,IAAI,CAAC,EAAE;cAAEjB,IAAI,EAAE;YAA2B,CAAC,CAAC;YACjEoG,GAAG,GAAGoD,GAAG,CAAC4D,eAAe,CAACF,IAAI,CAAC;YAC/B3H,QAAQ,CAAClF,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAC/BZ,cAAc,CAACnF,KAAK,CAAC8F,SAAS,CAAC,GAAGC,GAAG;YAErCjG,OAAO,CAACC,GAAG,CAAC,SAAS;YACrB8L,oBAAoB,CAAC/K,OAAO;UAChC;;UAEA;UACA;UACA;QAEJ,CAAC,CAAC,CAACS,KAAK,CAACC,KAAK,IAAI;UACd1B,OAAO,CAACC,GAAG,CAACyB,KAAK,CAAC;UAClB;;UAEAmL,mBAAmB,CAAC,CAAC;UACrBpO,SAAS,CAACiD,KAAK,CAAC,oBAAoB,CAAC;QACzC,CAAC,CAAC;MACN,CAAC,CAAC,OAAOA,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QAClC;QACAlC,iBAAiB,CAACU,KAAK,EAAE;QACzB2M,mBAAmB,CAAC,CAAC;MACzB,CAAC,SAAS;QACN7M,OAAO,CAACC,GAAG,CAAC,eAAe;QAC3B4M,mBAAmB,CAAC,CAAC;QACrBrN,iBAAiB,CAACU,KAAK,EAAE;MAC7B;IACJ;;IAEA;IACA,MAAM2M,mBAAmB,GAAGA,CAAA,KAAM;MAC9B;MACA,IAAIrN,iBAAiB,CAACU,KAAK,KAAKT,eAAe,CAACS,KAAK,IAAIT,eAAe,CAACS,KAAK,GAAG,CAAC,EAAE;QAChF;QACAnC,QAAQ,CAAC,MAAM;UACXwD,cAAc,CAAC,CAAC;QACpB,CAAC,CAAC;MACN;IACJ;IAEA,MAAM8L,YAAY,GAAGzP,GAAG,CAAC,KAAK;IAC9B;IACAE,KAAK,CAACsB,kBAAkB,EAAE,MAAOyB,WAAW,IAAK;MAC7C,IAAI,CAACA,WAAW,EAAE;QACdwM,YAAY,CAACnN,KAAK,GAAG,IAAI;QACzB;MACJ;MAEA,MAAMoN,QAAQ,GAAGzM,WAAW,CAAC0M,GAAG,CAAC,MAAMvM,OAAO,IAAI;QAC9C,IAAIA,OAAO,CAACC,OAAO,KAAK,QAAQ,EAAE;UAC9BsL,kBAAkB,CAACvL,OAAO,CAAC;QAC/B,CAAC,MAAM,IAAIA,OAAO,CAACC,OAAO,KAAK,OAAO,IAAID,OAAO,CAACC,OAAO,KAAK,SAAS,IAAID,OAAO,CAACC,OAAO,KAAK,OAAO,IAAID,OAAO,CAACC,OAAO,KAAK,OAAO,IAAID,OAAO,CAACC,OAAO,KAAK,MAAM,EAAE;UACjK2L,iBAAiB,CAAC5L,OAAO,CAAC;QAC9B,CAAC,MAAM;UACH+K,oBAAoB,CAAC/K,OAAO,CAAC;QACjC;MAEJ,CAAC,CAAC;MAEF,IAAI;QACA,MAAMmK,OAAO,CAACqC,GAAG,CAACF,QAAQ,CAAC;MAE/B,CAAC,CAAC,OAAO5L,KAAK,EAAE;QACZ1B,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEyB,KAAK,CAAC;MAChC,CAAC,SAAS;QACN1B,OAAO,CAACC,GAAG,CAAC,yBAAyB;QACrCsB,cAAc,CAAC;QACf8L,YAAY,CAACnN,KAAK,GAAG,IAAI;MAC7B;IACJ,CAAC,EAAE;MAAEuN,SAAS,EAAE;IAAK,CAAC,CAAC;;IAKvB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;;IAEA;IACA;;IAWA;IACA,MAAMC,iBAAiB,GAAG9P,GAAG,CAAC,KAAK;IACnC,MAAM+P,iBAAiB,GAAG/P,GAAG,CAAC,IAAI;;IAElC;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;;IAEA;IACA,MAAMgQ,sBAAsB,GAAG/P,MAAM,CAAC,wBAAwB;IAC9D;IACA,MAAMgQ,qBAAqB,GAAGhQ,MAAM,CAAC,uBAAuB;IAC5D,MAAMiQ,cAAc,GAAGlQ,GAAG,CAAC;MACvBmQ,KAAK,EAAE,uBAAuB,CAAC;IACnC,CAAC;;IAED;IACA,MAAMC,oBAAoB,GAAIC,UAAU,IAAK;MACzC,IAAIJ,qBAAqB,CAAC3N,KAAK,IAAI,WAAW,EAAE;QAC5C+N,UAAU,GAAGA,UAAU,GAAG,EAAC;MAC/B;MACAH,cAAc,CAAC5N,KAAK,CAAC6N,KAAK,GAAG,gBAAgBE,UAAU,MAAK;MAC5D;IACJ;;IAEA;IACAnQ,KAAK,CAAC,MAAM8P,sBAAsB,CAAC1N,KAAK,EAAGgO,QAAQ,IAAK;MACpD,IAAID,UAAU,GAAG,IAAG;MACpB,IAAI,CAACC,QAAQ,EAAE;QACX;QACAD,UAAU,GAAG,EAAE,EAAE;QACjBD,oBAAoB,CAACC,UAAU;MACnC,CAAC,MAAM;QACH;QACAD,oBAAoB,CAACC,UAAU;MAEnC;IACJ,CAAC,EAAE;MAAER,SAAS,EAAE;IAAK,CAAC;;IAEtB;IACA3P,KAAK,CAAC,MAAM+P,qBAAqB,CAAC3N,KAAK,EAAE,MAAM;MAC3C,IAAI+N,UAAU,GAAG,IAAG;MACpB,IAAI,CAACL,sBAAsB,CAAC1N,KAAK,EAAE;QAC/B;QACA+N,UAAU,GAAG,EAAE,EAAE;QACjBD,oBAAoB,CAACC,UAAU;MACnC,CAAC,MAAM;QACH;QACAD,oBAAoB,CAACC,UAAU;MAEnC;IACJ,CAAC,EAAE;MAAER,SAAS,EAAE;IAAK,CAAC;;IAMtB;IACA,MAAMjD,cAAc,GAAG5M,GAAG,CAAC,IAAI;IAC/B;IACA,MAAMuQ,SAAS,GAAGvQ,GAAG,CAAC,KAAK;IAC3B;IACA,MAAMoE,OAAO,GAAGpE,GAAG,CAAC,IAAI;IACxB;IACA,MAAMmE,WAAW,GAAGnE,GAAG,CAAC,CAAC;IACzB;IAGA,MAAMwQ,aAAa,GAAGxQ,GAAG,CAAC,CAAC;;IAE3B;IACA,MAAMyQ,YAAY,GAAG,MAAOC,CAAC,IAAK;MAC9B,MAAM;QAAEC;MAAU,CAAC,GAAGD,CAAC,CAACE,MAAK;MAC7B;MACA,IAAID,SAAS,GAAGH,aAAa,CAAClO,KAAK,IAAIb,uBAAuB,CAACa,KAAK,GAAGf,YAAY,CAACe,KAAK,CAACH,KAAK,EAAE;QAC7F,IAAIiC,OAAO,CAAC9B,KAAK,EAAE;UACfzB,SAAS,CAACgQ,OAAO,CAAC,SAAS,CAAC;UAC5BzM,OAAO,CAAC9B,KAAK,GAAG,KAAI;QACxB;MACJ;MAEA,IAAIqO,SAAS,GAAGzP,iBAAiB,IAAI,CAACqP,SAAS,CAACjO,KAAK,IAAI8B,OAAO,CAAC9B,KAAK,EAAE;QACpE,MAAMwO,gBAAgB,CAAC;MAC3B;MACA;MACAN,aAAa,CAAClO,KAAK,GAAGqO,SAAQ;IAClC;;IAEA;IACA,MAAMG,gBAAgB,GAAG,MAAAA,CAAA,KAAY;MACjC,IAAIP,SAAS,CAACjO,KAAK,EAAE;QACjB;MACJ;MAEAiO,SAAS,CAACjO,KAAK,GAAG,IAAG;MACrB,IAAI;QACA6B,WAAW,CAAC7B,KAAK,IAAI;QACrB;;QAEA;QACA,MAAM,IAAIiL,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAEtD1L,qBAAqB,CAACP,YAAY,CAACe,KAAK,CAACP,IAAI,EAAER,YAAY,CAACe,KAAK,CAACN,EAAE,EAAET,YAAY,CAACe,KAAK,CAACL,IAAI,EAAEX,UAAU,CAACgB,KAAK,EAAE6B,WAAW,CAAC7B,KAAK,EAAEf,YAAY,CAACe,KAAK,CAACH,KAAK;MAEhK,CAAC,CAAC,OAAO2B,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEA,KAAK;MAClC,CAAC,SAAS;QACNyM,SAAS,CAACjO,KAAK,GAAG,KAAI;MAC1B;IACJ;;IAEA;IACA,MAAMqB,cAAc,GAAG,MAAAA,CAAA,KAAY;MAC/B;MACAvB,OAAO,CAACC,GAAG,CAAC,OAAO;MACnB,IAAI8B,WAAW,CAAC7B,KAAK,KAAK,CAAC,EAAE;QACzB;MACJ;MACA;MACA,MAAMnC,QAAQ,CAAC;MACf,MAAM4Q,WAAW,GAAGnE,cAAc,CAACtK,KAAI;MACvC,IAAIyO,WAAW,EAAE;QACb;QACAA,WAAW,CAACJ,SAAS,GAAGI,WAAW,CAAClE,YAAW;MACnD;IACJ;;IAIA;IACA,MAAMmE,aAAa,GAAGA,CAAC3C,IAAI,EAAE4C,OAAO,KAAK;MAErC,IAAI,CAACA,OAAO,IAAI,CAAC5C,IAAI,EAAE,OAAOA,IAAI;MAClC,IAAI;QACA;QACA,MAAM6C,cAAc,GAAGD,OAAO,CAACE,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;QACrE,MAAMC,KAAK,GAAG,IAAIC,MAAM,CAACH,cAAc,EAAE,IAAI,CAAC;QAC9C,OAAO7C,IAAI,CAAC8C,OAAO,CAACC,KAAK,EAAEE,KAAK,IAAI,2BAA2BA,KAAK,SAAS,CAAC;MAClF,CAAC,CAAC,OAAOxN,KAAK,EAAE;QACZ1B,OAAO,CAAC0B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;QAC/B,OAAOuK,IAAI;MACf;IACJ;;IAEA;IACA,MAAMkD,mBAAmB,GAAGvR,GAAG,CAAC,KAAK;IACrC,MAAMwR,eAAe,GAAGxR,GAAG,CAAC,EAAE;;IAE9B;IACA,MAAMyR,KAAK,GAAGzR,GAAG,CAAC,CAAC;IACnB,MAAM0R,SAAS,GAAG1R,GAAG,CAAC,CAAC;IACvB,MAAM2R,UAAU,GAAG3R,GAAG,CAAC,CAAC;IAMxB,MAAM4R,eAAe,GAAIlB,CAAC,IAAK;MAC3B,MAAMmB,GAAG,GAAGnB,CAAC,CAACE,MAAK;MACnBc,SAAS,CAACpP,KAAK,GAAGuP,GAAG,CAACC,YAAW;MACjCH,UAAU,CAACrP,KAAK,GAAGuP,GAAG,CAACE,aAAY;;MAEnC;MACA,MAAMC,QAAQ,GAAGhG,MAAM,CAACiG,UAAU,GAAG,GAAE;MACvC,MAAMC,SAAS,GAAGlG,MAAM,CAACmG,WAAW,GAAG,GAAE;;MAEzC;MACA,MAAMC,UAAU,GAAGV,SAAS,CAACpP,KAAK,GAAGqP,UAAU,CAACrP,KAAI;MACpD,MAAM+P,WAAW,GAAGL,QAAQ,GAAGE,SAAQ;MAEvC,IAAII,YAAW;MACf,IAAIF,UAAU,GAAGC,WAAW,EAAE;QAC1B;QACAC,YAAY,GAAGN,QAAQ,GAAGN,SAAS,CAACpP,KAAI;MAC5C,CAAC,MAAM;QACH;QACAgQ,YAAY,GAAGJ,SAAS,GAAGP,UAAU,CAACrP,KAAI;MAC9C;;MAEA;MACAmP,KAAK,CAACnP,KAAK,GAAGiQ,IAAI,CAACC,GAAG,CAACD,IAAI,CAACE,GAAG,CAACH,YAAY,EAAEnR,QAAQ,CAAC,EAAEC,QAAQ;IACrE;;IAEA;IACA,MAAMsR,WAAW,GAAIhC,CAAC,IAAK;MACvB;MACA,MAAMiC,KAAK,GAAGjC,CAAC,CAACkC,MAAM,GAAG,CAAC,GAAG,CAACvR,SAAS,GAAGA,SAAQ;MAClD,MAAMwR,QAAQ,GAAGpB,KAAK,CAACnP,KAAK,GAAGqQ,KAAI;;MAEnC;MACA,IAAIE,QAAQ,IAAI1R,QAAQ,IAAI0R,QAAQ,IAAIzR,QAAQ,EAAE;QAC9CqQ,KAAK,CAACnP,KAAK,GAAGuQ,QAAO;MACzB;IACJ;;IAEA;IACA,MAAMC,gBAAgB,GAAI1P,OAAO,IAAK;MAClC,MAAM2P,QAAQ,GAAG9I,WAAW,CAAC7G,OAAO;MACpC,IAAI2P,QAAQ,EAAE;QACVvB,eAAe,CAAClP,KAAK,GAAGyQ,QAAO;QAC/BxB,mBAAmB,CAACjP,KAAK,GAAG,IAAG;QAC/BmP,KAAK,CAACnP,KAAK,GAAG,CAAC,EAAC;QAChB2G,QAAQ,CAACK,IAAI,CAAC0J,KAAK,CAACC,QAAQ,GAAG,QAAO;MAC1C;IACJ;;IAEA;IACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;MAC5B3B,mBAAmB,CAACjP,KAAK,GAAG,KAAI;MAChCmP,KAAK,CAACnP,KAAK,GAAG,CAAC,EAAC;MAChB2G,QAAQ,CAACK,IAAI,CAAC0J,KAAK,CAACC,QAAQ,GAAG,MAAK;IACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}