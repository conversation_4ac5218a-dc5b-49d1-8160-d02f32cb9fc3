{"ast": null, "code": "import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport isRegExp from './isRegExp.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Splits `string` by `separator`.\n *\n * **Note:** This method is based on\n * [`String#split`](https://mdn.io/String/split).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to split.\n * @param {RegExp|string} separator The separator pattern to split by.\n * @param {number} [limit] The length to truncate results to.\n * @returns {Array} Returns the string segments.\n * @example\n *\n * _.split('a-b-c', '-', 2);\n * // => ['a', 'b']\n */\nfunction split(string, separator, limit) {\n  if (limit && typeof limit != 'number' && isIterateeCall(string, separator, limit)) {\n    separator = limit = undefined;\n  }\n  limit = limit === undefined ? MAX_ARRAY_LENGTH : limit >>> 0;\n  if (!limit) {\n    return [];\n  }\n  string = toString(string);\n  if (string && (typeof separator == 'string' || separator != null && !isRegExp(separator))) {\n    separator = baseToString(separator);\n    if (!separator && hasUnicode(string)) {\n      return castSlice(stringToArray(string), 0, limit);\n    }\n  }\n  return string.split(separator, limit);\n}\nexport default split;", "map": {"version": 3, "names": ["baseToString", "castSlice", "hasUnicode", "isIterateeCall", "isRegExp", "stringToArray", "toString", "MAX_ARRAY_LENGTH", "split", "string", "separator", "limit", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/split.js"], "sourcesContent": ["import baseToString from './_baseToString.js';\nimport castSlice from './_castSlice.js';\nimport hasUnicode from './_hasUnicode.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport isRegExp from './isRegExp.js';\nimport stringToArray from './_stringToArray.js';\nimport toString from './toString.js';\n\n/** Used as references for the maximum length and index of an array. */\nvar MAX_ARRAY_LENGTH = 4294967295;\n\n/**\n * Splits `string` by `separator`.\n *\n * **Note:** This method is based on\n * [`String#split`](https://mdn.io/String/split).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to split.\n * @param {RegExp|string} separator The separator pattern to split by.\n * @param {number} [limit] The length to truncate results to.\n * @returns {Array} Returns the string segments.\n * @example\n *\n * _.split('a-b-c', '-', 2);\n * // => ['a', 'b']\n */\nfunction split(string, separator, limit) {\n  if (limit && typeof limit != 'number' && isIterateeCall(string, separator, limit)) {\n    separator = limit = undefined;\n  }\n  limit = limit === undefined ? MAX_ARRAY_LENGTH : limit >>> 0;\n  if (!limit) {\n    return [];\n  }\n  string = toString(string);\n  if (string && (\n        typeof separator == 'string' ||\n        (separator != null && !isRegExp(separator))\n      )) {\n    separator = baseToString(separator);\n    if (!separator && hasUnicode(string)) {\n      return castSlice(stringToArray(string), 0, limit);\n    }\n  }\n  return string.split(separator, limit);\n}\n\nexport default split;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,UAAU,MAAM,kBAAkB;AACzC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,gBAAgB,GAAG,UAAU;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,MAAM,EAAEC,SAAS,EAAEC,KAAK,EAAE;EACvC,IAAIA,KAAK,IAAI,OAAOA,KAAK,IAAI,QAAQ,IAAIR,cAAc,CAACM,MAAM,EAAEC,SAAS,EAAEC,KAAK,CAAC,EAAE;IACjFD,SAAS,GAAGC,KAAK,GAAGC,SAAS;EAC/B;EACAD,KAAK,GAAGA,KAAK,KAAKC,SAAS,GAAGL,gBAAgB,GAAGI,KAAK,KAAK,CAAC;EAC5D,IAAI,CAACA,KAAK,EAAE;IACV,OAAO,EAAE;EACX;EACAF,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;EACzB,IAAIA,MAAM,KACJ,OAAOC,SAAS,IAAI,QAAQ,IAC3BA,SAAS,IAAI,IAAI,IAAI,CAACN,QAAQ,CAACM,SAAS,CAAE,CAC5C,EAAE;IACLA,SAAS,GAAGV,YAAY,CAACU,SAAS,CAAC;IACnC,IAAI,CAACA,SAAS,IAAIR,UAAU,CAACO,MAAM,CAAC,EAAE;MACpC,OAAOR,SAAS,CAACI,aAAa,CAACI,MAAM,CAAC,EAAE,CAAC,EAAEE,KAAK,CAAC;IACnD;EACF;EACA,OAAOF,MAAM,CAACD,KAAK,CAACE,SAAS,EAAEC,KAAK,CAAC;AACvC;AAEA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}