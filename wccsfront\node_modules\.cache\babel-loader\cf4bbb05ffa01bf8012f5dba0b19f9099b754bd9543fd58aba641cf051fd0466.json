{"ast": null, "code": "import { defineComponent, ref, openBlock, createElementBlock, normalizeClass, unref, normalizeStyle, renderSlot, createElementVNode, mergeProps } from 'vue';\nimport { timePickerRangeTriggerProps } from './props.mjs';\nimport _export_sfc from '../../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useAttrs } from '../../../../hooks/use-attrs/index.mjs';\nimport { useNamespace } from '../../../../hooks/use-namespace/index.mjs';\nimport { useFocusController } from '../../../../hooks/use-focus-controller/index.mjs';\nconst __default__ = defineComponent({\n  name: \"PickerRangeTrigger\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: timePickerRangeTriggerProps,\n  emits: [\"mouseenter\", \"mouseleave\", \"click\", \"touchstart\", \"focus\", \"blur\", \"startInput\", \"endInput\", \"startChange\", \"endChange\"],\n  setup(__props, {\n    expose,\n    emit\n  }) {\n    const attrs = useAttrs();\n    const nsDate = useNamespace(\"date\");\n    const nsRange = useNamespace(\"range\");\n    const inputRef = ref();\n    const endInputRef = ref();\n    const {\n      wrapperRef,\n      isFocused\n    } = useFocusController(inputRef);\n    const handleClick = evt => {\n      emit(\"click\", evt);\n    };\n    const handleMouseEnter = evt => {\n      emit(\"mouseenter\", evt);\n    };\n    const handleMouseLeave = evt => {\n      emit(\"mouseleave\", evt);\n    };\n    const handleTouchStart = evt => {\n      emit(\"mouseenter\", evt);\n    };\n    const handleStartInput = evt => {\n      emit(\"startInput\", evt);\n    };\n    const handleEndInput = evt => {\n      emit(\"endInput\", evt);\n    };\n    const handleStartChange = evt => {\n      emit(\"startChange\", evt);\n    };\n    const handleEndChange = evt => {\n      emit(\"endChange\", evt);\n    };\n    const focus = () => {\n      var _a;\n      (_a = inputRef.value) == null ? void 0 : _a.focus();\n    };\n    const blur = () => {\n      var _a, _b;\n      (_a = inputRef.value) == null ? void 0 : _a.blur();\n      (_b = endInputRef.value) == null ? void 0 : _b.blur();\n    };\n    expose({\n      focus,\n      blur\n    });\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        ref_key: \"wrapperRef\",\n        ref: wrapperRef,\n        class: normalizeClass([unref(nsDate).is(\"active\", unref(isFocused)), _ctx.$attrs.class]),\n        style: normalizeStyle(_ctx.$attrs.style),\n        onClick: handleClick,\n        onMouseenter: handleMouseEnter,\n        onMouseleave: handleMouseLeave,\n        onTouchstartPassive: handleTouchStart\n      }, [renderSlot(_ctx.$slots, \"prefix\"), createElementVNode(\"input\", mergeProps(unref(attrs), {\n        id: _ctx.id && _ctx.id[0],\n        ref_key: \"inputRef\",\n        ref: inputRef,\n        name: _ctx.name && _ctx.name[0],\n        placeholder: _ctx.startPlaceholder,\n        value: _ctx.modelValue && _ctx.modelValue[0],\n        class: unref(nsRange).b(\"input\"),\n        onInput: handleStartInput,\n        onChange: handleStartChange\n      }), null, 16, [\"id\", \"name\", \"placeholder\", \"value\"]), renderSlot(_ctx.$slots, \"range-separator\"), createElementVNode(\"input\", mergeProps(unref(attrs), {\n        id: _ctx.id && _ctx.id[1],\n        ref_key: \"endInputRef\",\n        ref: endInputRef,\n        name: _ctx.name && _ctx.name[1],\n        placeholder: _ctx.endPlaceholder,\n        value: _ctx.modelValue && _ctx.modelValue[1],\n        class: unref(nsRange).b(\"input\"),\n        onInput: handleEndInput,\n        onChange: handleEndChange\n      }), null, 16, [\"id\", \"name\", \"placeholder\", \"value\"]), renderSlot(_ctx.$slots, \"suffix\")], 38);\n    };\n  }\n});\nvar PickerRangeTrigger = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"picker-range-trigger.vue\"]]);\nexport { PickerRangeTrigger as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "attrs", "useAttrs", "nsDate", "useNamespace", "nsRange", "inputRef", "ref", "endInputRef", "wrapperRef", "isFocused", "useFocusController", "handleClick", "evt", "emit", "handleMouseEnter", "handleMouseLeave", "handleTouchStart", "handleStartInput", "handleEndInput", "handleStartChange", "handleEndChange", "focus", "_a", "value", "blur", "_b", "expose"], "sources": ["../../../../../../../packages/components/time-picker/src/common/picker-range-trigger.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"wrapperRef\"\n    :class=\"[nsDate.is('active', isFocused), $attrs.class]\"\n    :style=\"($attrs.style as CSSProperties)\"\n    @click=\"handleClick\"\n    @mouseenter=\"handleMouseEnter\"\n    @mouseleave=\"handleMouseLeave\"\n    @touchstart.passive=\"handleTouchStart\"\n  >\n    <slot name=\"prefix\" />\n    <input\n      v-bind=\"attrs\"\n      :id=\"id && id[0]\"\n      ref=\"inputRef\"\n      :name=\"name && name[0]\"\n      :placeholder=\"startPlaceholder\"\n      :value=\"modelValue && modelValue[0]\"\n      :class=\"nsRange.b('input')\"\n      @input=\"handleStartInput\"\n      @change=\"handleStartChange\"\n    />\n    <slot name=\"range-separator\" />\n    <input\n      v-bind=\"attrs\"\n      :id=\"id && id[1]\"\n      ref=\"endInputRef\"\n      :name=\"name && name[1]\"\n      :placeholder=\"endPlaceholder\"\n      :value=\"modelValue && modelValue[1]\"\n      :class=\"nsRange.b('input')\"\n      @input=\"handleEndInput\"\n      @change=\"handleEndChange\"\n    />\n    <slot name=\"suffix\" />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { ref } from 'vue'\nimport { useAttrs, useFocusController, useNamespace } from '@element-plus/hooks'\nimport { timePickerRangeTriggerProps } from './props'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'PickerRangeTrigger',\n  inheritAttrs: false,\n})\n\ndefineProps(timePickerRangeTriggerProps)\nconst emit = defineEmits([\n  'mouseenter',\n  'mouseleave',\n  'click',\n  'touchstart',\n  'focus',\n  'blur',\n  'startInput',\n  'endInput',\n  'startChange',\n  'endChange',\n])\n\nconst attrs = useAttrs()\nconst nsDate = useNamespace('date')\nconst nsRange = useNamespace('range')\n\nconst inputRef = ref<HTMLInputElement>()\nconst endInputRef = ref<HTMLInputElement>()\n\nconst { wrapperRef, isFocused } = useFocusController(inputRef)\n\nconst handleClick = (evt: MouseEvent) => {\n  emit('click', evt)\n}\n\nconst handleMouseEnter = (evt: MouseEvent) => {\n  emit('mouseenter', evt)\n}\n\nconst handleMouseLeave = (evt: MouseEvent) => {\n  emit('mouseleave', evt)\n}\n\nconst handleTouchStart = (evt: TouchEvent) => {\n  emit('mouseenter', evt)\n}\n\nconst handleStartInput = (evt: Event) => {\n  emit('startInput', evt)\n}\n\nconst handleEndInput = (evt: Event) => {\n  emit('endInput', evt)\n}\n\nconst handleStartChange = (evt: Event) => {\n  emit('startChange', evt)\n}\n\nconst handleEndChange = (evt: Event) => {\n  emit('endChange', evt)\n}\n\nconst focus = () => {\n  inputRef.value?.focus()\n}\n\nconst blur = () => {\n  inputRef.value?.blur()\n  endInputRef.value?.blur()\n}\n\ndefineExpose({\n  focus,\n  blur,\n})\n</script>\n"], "mappings": ";;;;;;mCA4Cc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;;;;IAgBA,MAAMC,KAAA,GAAQC,QAAS;IACjB,MAAAC,MAAA,GAASC,YAAA,CAAa,MAAM;IAC5B,MAAAC,OAAA,GAAUD,YAAA,CAAa,OAAO;IAEpC,MAAME,QAAA,GAAWC,GAAsB;IACvC,MAAMC,WAAA,GAAcD,GAAsB;IAE1C,MAAM;MAAEE,UAAA;MAAYC;IAAU,IAAIC,kBAAA,CAAmBL,QAAQ;IAEvD,MAAAM,WAAA,GAAeC,GAAoB;MACvCC,IAAA,CAAK,SAASD,GAAG;IAAA,CACnB;IAEM,MAAAE,gBAAA,GAAoBF,GAAoB;MAC5CC,IAAA,CAAK,cAAcD,GAAG;IAAA,CACxB;IAEM,MAAAG,gBAAA,GAAoBH,GAAoB;MAC5CC,IAAA,CAAK,cAAcD,GAAG;IAAA,CACxB;IAEM,MAAAI,gBAAA,GAAoBJ,GAAoB;MAC5CC,IAAA,CAAK,cAAcD,GAAG;IAAA,CACxB;IAEM,MAAAK,gBAAA,GAAoBL,GAAe;MACvCC,IAAA,CAAK,cAAcD,GAAG;IAAA,CACxB;IAEM,MAAAM,cAAA,GAAkBN,GAAe;MACrCC,IAAA,CAAK,YAAYD,GAAG;IAAA,CACtB;IAEM,MAAAO,iBAAA,GAAqBP,GAAe;MACxCC,IAAA,CAAK,eAAeD,GAAG;IAAA,CACzB;IAEM,MAAAQ,eAAA,GAAmBR,GAAe;MACtCC,IAAA,CAAK,aAAaD,GAAG;IAAA,CACvB;IAEA,MAAMS,KAAA,GAAQA,CAAA,KAAM;MAClB,IAAAC,EAAA;MACF,CAAAA,EAAA,GAAAjB,QAAA,CAAAkB,KAAA,qBAAAD,EAAA,CAAAD,KAAA;IAEA;IACE,MAAAG,IAAA,GAAAA,CAAA,KAAqB;MACrB,IAAAF,EAAA,EAAAG,EAAA;MACF,CAAAH,EAAA,GAAAjB,QAAA,CAAAkB,KAAA,qBAAAD,EAAA,CAAAE,IAAA;MAEa,CAAAC,EAAA,GAAAlB,WAAA,CAAAgB,KAAA,qBAAAE,EAAA,CAAAD,IAAA;IAAA,CACX;IACAE,MAAA;MACDL,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}