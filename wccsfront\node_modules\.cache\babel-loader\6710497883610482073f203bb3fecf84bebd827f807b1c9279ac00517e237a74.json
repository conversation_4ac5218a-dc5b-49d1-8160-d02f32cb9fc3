{"ast": null, "code": "import flatten from './flatten.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\nexport default flatRest;", "map": {"version": 3, "names": ["flatten", "overRest", "setToString", "flatRest", "func", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_flatRest.js"], "sourcesContent": ["import flatten from './flatten.js';\nimport overRest from './_overRest.js';\nimport setToString from './_setToString.js';\n\n/**\n * A specialized version of `baseRest` which flattens the rest array.\n *\n * @private\n * @param {Function} func The function to apply a rest parameter to.\n * @returns {Function} Returns the new function.\n */\nfunction flatRest(func) {\n  return setToString(overRest(func, undefined, flatten), func + '');\n}\n\nexport default flatRest;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,OAAOF,WAAW,CAACD,QAAQ,CAACG,IAAI,EAAEC,SAAS,EAAEL,OAAO,CAAC,EAAEI,IAAI,GAAG,EAAE,CAAC;AACnE;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}