{"ast": null, "code": "import baseIndexOf from './_baseIndexOf.js';\n\n/**\n * Used by `_.trim` and `_.trimStart` to get the index of the first string symbol\n * that is not found in the character symbols.\n *\n * @private\n * @param {Array} strSymbols The string symbols to inspect.\n * @param {Array} chrSymbols The character symbols to find.\n * @returns {number} Returns the index of the first unmatched string symbol.\n */\nfunction charsStartIndex(strSymbols, chrSymbols) {\n  var index = -1,\n    length = strSymbols.length;\n  while (++index < length && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}\n  return index;\n}\nexport default charsStartIndex;", "map": {"version": 3, "names": ["baseIndexOf", "charsStartIndex", "strSymbols", "chrSymbols", "index", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_charsStartIndex.js"], "sourcesContent": ["import baseIndexOf from './_baseIndexOf.js';\n\n/**\n * Used by `_.trim` and `_.trimStart` to get the index of the first string symbol\n * that is not found in the character symbols.\n *\n * @private\n * @param {Array} strSymbols The string symbols to inspect.\n * @param {Array} chrSymbols The character symbols to find.\n * @returns {number} Returns the index of the first unmatched string symbol.\n */\nfunction charsStartIndex(strSymbols, chrSymbols) {\n  var index = -1,\n      length = strSymbols.length;\n\n  while (++index < length && baseIndexOf(chrSymbols, strSymbols[index], 0) > -1) {}\n  return index;\n}\n\nexport default charsStartIndex;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,UAAU,EAAEC,UAAU,EAAE;EAC/C,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,UAAU,CAACG,MAAM;EAE9B,OAAO,EAAED,KAAK,GAAGC,MAAM,IAAIL,WAAW,CAACG,UAAU,EAAED,UAAU,CAACE,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC;EAChF,OAAOA,KAAK;AACd;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}