{"ast": null, "code": "/**\n * Converts an ASCII `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction asciiToArray(string) {\n  return string.split('');\n}\nexport default asciiToArray;", "map": {"version": 3, "names": ["asciiToArray", "string", "split"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_asciiToArray.js"], "sourcesContent": ["/**\n * Converts an ASCII `string` to an array.\n *\n * @private\n * @param {string} string The string to convert.\n * @returns {Array} Returns the converted array.\n */\nfunction asciiToArray(string) {\n  return string.split('');\n}\n\nexport default asciiToArray;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,MAAM,EAAE;EAC5B,OAAOA,MAAM,CAACC,KAAK,CAAC,EAAE,CAAC;AACzB;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}