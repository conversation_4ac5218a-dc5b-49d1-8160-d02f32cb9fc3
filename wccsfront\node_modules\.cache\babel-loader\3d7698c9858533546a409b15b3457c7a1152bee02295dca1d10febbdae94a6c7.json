{"ast": null, "code": "// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n  aliceblue: '#f0f8ff',\n  antiquewhite: '#faebd7',\n  aqua: '#00ffff',\n  aquamarine: '#7fffd4',\n  azure: '#f0ffff',\n  beige: '#f5f5dc',\n  bisque: '#ffe4c4',\n  black: '#000000',\n  blanchedalmond: '#ffebcd',\n  blue: '#0000ff',\n  blueviolet: '#8a2be2',\n  brown: '#a52a2a',\n  burlywood: '#deb887',\n  cadetblue: '#5f9ea0',\n  chartreuse: '#7fff00',\n  chocolate: '#d2691e',\n  coral: '#ff7f50',\n  cornflowerblue: '#6495ed',\n  cornsilk: '#fff8dc',\n  crimson: '#dc143c',\n  cyan: '#00ffff',\n  darkblue: '#00008b',\n  darkcyan: '#008b8b',\n  darkgoldenrod: '#b8860b',\n  darkgray: '#a9a9a9',\n  darkgreen: '#006400',\n  darkgrey: '#a9a9a9',\n  darkkhaki: '#bdb76b',\n  darkmagenta: '#8b008b',\n  darkolivegreen: '#556b2f',\n  darkorange: '#ff8c00',\n  darkorchid: '#9932cc',\n  darkred: '#8b0000',\n  darksalmon: '#e9967a',\n  darkseagreen: '#8fbc8f',\n  darkslateblue: '#483d8b',\n  darkslategray: '#2f4f4f',\n  darkslategrey: '#2f4f4f',\n  darkturquoise: '#00ced1',\n  darkviolet: '#9400d3',\n  deeppink: '#ff1493',\n  deepskyblue: '#00bfff',\n  dimgray: '#696969',\n  dimgrey: '#696969',\n  dodgerblue: '#1e90ff',\n  firebrick: '#b22222',\n  floralwhite: '#fffaf0',\n  forestgreen: '#228b22',\n  fuchsia: '#ff00ff',\n  gainsboro: '#dcdcdc',\n  ghostwhite: '#f8f8ff',\n  goldenrod: '#daa520',\n  gold: '#ffd700',\n  gray: '#808080',\n  green: '#008000',\n  greenyellow: '#adff2f',\n  grey: '#808080',\n  honeydew: '#f0fff0',\n  hotpink: '#ff69b4',\n  indianred: '#cd5c5c',\n  indigo: '#4b0082',\n  ivory: '#fffff0',\n  khaki: '#f0e68c',\n  lavenderblush: '#fff0f5',\n  lavender: '#e6e6fa',\n  lawngreen: '#7cfc00',\n  lemonchiffon: '#fffacd',\n  lightblue: '#add8e6',\n  lightcoral: '#f08080',\n  lightcyan: '#e0ffff',\n  lightgoldenrodyellow: '#fafad2',\n  lightgray: '#d3d3d3',\n  lightgreen: '#90ee90',\n  lightgrey: '#d3d3d3',\n  lightpink: '#ffb6c1',\n  lightsalmon: '#ffa07a',\n  lightseagreen: '#20b2aa',\n  lightskyblue: '#87cefa',\n  lightslategray: '#778899',\n  lightslategrey: '#778899',\n  lightsteelblue: '#b0c4de',\n  lightyellow: '#ffffe0',\n  lime: '#00ff00',\n  limegreen: '#32cd32',\n  linen: '#faf0e6',\n  magenta: '#ff00ff',\n  maroon: '#800000',\n  mediumaquamarine: '#66cdaa',\n  mediumblue: '#0000cd',\n  mediumorchid: '#ba55d3',\n  mediumpurple: '#9370db',\n  mediumseagreen: '#3cb371',\n  mediumslateblue: '#7b68ee',\n  mediumspringgreen: '#00fa9a',\n  mediumturquoise: '#48d1cc',\n  mediumvioletred: '#c71585',\n  midnightblue: '#191970',\n  mintcream: '#f5fffa',\n  mistyrose: '#ffe4e1',\n  moccasin: '#ffe4b5',\n  navajowhite: '#ffdead',\n  navy: '#000080',\n  oldlace: '#fdf5e6',\n  olive: '#808000',\n  olivedrab: '#6b8e23',\n  orange: '#ffa500',\n  orangered: '#ff4500',\n  orchid: '#da70d6',\n  palegoldenrod: '#eee8aa',\n  palegreen: '#98fb98',\n  paleturquoise: '#afeeee',\n  palevioletred: '#db7093',\n  papayawhip: '#ffefd5',\n  peachpuff: '#ffdab9',\n  peru: '#cd853f',\n  pink: '#ffc0cb',\n  plum: '#dda0dd',\n  powderblue: '#b0e0e6',\n  purple: '#800080',\n  rebeccapurple: '#663399',\n  red: '#ff0000',\n  rosybrown: '#bc8f8f',\n  royalblue: '#4169e1',\n  saddlebrown: '#8b4513',\n  salmon: '#fa8072',\n  sandybrown: '#f4a460',\n  seagreen: '#2e8b57',\n  seashell: '#fff5ee',\n  sienna: '#a0522d',\n  silver: '#c0c0c0',\n  skyblue: '#87ceeb',\n  slateblue: '#6a5acd',\n  slategray: '#708090',\n  slategrey: '#708090',\n  snow: '#fffafa',\n  springgreen: '#00ff7f',\n  steelblue: '#4682b4',\n  tan: '#d2b48c',\n  teal: '#008080',\n  thistle: '#d8bfd8',\n  tomato: '#ff6347',\n  turquoise: '#40e0d0',\n  violet: '#ee82ee',\n  wheat: '#f5deb3',\n  white: '#ffffff',\n  whitesmoke: '#f5f5f5',\n  yellow: '#ffff00',\n  yellowgreen: '#9acd32'\n};", "map": {"version": 3, "names": ["names", "aliceblue", "antiquewhite", "aqua", "aquamarine", "azure", "beige", "bisque", "black", "blanche<PERSON><PERSON>", "blue", "blueviolet", "brown", "burlywood", "cadetblue", "chartreuse", "chocolate", "coral", "cornflowerblue", "cornsilk", "crimson", "cyan", "darkblue", "dark<PERSON>an", "darkgoldenrod", "darkgray", "darkgreen", "<PERSON><PERSON>rey", "<PERSON><PERSON><PERSON>", "darkmagenta", "darkolivegreen", "darkorange", "darkorchid", "darkred", "<PERSON><PERSON><PERSON>", "darkseagreen", "darkslateblue", "darkslategray", "darkslateg<PERSON>", "darkturquoise", "darkviolet", "deeppink", "deepskyblue", "dimgray", "<PERSON><PERSON><PERSON>", "dodgerblue", "firebrick", "<PERSON><PERSON><PERSON><PERSON>", "forestgreen", "fuchsia", "gainsboro", "ghostwhite", "goldenrod", "gold", "gray", "green", "greenyellow", "grey", "honeydew", "hotpink", "indianred", "indigo", "ivory", "khaki", "lavenderblush", "lavender", "lawngreen", "lemon<PERSON>ffon", "lightblue", "lightcoral", "lightcyan", "lightgoldenrodyellow", "lightgray", "lightgreen", "<PERSON><PERSON>rey", "lightpink", "<PERSON><PERSON><PERSON>", "lightseagreen", "lightskyblue", "lightslategray", "lightslategrey", "lightsteelblue", "lightyellow", "lime", "limegreen", "linen", "magenta", "maroon", "mediumaquamarine", "mediumblue", "mediumorchid", "mediumpurple", "mediumseagreen", "mediumslateblue", "mediumspringgreen", "mediumturquoise", "mediumvioletred", "midnightblue", "mintcream", "mistyrose", "moccasin", "navajowhite", "navy", "oldlace", "olive", "<PERSON><PERSON><PERSON>", "orange", "orangered", "orchid", "palegoldenrod", "palegreen", "paleturquoise", "palevioletred", "papayawhip", "peachpuff", "peru", "pink", "plum", "powderblue", "purple", "rebeccapurple", "red", "rosybrown", "royalblue", "saddlebrown", "salmon", "sandybrown", "seagreen", "seashell", "sienna", "silver", "skyblue", "slateblue", "slategray", "<PERSON><PERSON><PERSON>", "snow", "springgreen", "steelblue", "tan", "teal", "thistle", "tomato", "turquoise", "violet", "wheat", "white", "whitesmoke", "yellow", "yellowgreen"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/@ctrl/tinycolor/dist/module/css-color-names.js"], "sourcesContent": ["// https://github.com/bahamas10/css-color-names/blob/master/css-color-names.json\n/**\n * @hidden\n */\nexport var names = {\n    aliceblue: '#f0f8ff',\n    antiquewhite: '#faebd7',\n    aqua: '#00ffff',\n    aquamarine: '#7fffd4',\n    azure: '#f0ffff',\n    beige: '#f5f5dc',\n    bisque: '#ffe4c4',\n    black: '#000000',\n    blanchedalmond: '#ffebcd',\n    blue: '#0000ff',\n    blueviolet: '#8a2be2',\n    brown: '#a52a2a',\n    burlywood: '#deb887',\n    cadetblue: '#5f9ea0',\n    chartreuse: '#7fff00',\n    chocolate: '#d2691e',\n    coral: '#ff7f50',\n    cornflowerblue: '#6495ed',\n    cornsilk: '#fff8dc',\n    crimson: '#dc143c',\n    cyan: '#00ffff',\n    darkblue: '#00008b',\n    darkcyan: '#008b8b',\n    darkgoldenrod: '#b8860b',\n    darkgray: '#a9a9a9',\n    darkgreen: '#006400',\n    darkgrey: '#a9a9a9',\n    darkkhaki: '#bdb76b',\n    darkmagenta: '#8b008b',\n    darkolivegreen: '#556b2f',\n    darkorange: '#ff8c00',\n    darkorchid: '#9932cc',\n    darkred: '#8b0000',\n    darksalmon: '#e9967a',\n    darkseagreen: '#8fbc8f',\n    darkslateblue: '#483d8b',\n    darkslategray: '#2f4f4f',\n    darkslategrey: '#2f4f4f',\n    darkturquoise: '#00ced1',\n    darkviolet: '#9400d3',\n    deeppink: '#ff1493',\n    deepskyblue: '#00bfff',\n    dimgray: '#696969',\n    dimgrey: '#696969',\n    dodgerblue: '#1e90ff',\n    firebrick: '#b22222',\n    floralwhite: '#fffaf0',\n    forestgreen: '#228b22',\n    fuchsia: '#ff00ff',\n    gainsboro: '#dcdcdc',\n    ghostwhite: '#f8f8ff',\n    goldenrod: '#daa520',\n    gold: '#ffd700',\n    gray: '#808080',\n    green: '#008000',\n    greenyellow: '#adff2f',\n    grey: '#808080',\n    honeydew: '#f0fff0',\n    hotpink: '#ff69b4',\n    indianred: '#cd5c5c',\n    indigo: '#4b0082',\n    ivory: '#fffff0',\n    khaki: '#f0e68c',\n    lavenderblush: '#fff0f5',\n    lavender: '#e6e6fa',\n    lawngreen: '#7cfc00',\n    lemonchiffon: '#fffacd',\n    lightblue: '#add8e6',\n    lightcoral: '#f08080',\n    lightcyan: '#e0ffff',\n    lightgoldenrodyellow: '#fafad2',\n    lightgray: '#d3d3d3',\n    lightgreen: '#90ee90',\n    lightgrey: '#d3d3d3',\n    lightpink: '#ffb6c1',\n    lightsalmon: '#ffa07a',\n    lightseagreen: '#20b2aa',\n    lightskyblue: '#87cefa',\n    lightslategray: '#778899',\n    lightslategrey: '#778899',\n    lightsteelblue: '#b0c4de',\n    lightyellow: '#ffffe0',\n    lime: '#00ff00',\n    limegreen: '#32cd32',\n    linen: '#faf0e6',\n    magenta: '#ff00ff',\n    maroon: '#800000',\n    mediumaquamarine: '#66cdaa',\n    mediumblue: '#0000cd',\n    mediumorchid: '#ba55d3',\n    mediumpurple: '#9370db',\n    mediumseagreen: '#3cb371',\n    mediumslateblue: '#7b68ee',\n    mediumspringgreen: '#00fa9a',\n    mediumturquoise: '#48d1cc',\n    mediumvioletred: '#c71585',\n    midnightblue: '#191970',\n    mintcream: '#f5fffa',\n    mistyrose: '#ffe4e1',\n    moccasin: '#ffe4b5',\n    navajowhite: '#ffdead',\n    navy: '#000080',\n    oldlace: '#fdf5e6',\n    olive: '#808000',\n    olivedrab: '#6b8e23',\n    orange: '#ffa500',\n    orangered: '#ff4500',\n    orchid: '#da70d6',\n    palegoldenrod: '#eee8aa',\n    palegreen: '#98fb98',\n    paleturquoise: '#afeeee',\n    palevioletred: '#db7093',\n    papayawhip: '#ffefd5',\n    peachpuff: '#ffdab9',\n    peru: '#cd853f',\n    pink: '#ffc0cb',\n    plum: '#dda0dd',\n    powderblue: '#b0e0e6',\n    purple: '#800080',\n    rebeccapurple: '#663399',\n    red: '#ff0000',\n    rosybrown: '#bc8f8f',\n    royalblue: '#4169e1',\n    saddlebrown: '#8b4513',\n    salmon: '#fa8072',\n    sandybrown: '#f4a460',\n    seagreen: '#2e8b57',\n    seashell: '#fff5ee',\n    sienna: '#a0522d',\n    silver: '#c0c0c0',\n    skyblue: '#87ceeb',\n    slateblue: '#6a5acd',\n    slategray: '#708090',\n    slategrey: '#708090',\n    snow: '#fffafa',\n    springgreen: '#00ff7f',\n    steelblue: '#4682b4',\n    tan: '#d2b48c',\n    teal: '#008080',\n    thistle: '#d8bfd8',\n    tomato: '#ff6347',\n    turquoise: '#40e0d0',\n    violet: '#ee82ee',\n    wheat: '#f5deb3',\n    white: '#ffffff',\n    whitesmoke: '#f5f5f5',\n    yellow: '#ffff00',\n    yellowgreen: '#9acd32',\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,KAAK,GAAG;EACfC,SAAS,EAAE,SAAS;EACpBC,YAAY,EAAE,SAAS;EACvBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EACrBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,SAAS;EAChBC,cAAc,EAAE,SAAS;EACzBC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EACrBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,KAAK,EAAE,SAAS;EAChBC,cAAc,EAAE,SAAS;EACzBC,QAAQ,EAAE,SAAS;EACnBC,OAAO,EAAE,SAAS;EAClBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,QAAQ,EAAE,SAAS;EACnBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,SAAS,EAAE,SAAS;EACpBC,QAAQ,EAAE,SAAS;EACnBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EACtBC,cAAc,EAAE,SAAS;EACzBC,UAAU,EAAE,SAAS;EACrBC,UAAU,EAAE,SAAS;EACrBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,SAAS;EACrBC,YAAY,EAAE,SAAS;EACvBC,aAAa,EAAE,SAAS;EACxBC,aAAa,EAAE,SAAS;EACxBC,aAAa,EAAE,SAAS;EACxBC,aAAa,EAAE,SAAS;EACxBC,UAAU,EAAE,SAAS;EACrBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,SAAS;EACtBC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EACtBC,WAAW,EAAE,SAAS;EACtBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,KAAK,EAAE,SAAS;EAChBC,WAAW,EAAE,SAAS;EACtBC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,aAAa,EAAE,SAAS;EACxBC,QAAQ,EAAE,SAAS;EACnBC,SAAS,EAAE,SAAS;EACpBC,YAAY,EAAE,SAAS;EACvBC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,oBAAoB,EAAE,SAAS;EAC/BC,SAAS,EAAE,SAAS;EACpBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EACtBC,aAAa,EAAE,SAAS;EACxBC,YAAY,EAAE,SAAS;EACvBC,cAAc,EAAE,SAAS;EACzBC,cAAc,EAAE,SAAS;EACzBC,cAAc,EAAE,SAAS;EACzBC,WAAW,EAAE,SAAS;EACtBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAE,SAAS;EACpBC,KAAK,EAAE,SAAS;EAChBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,gBAAgB,EAAE,SAAS;EAC3BC,UAAU,EAAE,SAAS;EACrBC,YAAY,EAAE,SAAS;EACvBC,YAAY,EAAE,SAAS;EACvBC,cAAc,EAAE,SAAS;EACzBC,eAAe,EAAE,SAAS;EAC1BC,iBAAiB,EAAE,SAAS;EAC5BC,eAAe,EAAE,SAAS;EAC1BC,eAAe,EAAE,SAAS;EAC1BC,YAAY,EAAE,SAAS;EACvBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,QAAQ,EAAE,SAAS;EACnBC,WAAW,EAAE,SAAS;EACtBC,IAAI,EAAE,SAAS;EACfC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EACjBC,aAAa,EAAE,SAAS;EACxBC,SAAS,EAAE,SAAS;EACpBC,aAAa,EAAE,SAAS;EACxBC,aAAa,EAAE,SAAS;EACxBC,UAAU,EAAE,SAAS;EACrBC,SAAS,EAAE,SAAS;EACpBC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EACjBC,aAAa,EAAE,SAAS;EACxBC,GAAG,EAAE,SAAS;EACdC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,SAAS;EACtBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE,SAAS;EACrBC,QAAQ,EAAE,SAAS;EACnBC,QAAQ,EAAE,SAAS;EACnBC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,SAAS;EAClBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,SAAS,EAAE,SAAS;EACpBC,IAAI,EAAE,SAAS;EACfC,WAAW,EAAE,SAAS;EACtBC,SAAS,EAAE,SAAS;EACpBC,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,SAAS,EAAE,SAAS;EACpBC,MAAM,EAAE,SAAS;EACjBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,UAAU,EAAE,SAAS;EACrBC,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}