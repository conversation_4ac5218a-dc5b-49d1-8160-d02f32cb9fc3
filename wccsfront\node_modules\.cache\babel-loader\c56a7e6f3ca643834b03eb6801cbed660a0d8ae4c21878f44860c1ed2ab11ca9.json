{"ast": null, "code": "import { capitalize as capitalize$1 } from '@vue/shared';\nexport { camelize, hyphenate, hyphenate as kebabCase } from '@vue/shared';\nconst escapeStringRegexp = (string = \"\") => string.replace(/[|\\\\{}()[\\]^$+*?.]/g, \"\\\\$&\").replace(/-/g, \"\\\\x2d\");\nconst capitalize = str => capitalize$1(str);\nexport { capitalize, escapeStringRegexp };", "map": {"version": 3, "names": ["escapeStringRegexp", "string", "replace", "capitalize", "str", "capitalize$1"], "sources": ["../../../../packages/utils/strings.ts"], "sourcesContent": ["import { capitalize as toCapitalize } from '@vue/shared'\nexport {\n  camelize,\n  hyphenate,\n  hyphenate as kebabCase, // alias\n} from '@vue/shared'\n\n/**\n * fork from {@link https://github.com/sindresorhus/escape-string-regexp}\n */\nexport const escapeStringRegexp = (string = '') =>\n  string.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&').replace(/-/g, '\\\\x2d')\n\n// NOTE: improve capitalize types. Restore previous code after the [PR](https://github.com/vuejs/core/pull/6212) merge\nexport const capitalize = <T extends string>(str: T) =>\n  toCapitalize(str) as Capitalize<T>\n"], "mappings": ";;AAMY,MAACA,kBAAkB,GAAGA,CAACC,MAAM,GAAG,EAAE,KAAKA,MAAM,CAACC,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,OAAO;AAC1G,MAACC,UAAU,GAAIC,GAAG,IAAKC,YAAY,CAACD,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}