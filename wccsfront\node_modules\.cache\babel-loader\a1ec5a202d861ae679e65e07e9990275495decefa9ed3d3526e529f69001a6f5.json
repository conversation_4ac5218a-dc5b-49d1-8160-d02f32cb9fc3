{"ast": null, "code": "import { ref, watch, onMounted } from 'vue';\nimport { Document } from '@element-plus/icons-vue';\nexport default {\n  __name: 'ChatRecordPopUp',\n  props: {\n    title: {\n      type: String,\n      default: '聊天记录' //弹窗标题，默认是聊天记录\n    },\n    chatRecords: {\n      type: Array,\n      default: () => []\n    },\n    visible: {\n      type: Boolean,\n      default: false //弹窗是否可见，默认是关闭的\n    }\n  },\n  emits: ['update:visible', 'close'],\n  setup(__props, {\n    expose: __expose,\n    emit: __emit\n  }) {\n    __expose();\n    const props = __props;\n\n    // 定义emit，用于更新visible属性和关闭弹窗\n    const emit = __emit;\n\n    // 定义dialogVisible，用于控制弹窗的可见性\n    const dialogVisible = ref(props.visible);\n\n    // 监听父组件传入的 visible 属性变化\n    watch(() => props.visible, newVal => {\n      dialogVisible.value = newVal;\n    });\n\n    // 监听本地 dialogVisible 变化，同步回父组件\n    watch(dialogVisible, newVal => {\n      emit('update:visible', newVal);\n    });\n    const handleClose = () => {\n      dialogVisible.value = false;\n      emit('close');\n    };\n    const getLabelClass = label => {\n      switch (label?.toLowerCase()) {\n        case '员工':\n          return 'label-employee';\n        case '微信':\n          return 'label-wechat';\n        default:\n          return 'label-default';\n      }\n    };\n    const formatFileSize = size => {\n      if (!size) return '0 B';\n      const units = ['B', 'KB', 'MB', 'GB'];\n      let index = 0;\n      let fileSize = size;\n      while (fileSize >= 1024 && index < units.length - 1) {\n        fileSize /= 1024;\n        index++;\n      }\n      return `${fileSize.toFixed(2)} ${units[index]}`;\n    };\n    const currentChatRecord = ref(null);\n    onMounted(() => {\n      currentChatRecord.value = {\n        title: props.title,\n        item: props.chatRecords\n      };\n    });\n    const __returned__ = {\n      props,\n      emit,\n      dialogVisible,\n      handleClose,\n      getLabelClass,\n      formatFileSize,\n      currentChatRecord,\n      ref,\n      watch,\n      onMounted,\n      get Document() {\n        return Document;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n};", "map": {"version": 3, "names": ["ref", "watch", "onMounted", "Document", "props", "__props", "emit", "__emit", "dialogVisible", "visible", "newVal", "value", "handleClose", "getLabelClass", "label", "toLowerCase", "formatFileSize", "size", "units", "index", "fileSize", "length", "toFixed", "currentChatRecord", "title", "item", "chatRecords"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/src/components/ChatRecordPopUp.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-model=\"dialogVisible\"\r\n    :title=\"title\"\r\n    width=\"60%\"\r\n    :before-close=\"handleClose\"\r\n    class=\"chat-record-dialog\"\r\n  >\r\n    <div class=\"chat-record-content\">\r\n      <div v-for=\"(item, index) in chatRecords\" :key=\"index\" class=\"chat-message\">\r\n        <div class=\"message-header\">\r\n          <div class=\"sender-info\">\r\n            <span class=\"sender-name\">{{ item.sender }}</span>\r\n            <span class=\"sender-label\" :class=\"getLabelClass(item.sender_label)\">\r\n              @{{ item.sender_label }}\r\n            </span>\r\n          </div>\r\n          <span class=\"message-time\">{{ item.timestamp }}</span>\r\n        </div>\r\n        <div class=\"message-body\" :class=\"{ 'is-received': item.type === 'received' }\">\r\n          <!-- 文本消息 -->\r\n          <div v-if=\"item.msgtype === 'text'\" class=\"text-message\">\r\n            {{ item.content }}\r\n          </div>\r\n          <!-- 图片消息 -->\r\n          <div v-else-if=\"item.msgtype === 'image'\" class=\"image-message\">\r\n            <el-image \r\n              :src=\"item.content\" \r\n              :preview-src-list=\"[item.content]\"\r\n              fit=\"cover\"\r\n            />\r\n          </div>\r\n          <!-- 文件消息 -->\r\n          <div v-else-if=\"item.msgtype === 'file'\" class=\"file-message\">\r\n            <el-button type=\"primary\" link>\r\n              <el-icon><Document /></el-icon>\r\n              {{ item.filename }}\r\n            </el-button>\r\n            <span class=\"file-size\">{{ formatFileSize(item.filesize) }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button @click=\"handleClose\">关闭</el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, defineProps, defineEmits, watch, onMounted } from 'vue'\r\nimport { Document } from '@element-plus/icons-vue'\r\n\r\nconst props = defineProps({\r\n  title: {\r\n    type: String,\r\n    default: '聊天记录'//弹窗标题，默认是聊天记录\r\n  },\r\n  chatRecords: {\r\n    type: Array,\r\n    default: () => []\r\n  },\r\n  visible: {\r\n    type: Boolean,\r\n    default: false//弹窗是否可见，默认是关闭的\r\n  }\r\n})\r\n\r\n// 定义emit，用于更新visible属性和关闭弹窗\r\nconst emit = defineEmits(['update:visible', 'close'])\r\n\r\n// 定义dialogVisible，用于控制弹窗的可见性\r\nconst dialogVisible = ref(props.visible)\r\n\r\n// 监听父组件传入的 visible 属性变化\r\nwatch(() => props.visible, (newVal) => {\r\n  dialogVisible.value = newVal\r\n})\r\n\r\n// 监听本地 dialogVisible 变化，同步回父组件\r\nwatch(dialogVisible, (newVal) => {\r\n  emit('update:visible', newVal)\r\n})\r\n\r\nconst handleClose = () => {\r\n  dialogVisible.value = false\r\n  emit('close')\r\n}\r\n\r\nconst getLabelClass = (label) => {\r\n  switch (label?.toLowerCase()) {\r\n    case '员工':\r\n      return 'label-employee'\r\n    case '微信':\r\n      return 'label-wechat'\r\n    default:\r\n      return 'label-default'\r\n  }\r\n}\r\n\r\nconst formatFileSize = (size) => {\r\n  if (!size) return '0 B'\r\n  const units = ['B', 'KB', 'MB', 'GB']\r\n  let index = 0\r\n  let fileSize = size\r\n\r\n  while (fileSize >= 1024 && index < units.length - 1) {\r\n    fileSize /= 1024\r\n    index++\r\n  }\r\n\r\n  return `${fileSize.toFixed(2)} ${units[index]}`\r\n}\r\n\r\nconst currentChatRecord = ref(null)\r\n\r\nonMounted(() => {\r\n    currentChatRecord.value = {\r\n        title: props.title,\r\n        item: props.chatRecords\r\n    }\r\n})\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.chat-record-dialog {\r\n  --el-dialog-padding-primary: 20px;\r\n}\r\n\r\n.chat-record-content {\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n  padding: 0 10px;\r\n}\r\n\r\n.chat-message {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.message-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.sender-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.sender-name {\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.sender-label {\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.label-employee {\r\n  background-color: #e6f4ff;\r\n  color: #1677ff;\r\n}\r\n\r\n.label-wechat {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.label-default {\r\n  background-color: #f5f5f5;\r\n  color: #666;\r\n}\r\n\r\n.message-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.message-body {\r\n  margin-left: 20px;\r\n  max-width: 80%;\r\n}\r\n\r\n.message-body.is-received {\r\n  margin-left: 0;\r\n  margin-right: 20px;\r\n}\r\n\r\n.text-message {\r\n  background-color: #f5f5f5;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  line-height: 1.5;\r\n  word-break: break-word;\r\n}\r\n\r\n.image-message {\r\n  display: inline-block;\r\n  max-width: 300px;\r\n}\r\n\r\n.image-message :deep(.el-image) {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-message {\r\n  background-color: #f5f5f5;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.file-size {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  margin-top: 20px;\r\n}\r\n</style>"], "mappings": "AAqDA,SAASA,GAA6B,EAAEC,KAAK,EAAEC,SAAS,QAAQ,KAAK;AACrE,SAASC,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;IAElD,MAAMC,KAAK,GAAGC,OAaZ;;IAEF;IACA,MAAMC,IAAI,GAAGC,MAAwC;;IAErD;IACA,MAAMC,aAAa,GAAGR,GAAG,CAACI,KAAK,CAACK,OAAO,CAAC;;IAExC;IACAR,KAAK,CAAC,MAAMG,KAAK,CAACK,OAAO,EAAGC,MAAM,IAAK;MACrCF,aAAa,CAACG,KAAK,GAAGD,MAAM;IAC9B,CAAC,CAAC;;IAEF;IACAT,KAAK,CAACO,aAAa,EAAGE,MAAM,IAAK;MAC/BJ,IAAI,CAAC,gBAAgB,EAAEI,MAAM,CAAC;IAChC,CAAC,CAAC;IAEF,MAAME,WAAW,GAAGA,CAAA,KAAM;MACxBJ,aAAa,CAACG,KAAK,GAAG,KAAK;MAC3BL,IAAI,CAAC,OAAO,CAAC;IACf,CAAC;IAED,MAAMO,aAAa,GAAIC,KAAK,IAAK;MAC/B,QAAQA,KAAK,EAAEC,WAAW,CAAC,CAAC;QAC1B,KAAK,IAAI;UACP,OAAO,gBAAgB;QACzB,KAAK,IAAI;UACP,OAAO,cAAc;QACvB;UACE,OAAO,eAAe;MAC1B;IACF,CAAC;IAED,MAAMC,cAAc,GAAIC,IAAI,IAAK;MAC/B,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;MACvB,MAAMC,KAAK,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;MACrC,IAAIC,KAAK,GAAG,CAAC;MACb,IAAIC,QAAQ,GAAGH,IAAI;MAEnB,OAAOG,QAAQ,IAAI,IAAI,IAAID,KAAK,GAAGD,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;QACnDD,QAAQ,IAAI,IAAI;QAChBD,KAAK,EAAE;MACT;MAEA,OAAO,GAAGC,QAAQ,CAACE,OAAO,CAAC,CAAC,CAAC,IAAIJ,KAAK,CAACC,KAAK,CAAC,EAAE;IACjD,CAAC;IAED,MAAMI,iBAAiB,GAAGvB,GAAG,CAAC,IAAI,CAAC;IAEnCE,SAAS,CAAC,MAAM;MACZqB,iBAAiB,CAACZ,KAAK,GAAG;QACtBa,KAAK,EAAEpB,KAAK,CAACoB,KAAK;QAClBC,IAAI,EAAErB,KAAK,CAACsB;MAChB,CAAC;IACL,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}