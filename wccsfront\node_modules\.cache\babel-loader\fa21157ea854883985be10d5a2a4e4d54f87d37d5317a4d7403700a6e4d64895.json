{"ast": null, "code": "import toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function () {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\nexport default before;", "map": {"version": 3, "names": ["toInteger", "FUNC_ERROR_TEXT", "before", "n", "func", "result", "TypeError", "apply", "arguments", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/before.js"], "sourcesContent": ["import toInteger from './toInteger.js';\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function() {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\n\nexport default before;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;;AAEtC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,CAAC,EAAEC,IAAI,EAAE;EACvB,IAAIC,MAAM;EACV,IAAI,OAAOD,IAAI,IAAI,UAAU,EAAE;IAC7B,MAAM,IAAIE,SAAS,CAACL,eAAe,CAAC;EACtC;EACAE,CAAC,GAAGH,SAAS,CAACG,CAAC,CAAC;EAChB,OAAO,YAAW;IAChB,IAAI,EAAEA,CAAC,GAAG,CAAC,EAAE;MACXE,MAAM,GAAGD,IAAI,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACtC;IACA,IAAIL,CAAC,IAAI,CAAC,EAAE;MACVC,IAAI,GAAGK,SAAS;IAClB;IACA,OAAOJ,MAAM;EACf,CAAC;AACH;AAEA,eAAeH,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}