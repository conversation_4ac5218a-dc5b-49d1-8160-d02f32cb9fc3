{"ast": null, "code": "import castPath from './_castPath.js';\nimport last from './last.js';\nimport parent from './_parent.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\nexport default baseUnset;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "last", "parent", "to<PERSON><PERSON>", "baseUnset", "object", "path"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_baseUnset.js"], "sourcesContent": ["import castPath from './_castPath.js';\nimport last from './last.js';\nimport parent from './_parent.js';\nimport toKey from './_toKey.js';\n\n/**\n * The base implementation of `_.unset`.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {Array|string} path The property path to unset.\n * @returns {boolean} Returns `true` if the property is deleted, else `false`.\n */\nfunction baseUnset(object, path) {\n  path = castPath(path, object);\n  object = parent(object, path);\n  return object == null || delete object[toKey(last(path))];\n}\n\nexport default baseUnset;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;AACrC,OAAOC,IAAI,MAAM,WAAW;AAC5B,OAAOC,MAAM,MAAM,cAAc;AACjC,OAAOC,KAAK,MAAM,aAAa;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEC,IAAI,EAAE;EAC/BA,IAAI,GAAGN,QAAQ,CAACM,IAAI,EAAED,MAAM,CAAC;EAC7BA,MAAM,GAAGH,MAAM,CAACG,MAAM,EAAEC,IAAI,CAAC;EAC7B,OAAOD,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,CAACF,KAAK,CAACF,IAAI,CAACK,IAAI,CAAC,CAAC,CAAC;AAC3D;AAEA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}