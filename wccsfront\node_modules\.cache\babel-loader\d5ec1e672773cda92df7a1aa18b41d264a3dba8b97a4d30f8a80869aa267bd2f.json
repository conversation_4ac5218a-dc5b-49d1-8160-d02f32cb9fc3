{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.reduce.js\";\nimport { ref, toRef, getCurrentInstance, shallowRef, computed, unref, watch } from 'vue';\nimport { useColumns } from './composables/use-columns.mjs';\nimport { useScrollbar } from './composables/use-scrollbar.mjs';\nimport { useRow } from './composables/use-row.mjs';\nimport { useData } from './composables/use-data.mjs';\nimport { useStyles } from './composables/use-styles.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nimport { isArray } from '@vue/shared';\nfunction useTable(props) {\n  const mainTableRef = ref();\n  const leftTableRef = ref();\n  const rightTableRef = ref();\n  const {\n    columns,\n    columnsStyles,\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    hasFixedColumns,\n    mainColumns,\n    onColumnSorted\n  } = useColumns(props, toRef(props, \"columns\"), toRef(props, \"fixed\"));\n  const {\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n    scrollPos\n  } = useScrollbar(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    onMaybeEndReached\n  });\n  const ns = useNamespace(\"table-v2\");\n  const instance = getCurrentInstance();\n  const isScrolling = shallowRef(false);\n  const {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHeightChange,\n    onRowHovered,\n    onRowsRendered\n  } = useRow(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    tableInstance: instance,\n    ns,\n    isScrolling\n  });\n  const {\n    data,\n    depthMap\n  } = useData(props, {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    resetAfterIndex\n  });\n  const rowsHeight = computed(() => {\n    const {\n      estimatedRowHeight,\n      rowHeight\n    } = props;\n    const _data = unref(data);\n    if (isNumber(estimatedRowHeight)) {\n      return Object.values(unref(rowHeights)).reduce((acc, curr) => acc + curr, 0);\n    }\n    return _data.length * rowHeight;\n  });\n  const {\n    bodyWidth,\n    fixedTableHeight,\n    mainTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    headerWidth,\n    windowHeight,\n    footerHeight,\n    emptyStyle,\n    rootStyle,\n    headerHeight\n  } = useStyles(props, {\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    rowsHeight\n  });\n  const containerRef = ref();\n  const showEmpty = computed(() => {\n    const noData = unref(data).length === 0;\n    return isArray(props.fixedData) ? props.fixedData.length === 0 && noData : noData;\n  });\n  function getRowHeight(rowIndex) {\n    const {\n      estimatedRowHeight,\n      rowHeight,\n      rowKey\n    } = props;\n    if (!estimatedRowHeight) return rowHeight;\n    return unref(rowHeights)[unref(data)[rowIndex][rowKey]] || estimatedRowHeight;\n  }\n  function onMaybeEndReached() {\n    const {\n      onEndReached\n    } = props;\n    if (!onEndReached) return;\n    const {\n      scrollTop\n    } = unref(scrollPos);\n    const _totalHeight = unref(rowsHeight);\n    const clientHeight = unref(windowHeight);\n    const heightUntilEnd = _totalHeight - (scrollTop + clientHeight) + props.hScrollbarSize;\n    if (unref(lastRenderedRowIndex) >= 0 && _totalHeight === scrollTop + unref(mainTableHeight) - unref(headerHeight)) {\n      onEndReached(heightUntilEnd);\n    }\n  }\n  watch(() => props.expandedRowKeys, val => expandedRowKeys.value = val, {\n    deep: true\n  });\n  return {\n    columns,\n    containerRef,\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    isDynamic,\n    isResetting,\n    isScrolling,\n    hasFixedColumns,\n    columnsStyles,\n    columnsTotalWidth,\n    data,\n    expandedRowKeys,\n    depthMap,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    mainColumns,\n    bodyWidth,\n    emptyStyle,\n    rootStyle,\n    headerWidth,\n    footerHeight,\n    mainTableHeight,\n    fixedTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    showEmpty,\n    getRowHeight,\n    onColumnSorted,\n    onRowHovered,\n    onRowExpanded,\n    onRowsRendered,\n    onRowHeightChange,\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll\n  };\n}\nexport { useTable };", "map": {"version": 3, "names": ["useTable", "props", "mainTableRef", "ref", "leftTableRef", "rightTableRef", "columns", "columnsStyles", "columnsTotalWidth", "fixedColumnsOnLeft", "fixedColumnsOnRight", "hasFixedColumns", "mainColumns", "onColumnSorted", "useColumns", "toRef", "scrollTo", "scrollToLeft", "scrollToTop", "scrollToRow", "onScroll", "onVerticalScroll", "scrollPos", "useScrollbar", "onMaybeEndReached", "ns", "useNamespace", "instance", "getCurrentInstance", "isScrolling", "shallowRef", "expandedRowKeys", "lastRenderedRowIndex", "isDynamic", "isResetting", "rowHeights", "resetAfterIndex", "onRowExpanded", "onRowHeightChange", "onRowHovered", "onRowsRendered", "useRow", "tableInstance", "data", "depthMap", "useData", "rowsHeight", "computed", "estimatedRowHeight", "rowHeight", "_data", "unref", "isNumber", "Object", "values", "reduce", "acc", "curr", "length", "bodyWidth", "fixedTableHeight", "mainTableHeight", "leftTableWidth", "rightTableWidth", "headerWidth", "windowHeight", "footerHeight", "emptyStyle", "rootStyle", "headerHeight", "useStyles", "containerRef", "showEmpty", "noData", "isArray", "fixedData", "getRowHeight", "rowIndex", "<PERSON><PERSON><PERSON>", "onEndReached", "scrollTop", "_totalHeight", "clientHeight", "heightUntilEnd", "hScrollbarSize", "watch", "val", "value", "deep"], "sources": ["../../../../../../packages/components/table-v2/src/use-table.ts"], "sourcesContent": ["import {\n  computed,\n  getCurrentInstance,\n  ref,\n  shallowRef,\n  toRef,\n  unref,\n  watch,\n} from 'vue'\nimport { isArray, isNumber } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport {\n  useColumns,\n  useData,\n  useRow,\n  useScrollbar,\n  useStyles,\n} from './composables'\n\nimport type { TableV2Props } from './table'\nimport type { TableGridInstance } from './table-grid'\n\nfunction useTable(props: TableV2Props) {\n  const mainTableRef = ref<TableGridInstance>()\n  const leftTableRef = ref<TableGridInstance>()\n  const rightTableRef = ref<TableGridInstance>()\n  const {\n    columns,\n    columnsStyles,\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    hasFixedColumns,\n    mainColumns,\n\n    onColumnSorted,\n  } = useColumns(props, toRef(props, 'columns'), toRef(props, 'fixed'))\n\n  const {\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n    scrollPos,\n  } = useScrollbar(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n\n    onMaybeEndReached,\n  })\n\n  const ns = useNamespace('table-v2')\n  const instance = getCurrentInstance()!\n\n  // state\n  const isScrolling = shallowRef(false)\n\n  const {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    isDynamic,\n    isResetting,\n    rowHeights,\n    resetAfterIndex,\n    onRowExpanded,\n    onRowHeightChange,\n    onRowHovered,\n    onRowsRendered,\n  } = useRow(props, {\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    tableInstance: instance,\n    ns,\n    isScrolling,\n  })\n\n  const { data, depthMap } = useData(props, {\n    expandedRowKeys,\n    lastRenderedRowIndex,\n    resetAfterIndex,\n  })\n\n  const rowsHeight = computed(() => {\n    const { estimatedRowHeight, rowHeight } = props\n    const _data = unref(data)\n    if (isNumber(estimatedRowHeight)) {\n      // calculate the actual height\n      return Object.values(unref(rowHeights)).reduce(\n        (acc, curr) => acc + curr,\n        0\n      )\n    }\n\n    return _data.length * rowHeight\n  })\n\n  const {\n    bodyWidth,\n    fixedTableHeight,\n    mainTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    headerWidth,\n    windowHeight,\n    footerHeight,\n    emptyStyle,\n    rootStyle,\n    headerHeight,\n  } = useStyles(props, {\n    columnsTotalWidth,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    rowsHeight,\n  })\n\n  // DOM/Component refs\n  const containerRef = ref()\n\n  const showEmpty = computed(() => {\n    const noData = unref(data).length === 0\n\n    return isArray(props.fixedData)\n      ? props.fixedData.length === 0 && noData\n      : noData\n  })\n\n  function getRowHeight(rowIndex: number) {\n    const { estimatedRowHeight, rowHeight, rowKey } = props\n\n    if (!estimatedRowHeight) return rowHeight\n\n    return (\n      unref(rowHeights)[unref(data)[rowIndex][rowKey]] || estimatedRowHeight\n    )\n  }\n\n  function onMaybeEndReached() {\n    const { onEndReached } = props\n    if (!onEndReached) return\n\n    const { scrollTop } = unref(scrollPos)\n\n    const _totalHeight = unref(rowsHeight)\n    const clientHeight = unref(windowHeight)\n\n    const heightUntilEnd =\n      _totalHeight - (scrollTop + clientHeight) + props.hScrollbarSize\n\n    if (\n      unref(lastRenderedRowIndex) >= 0 &&\n      _totalHeight === scrollTop + unref(mainTableHeight) - unref(headerHeight)\n    ) {\n      onEndReached(heightUntilEnd)\n    }\n  }\n\n  // events\n\n  watch(\n    () => props.expandedRowKeys,\n    (val) => (expandedRowKeys.value = val),\n    {\n      deep: true,\n    }\n  )\n\n  return {\n    // models\n    columns,\n    containerRef,\n    mainTableRef,\n    leftTableRef,\n    rightTableRef,\n    // states\n    isDynamic,\n    isResetting,\n    isScrolling,\n    hasFixedColumns,\n    // records\n    columnsStyles,\n    columnsTotalWidth,\n    data,\n    expandedRowKeys,\n    depthMap,\n    fixedColumnsOnLeft,\n    fixedColumnsOnRight,\n    mainColumns,\n    // metadata\n    bodyWidth,\n    emptyStyle,\n    rootStyle,\n    headerWidth,\n    footerHeight,\n    mainTableHeight,\n    fixedTableHeight,\n    leftTableWidth,\n    rightTableWidth,\n    // flags\n    showEmpty,\n\n    // methods\n    getRowHeight,\n\n    // event handlers\n    onColumnSorted,\n    onRowHovered,\n    onRowExpanded,\n    onRowsRendered,\n    onRowHeightChange,\n    // use scrollbars\n    scrollTo,\n    scrollToLeft,\n    scrollToTop,\n    scrollToRow,\n    onScroll,\n    onVerticalScroll,\n  }\n}\n\nexport { useTable }\n\nexport type UseTableReturn = ReturnType<typeof useTable>\n"], "mappings": ";;;;;;;;;;;AAkBA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAMC,YAAY,GAAGC,GAAG,EAAE;EAC1B,MAAMC,YAAY,GAAGD,GAAG,EAAE;EAC1B,MAAME,aAAa,GAAGF,GAAG,EAAE;EAC3B,MAAM;IACJG,OAAO;IACPC,aAAa;IACbC,iBAAiB;IACjBC,kBAAkB;IAClBC,mBAAmB;IACnBC,eAAe;IACfC,WAAW;IACXC;EACJ,CAAG,GAAGC,UAAU,CAACb,KAAK,EAAEc,KAAK,CAACd,KAAK,EAAE,SAAS,CAAC,EAAEc,KAAK,CAACd,KAAK,EAAE,OAAO,CAAC,CAAC;EACrE,MAAM;IACJe,QAAQ;IACRC,YAAY;IACZC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC,gBAAgB;IAChBC;EACJ,CAAG,GAAGC,YAAY,CAACtB,KAAK,EAAE;IACtBC,YAAY;IACZE,YAAY;IACZC,aAAa;IACbmB;EACJ,CAAG,CAAC;EACF,MAAMC,EAAE,GAAGC,YAAY,CAAC,UAAU,CAAC;EACnC,MAAMC,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,MAAMC,WAAW,GAAGC,UAAU,CAAC,KAAK,CAAC;EACrC,MAAM;IACJC,eAAe;IACfC,oBAAoB;IACpBC,SAAS;IACTC,WAAW;IACXC,UAAU;IACVC,eAAe;IACfC,aAAa;IACbC,iBAAiB;IACjBC,YAAY;IACZC;EACJ,CAAG,GAAGC,MAAM,CAACxC,KAAK,EAAE;IAChBC,YAAY;IACZE,YAAY;IACZC,aAAa;IACbqC,aAAa,EAAEf,QAAQ;IACvBF,EAAE;IACFI;EACJ,CAAG,CAAC;EACF,MAAM;IAAEc,IAAI;IAAEC;EAAQ,CAAE,GAAGC,OAAO,CAAC5C,KAAK,EAAE;IACxC8B,eAAe;IACfC,oBAAoB;IACpBI;EACJ,CAAG,CAAC;EACF,MAAMU,UAAU,GAAGC,QAAQ,CAAC,MAAM;IAChC,MAAM;MAAEC,kBAAkB;MAAEC;IAAS,CAAE,GAAGhD,KAAK;IAC/C,MAAMiD,KAAK,GAAGC,KAAK,CAACR,IAAI,CAAC;IACzB,IAAIS,QAAQ,CAACJ,kBAAkB,CAAC,EAAE;MAChC,OAAOK,MAAM,CAACC,MAAM,CAACH,KAAK,CAAChB,UAAU,CAAC,CAAC,CAACoB,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGC,IAAI,EAAE,CAAC,CAAC;IAClF;IACI,OAAOP,KAAK,CAACQ,MAAM,GAAGT,SAAS;EACnC,CAAG,CAAC;EACF,MAAM;IACJU,SAAS;IACTC,gBAAgB;IAChBC,eAAe;IACfC,cAAc;IACdC,eAAe;IACfC,WAAW;IACXC,YAAY;IACZC,YAAY;IACZC,UAAU;IACVC,SAAS;IACTC;EACJ,CAAG,GAAGC,SAAS,CAACrE,KAAK,EAAE;IACnBO,iBAAiB;IACjBC,kBAAkB;IAClBC,mBAAmB;IACnBoC;EACJ,CAAG,CAAC;EACF,MAAMyB,YAAY,GAAGpE,GAAG,EAAE;EAC1B,MAAMqE,SAAS,GAAGzB,QAAQ,CAAC,MAAM;IAC/B,MAAM0B,MAAM,GAAGtB,KAAK,CAACR,IAAI,CAAC,CAACe,MAAM,KAAK,CAAC;IACvC,OAAOgB,OAAO,CAACzE,KAAK,CAAC0E,SAAS,CAAC,GAAG1E,KAAK,CAAC0E,SAAS,CAACjB,MAAM,KAAK,CAAC,IAAIe,MAAM,GAAGA,MAAM;EACrF,CAAG,CAAC;EACF,SAASG,YAAYA,CAACC,QAAQ,EAAE;IAC9B,MAAM;MAAE7B,kBAAkB;MAAEC,SAAS;MAAE6B;IAAM,CAAE,GAAG7E,KAAK;IACvD,IAAI,CAAC+C,kBAAkB,EACrB,OAAOC,SAAS;IAClB,OAAOE,KAAK,CAAChB,UAAU,CAAC,CAACgB,KAAK,CAACR,IAAI,CAAC,CAACkC,QAAQ,CAAC,CAACC,MAAM,CAAC,CAAC,IAAI9B,kBAAkB;EACjF;EACE,SAASxB,iBAAiBA,CAAA,EAAG;IAC3B,MAAM;MAAEuD;IAAY,CAAE,GAAG9E,KAAK;IAC9B,IAAI,CAAC8E,YAAY,EACf;IACF,MAAM;MAAEC;IAAS,CAAE,GAAG7B,KAAK,CAAC7B,SAAS,CAAC;IACtC,MAAM2D,YAAY,GAAG9B,KAAK,CAACL,UAAU,CAAC;IACtC,MAAMoC,YAAY,GAAG/B,KAAK,CAACc,YAAY,CAAC;IACxC,MAAMkB,cAAc,GAAGF,YAAY,IAAID,SAAS,GAAGE,YAAY,CAAC,GAAGjF,KAAK,CAACmF,cAAc;IACvF,IAAIjC,KAAK,CAACnB,oBAAoB,CAAC,IAAI,CAAC,IAAIiD,YAAY,KAAKD,SAAS,GAAG7B,KAAK,CAACU,eAAe,CAAC,GAAGV,KAAK,CAACkB,YAAY,CAAC,EAAE;MACjHU,YAAY,CAACI,cAAc,CAAC;IAClC;EACA;EACEE,KAAK,CAAC,MAAMpF,KAAK,CAAC8B,eAAe,EAAGuD,GAAG,IAAKvD,eAAe,CAACwD,KAAK,GAAGD,GAAG,EAAE;IACvEE,IAAI,EAAE;EACV,CAAG,CAAC;EACF,OAAO;IACLlF,OAAO;IACPiE,YAAY;IACZrE,YAAY;IACZE,YAAY;IACZC,aAAa;IACb4B,SAAS;IACTC,WAAW;IACXL,WAAW;IACXlB,eAAe;IACfJ,aAAa;IACbC,iBAAiB;IACjBmC,IAAI;IACJZ,eAAe;IACfa,QAAQ;IACRnC,kBAAkB;IAClBC,mBAAmB;IACnBE,WAAW;IACX+C,SAAS;IACTQ,UAAU;IACVC,SAAS;IACTJ,WAAW;IACXE,YAAY;IACZL,eAAe;IACfD,gBAAgB;IAChBE,cAAc;IACdC,eAAe;IACfS,SAAS;IACTI,YAAY;IACZ/D,cAAc;IACd0B,YAAY;IACZF,aAAa;IACbG,cAAc;IACdF,iBAAiB;IACjBtB,QAAQ;IACRC,YAAY;IACZC,WAAW;IACXC,WAAW;IACXC,QAAQ;IACRC;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}