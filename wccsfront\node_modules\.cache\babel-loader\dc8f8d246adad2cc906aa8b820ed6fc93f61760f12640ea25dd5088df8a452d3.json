{"ast": null, "code": "import { defineComponent, computed, provide, openBlock, createBlock, resolveDynamicComponent, normalizeClass, unref, normalizeStyle, withCtx, renderSlot } from 'vue';\nimport { rowContextKey } from './constants.mjs';\nimport { rowProps } from './row.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElRow\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: rowProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"row\");\n    const gutter = computed(() => props.gutter);\n    provide(rowContextKey, {\n      gutter\n    });\n    const style = computed(() => {\n      const styles = {};\n      if (!props.gutter) {\n        return styles;\n      }\n      styles.marginRight = styles.marginLeft = `-${props.gutter / 2}px`;\n      return styles;\n    });\n    const rowKls = computed(() => [ns.b(), ns.is(`justify-${props.justify}`, props.justify !== \"start\"), ns.is(`align-${props.align}`, !!props.align)]);\n    return (_ctx, _cache) => {\n      return openBlock(), createBlock(resolveDynamicComponent(_ctx.tag), {\n        class: normalizeClass(unref(rowKls)),\n        style: normalizeStyle(unref(style))\n      }, {\n        default: withCtx(() => [renderSlot(_ctx.$slots, \"default\")]),\n        _: 3\n      }, 8, [\"class\", \"style\"]);\n    };\n  }\n});\nvar Row = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"row.vue\"]]);\nexport { Row as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "gutter", "computed", "props", "provide", "rowContextKey", "style", "styles", "marginRight", "marginLeft", "rowKls", "b", "is", "justify", "align"], "sources": ["../../../../../../packages/components/row/src/row.vue"], "sourcesContent": ["<template>\n  <component :is=\"tag\" :class=\"rowKls\" :style=\"style\">\n    <slot />\n  </component>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, provide } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { rowContextKey } from './constants'\nimport { rowProps } from './row'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElRow',\n})\n\nconst props = defineProps(rowProps)\n\nconst ns = useNamespace('row')\nconst gutter = computed(() => props.gutter)\n\nprovide(rowContextKey, {\n  gutter,\n})\n\nconst style = computed(() => {\n  const styles: CSSProperties = {}\n  if (!props.gutter) {\n    return styles\n  }\n\n  styles.marginRight = styles.marginLeft = `-${props.gutter / 2}px`\n  return styles\n})\n\nconst rowKls = computed(() => [\n  ns.b(),\n  ns.is(`justify-${props.justify}`, props.justify !== 'start'),\n  ns.is(`align-${props.align}`, !!props.align),\n])\n</script>\n"], "mappings": ";;;;;mCAac;EACZA,IAAM;AACR;;;;;;IAIM,MAAAC,EAAA,GAAKC,YAAA,CAAa,KAAK;IAC7B,MAAMC,MAAS,GAAAC,QAAA,CAAS,MAAMC,KAAA,CAAMF,MAAM;IAE1CG,OAAA,CAAQC,aAAe;MACrBJ;IAAA,CACD;IAEK,MAAAK,KAAA,GAAQJ,QAAA,CAAS,MAAM;MAC3B,MAAMK,MAAA,GAAwB,EAAC;MAC3B,KAACJ,KAAA,CAAMF,MAAQ;QACV,OAAAM,MAAA;MAAA;MAGTA,MAAA,CAAOC,WAAA,GAAcD,MAAO,CAAAE,UAAA,GAAa,IAAIN,KAAA,CAAMF,MAAA,GAAS,CAAC;MACtD,OAAAM,MAAA;IAAA,CACR;IAEK,MAAAG,MAAA,GAASR,QAAA,CAAS,MAAM,CAC5BH,EAAA,CAAGY,CAAE,IACLZ,EAAA,CAAGa,EAAA,CAAG,WAAWT,KAAA,CAAMU,OAAO,EAAI,EAAAV,KAAA,CAAMU,OAAA,KAAY,OAAO,GAC3Dd,EAAA,CAAGa,EAAA,CAAG,SAAST,KAAA,CAAMW,KAAK,EAAI,GAAC,CAACX,KAAA,CAAMW,KAAK,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}