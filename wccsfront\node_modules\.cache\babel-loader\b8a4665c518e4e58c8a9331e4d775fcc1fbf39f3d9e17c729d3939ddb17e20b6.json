{"ast": null, "code": "import baseAssignValue from './_baseAssignValue.js';\nimport eq from './eq.js';\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if (value !== undefined && !eq(object[key], value) || value === undefined && !(key in object)) {\n    baseAssignValue(object, key, value);\n  }\n}\nexport default assignMergeValue;", "map": {"version": 3, "names": ["baseAssignValue", "eq", "assignMergeValue", "object", "key", "value", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_assignMergeValue.js"], "sourcesContent": ["import baseAssignValue from './_baseAssignValue.js';\nimport eq from './eq.js';\n\n/**\n * This function is like `assignValue` except that it doesn't assign\n * `undefined` values.\n *\n * @private\n * @param {Object} object The object to modify.\n * @param {string} key The key of the property to assign.\n * @param {*} value The value to assign.\n */\nfunction assignMergeValue(object, key, value) {\n  if ((value !== undefined && !eq(object[key], value)) ||\n      (value === undefined && !(key in object))) {\n    baseAssignValue(object, key, value);\n  }\n}\n\nexport default assignMergeValue;\n"], "mappings": "AAAA,OAAOA,eAAe,MAAM,uBAAuB;AACnD,OAAOC,EAAE,MAAM,SAAS;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAE;EAC5C,IAAKA,KAAK,KAAKC,SAAS,IAAI,CAACL,EAAE,CAACE,MAAM,CAACC,GAAG,CAAC,EAAEC,KAAK,CAAC,IAC9CA,KAAK,KAAKC,SAAS,IAAI,EAAEF,GAAG,IAAID,MAAM,CAAE,EAAE;IAC7CH,eAAe,CAACG,MAAM,EAAEC,GAAG,EAAEC,KAAK,CAAC;EACrC;AACF;AAEA,eAAeH,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}