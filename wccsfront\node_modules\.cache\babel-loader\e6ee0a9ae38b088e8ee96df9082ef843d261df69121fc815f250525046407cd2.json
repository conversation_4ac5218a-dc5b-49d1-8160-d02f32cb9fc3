{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst disabledTimeListsProps = buildProps({\n  disabledHours: {\n    type: definePropType(Function)\n  },\n  disabledMinutes: {\n    type: definePropType(Function)\n  },\n  disabledSeconds: {\n    type: definePropType(Function)\n  }\n});\nconst timePanelSharedProps = buildProps({\n  visible: Boolean,\n  actualVisible: {\n    type: Boolean,\n    default: void 0\n  },\n  format: {\n    type: String,\n    default: \"\"\n  }\n});\nexport { disabledTimeListsProps, timePanelSharedProps };", "map": {"version": 3, "names": ["disabledTimeListsProps", "buildProps", "disabledHours", "type", "definePropType", "Function", "disabledMinutes", "disabledSeconds", "timePanelSharedProps", "visible", "Boolean", "actualVisible", "default", "format", "String"], "sources": ["../../../../../../../packages/components/time-picker/src/props/shared.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\nimport type {\n  GetDisabledHours,\n  GetDisabledMinutes,\n  GetDisabledSeconds,\n} from '../common/props'\n\nexport const disabledTimeListsProps = buildProps({\n  /**\n   * @description To specify the array of hours that cannot be selected\n   */\n  disabledHours: {\n    type: definePropType<GetDisabledHours>(Function),\n  },\n  /**\n   * @description To specify the array of minutes that cannot be selected\n   */\n  disabledMinutes: {\n    type: definePropType<GetDisabledMinutes>(Function),\n  },\n  /**\n   * @description To specify the array of seconds that cannot be selected\n   */\n  disabledSeconds: {\n    type: definePropType<GetDisabledSeconds>(Function),\n  },\n} as const)\n\nexport type DisabledTimeListsProps = ExtractPropTypes<\n  typeof disabledTimeListsProps\n>\n\nexport const timePanelSharedProps = buildProps({\n  visible: Boolean,\n  actualVisible: {\n    type: Boolean,\n    default: undefined,\n  },\n  format: {\n    type: String,\n    default: '',\n  },\n} as const)\n\nexport type TimePanelSharedProps = ExtractPropTypes<typeof timePanelSharedProps>\n"], "mappings": ";AACY,MAACA,sBAAsB,GAAGC,UAAU,CAAC;EAC/CC,aAAa,EAAE;IACbC,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDC,eAAe,EAAE;IACfH,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC,CAAG;EACDE,eAAe,EAAE;IACfJ,IAAI,EAAEC,cAAc,CAACC,QAAQ;EACjC;AACA,CAAC;AACW,MAACG,oBAAoB,GAAGP,UAAU,CAAC;EAC7CQ,OAAO,EAAEC,OAAO;EAChBC,aAAa,EAAE;IACbR,IAAI,EAAEO,OAAO;IACbE,OAAO,EAAE,KAAK;EAClB,CAAG;EACDC,MAAM,EAAE;IACNV,IAAI,EAAEW,MAAM;IACZF,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}