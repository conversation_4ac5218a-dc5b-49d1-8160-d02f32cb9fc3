{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport \"core-js/modules/web.url-search-params.delete.js\";\nimport \"core-js/modules/web.url-search-params.has.js\";\nimport \"core-js/modules/web.url-search-params.size.js\";\nimport { watch } from 'vue';\nimport { isNil } from 'lodash-unified';\nimport { useVModel } from '@vueuse/core';\nimport { genFileId } from './upload.mjs';\nimport { debugWarn, throwError } from '../../../utils/error.mjs';\nconst SCOPE = \"ElUpload\";\nconst revokeFileObjectURL = file => {\n  var _a;\n  if ((_a = file.url) == null ? void 0 : _a.startsWith(\"blob:\")) {\n    URL.revokeObjectURL(file.url);\n  }\n};\nconst useHandlers = (props, uploadRef) => {\n  const uploadFiles = useVModel(props, \"fileList\", void 0, {\n    passive: true\n  });\n  const getFile = rawFile => uploadFiles.value.find(file => file.uid === rawFile.uid);\n  function abort(file) {\n    var _a;\n    (_a = uploadRef.value) == null ? void 0 : _a.abort(file);\n  }\n  function clearFiles(states = [\"ready\", \"uploading\", \"success\", \"fail\"]) {\n    uploadFiles.value = uploadFiles.value.filter(row => !states.includes(row.status));\n  }\n  function removeFile(file) {\n    uploadFiles.value = uploadFiles.value.filter(uploadFile => uploadFile.uid !== file.uid);\n  }\n  const handleError = (err, rawFile) => {\n    const file = getFile(rawFile);\n    if (!file) return;\n    console.error(err);\n    file.status = \"fail\";\n    removeFile(file);\n    props.onError(err, file, uploadFiles.value);\n    props.onChange(file, uploadFiles.value);\n  };\n  const handleProgress = (evt, rawFile) => {\n    const file = getFile(rawFile);\n    if (!file) return;\n    props.onProgress(evt, file, uploadFiles.value);\n    file.status = \"uploading\";\n    file.percentage = Math.round(evt.percent);\n  };\n  const handleSuccess = (response, rawFile) => {\n    const file = getFile(rawFile);\n    if (!file) return;\n    file.status = \"success\";\n    file.response = response;\n    props.onSuccess(response, file, uploadFiles.value);\n    props.onChange(file, uploadFiles.value);\n  };\n  const handleStart = file => {\n    if (isNil(file.uid)) file.uid = genFileId();\n    const uploadFile = {\n      name: file.name,\n      percentage: 0,\n      status: \"ready\",\n      size: file.size,\n      raw: file,\n      uid: file.uid\n    };\n    if (props.listType === \"picture-card\" || props.listType === \"picture\") {\n      try {\n        uploadFile.url = URL.createObjectURL(file);\n      } catch (err) {\n        debugWarn(SCOPE, err.message);\n        props.onError(err, uploadFile, uploadFiles.value);\n      }\n    }\n    uploadFiles.value = [...uploadFiles.value, uploadFile];\n    props.onChange(uploadFile, uploadFiles.value);\n  };\n  const handleRemove = async file => {\n    const uploadFile = file instanceof File ? getFile(file) : file;\n    if (!uploadFile) throwError(SCOPE, \"file to be removed not found\");\n    const doRemove = file2 => {\n      abort(file2);\n      removeFile(file2);\n      props.onRemove(file2, uploadFiles.value);\n      revokeFileObjectURL(file2);\n    };\n    if (props.beforeRemove) {\n      const before = await props.beforeRemove(uploadFile, uploadFiles.value);\n      if (before !== false) doRemove(uploadFile);\n    } else {\n      doRemove(uploadFile);\n    }\n  };\n  function submit() {\n    uploadFiles.value.filter(({\n      status\n    }) => status === \"ready\").forEach(({\n      raw\n    }) => {\n      var _a;\n      return raw && ((_a = uploadRef.value) == null ? void 0 : _a.upload(raw));\n    });\n  }\n  watch(() => props.listType, val => {\n    if (val !== \"picture-card\" && val !== \"picture\") {\n      return;\n    }\n    uploadFiles.value = uploadFiles.value.map(file => {\n      const {\n        raw,\n        url\n      } = file;\n      if (!url && raw) {\n        try {\n          file.url = URL.createObjectURL(raw);\n        } catch (err) {\n          props.onError(err, file, uploadFiles.value);\n        }\n      }\n      return file;\n    });\n  });\n  watch(uploadFiles, files => {\n    for (const file of files) {\n      file.uid || (file.uid = genFileId());\n      file.status || (file.status = \"success\");\n    }\n  }, {\n    immediate: true,\n    deep: true\n  });\n  return {\n    uploadFiles,\n    abort,\n    clearFiles,\n    handleError,\n    handleProgress,\n    handleStart,\n    handleSuccess,\n    handleRemove,\n    submit,\n    revokeFileObjectURL\n  };\n};\nexport { useHandlers };", "map": {"version": 3, "names": ["SCOPE", "revokeFileObjectURL", "file", "_a", "url", "startsWith", "URL", "revokeObjectURL", "useHandlers", "props", "uploadRef", "uploadFiles", "useVModel", "passive", "getFile", "rawFile", "value", "find", "uid", "abort", "clearFiles", "states", "filter", "row", "includes", "status", "removeFile", "uploadFile", "handleError", "err", "console", "error", "onError", "onChange", "handleProgress", "evt", "onProgress", "percentage", "Math", "round", "percent", "handleSuccess", "response", "onSuccess", "handleStart", "isNil", "genFileId", "name", "size", "raw", "listType", "createObjectURL", "debugWarn", "message", "handleRemove", "File", "throwError", "doRemove", "file2", "onRemove", "beforeRemove", "before", "submit", "for<PERSON>ach", "upload", "watch", "val", "map", "files", "immediate", "deep"], "sources": ["../../../../../../packages/components/upload/src/use-handlers.ts"], "sourcesContent": ["import { watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { useVModel } from '@vueuse/core'\nimport { debugWarn, throwError } from '@element-plus/utils'\nimport { genFileId } from './upload'\nimport type { ShallowRef } from 'vue'\nimport type {\n  UploadContentInstance,\n  UploadContentProps,\n} from './upload-content'\nimport type {\n  UploadFile,\n  UploadFiles,\n  UploadProps,\n  UploadRawFile,\n  UploadStatus,\n} from './upload'\n\nconst SCOPE = 'ElUpload'\n\nconst revokeFileObjectURL = (file: UploadFile) => {\n  if (file.url?.startsWith('blob:')) {\n    URL.revokeObjectURL(file.url)\n  }\n}\n\nexport const useHandlers = (\n  props: UploadProps,\n  uploadRef: ShallowRef<UploadContentInstance | undefined>\n) => {\n  const uploadFiles = useVModel(\n    props as Omit<UploadProps, 'fileList'> & { fileList: UploadFiles },\n    'fileList',\n    undefined,\n    { passive: true }\n  )\n\n  const getFile = (rawFile: UploadRawFile) =>\n    uploadFiles.value.find((file) => file.uid === rawFile.uid)\n\n  function abort(file: UploadFile) {\n    uploadRef.value?.abort(file)\n  }\n\n  function clearFiles(\n    /** @default ['ready', 'uploading', 'success', 'fail'] */\n    states: UploadStatus[] = ['ready', 'uploading', 'success', 'fail']\n  ) {\n    uploadFiles.value = uploadFiles.value.filter(\n      (row) => !states.includes(row.status)\n    )\n  }\n\n  function removeFile(file: UploadFile) {\n    uploadFiles.value = uploadFiles.value.filter(\n      (uploadFile) => uploadFile.uid !== file.uid\n    )\n  }\n\n  const handleError: UploadContentProps['onError'] = (err, rawFile) => {\n    const file = getFile(rawFile)\n    if (!file) return\n\n    console.error(err)\n    file.status = 'fail'\n    removeFile(file)\n    props.onError(err, file, uploadFiles.value)\n    props.onChange(file, uploadFiles.value)\n  }\n\n  const handleProgress: UploadContentProps['onProgress'] = (evt, rawFile) => {\n    const file = getFile(rawFile)\n    if (!file) return\n\n    props.onProgress(evt, file, uploadFiles.value)\n    file.status = 'uploading'\n    file.percentage = Math.round(evt.percent)\n  }\n\n  const handleSuccess: UploadContentProps['onSuccess'] = (\n    response,\n    rawFile\n  ) => {\n    const file = getFile(rawFile)\n    if (!file) return\n\n    file.status = 'success'\n    file.response = response\n    props.onSuccess(response, file, uploadFiles.value)\n    props.onChange(file, uploadFiles.value)\n  }\n\n  const handleStart: UploadContentProps['onStart'] = (file) => {\n    if (isNil(file.uid)) file.uid = genFileId()\n    const uploadFile: UploadFile = {\n      name: file.name,\n      percentage: 0,\n      status: 'ready',\n      size: file.size,\n      raw: file,\n      uid: file.uid,\n    }\n    if (props.listType === 'picture-card' || props.listType === 'picture') {\n      try {\n        uploadFile.url = URL.createObjectURL(file)\n      } catch (err: unknown) {\n        debugWarn(SCOPE, (err as Error).message)\n        props.onError(err as Error, uploadFile, uploadFiles.value)\n      }\n    }\n    uploadFiles.value = [...uploadFiles.value, uploadFile]\n    props.onChange(uploadFile, uploadFiles.value)\n  }\n\n  const handleRemove: UploadContentProps['onRemove'] = async (\n    file\n  ): Promise<void> => {\n    const uploadFile = file instanceof File ? getFile(file) : file\n    if (!uploadFile) throwError(SCOPE, 'file to be removed not found')\n\n    const doRemove = (file: UploadFile) => {\n      abort(file)\n      removeFile(file)\n      props.onRemove(file, uploadFiles.value)\n      revokeFileObjectURL(file)\n    }\n\n    if (props.beforeRemove) {\n      const before = await props.beforeRemove(uploadFile, uploadFiles.value)\n      if (before !== false) doRemove(uploadFile)\n    } else {\n      doRemove(uploadFile)\n    }\n  }\n\n  function submit() {\n    uploadFiles.value\n      .filter(({ status }) => status === 'ready')\n      .forEach(({ raw }) => raw && uploadRef.value?.upload(raw))\n  }\n\n  watch(\n    () => props.listType,\n    (val) => {\n      if (val !== 'picture-card' && val !== 'picture') {\n        return\n      }\n\n      uploadFiles.value = uploadFiles.value.map((file) => {\n        const { raw, url } = file\n        if (!url && raw) {\n          try {\n            file.url = URL.createObjectURL(raw)\n          } catch (err: unknown) {\n            props.onError(err as Error, file, uploadFiles.value)\n          }\n        }\n        return file\n      })\n    }\n  )\n\n  watch(\n    uploadFiles,\n    (files) => {\n      for (const file of files) {\n        file.uid ||= genFileId()\n        file.status ||= 'success'\n      }\n    },\n    { immediate: true, deep: true }\n  )\n\n  return {\n    /** @description two-way binding ref from props `fileList` */\n    uploadFiles,\n    abort,\n    clearFiles,\n    handleError,\n    handleProgress,\n    handleStart,\n    handleSuccess,\n    handleRemove,\n    submit,\n    revokeFileObjectURL,\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAKA,MAAMA,KAAK,GAAG,UAAU;AACxB,MAAMC,mBAAmB,GAAIC,IAAI,IAAK;EACpC,IAAIC,EAAE;EACN,IAAI,CAACA,EAAE,GAAGD,IAAI,CAACE,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,UAAU,CAAC,OAAO,CAAC,EAAE;IAC7DC,GAAG,CAACC,eAAe,CAACL,IAAI,CAACE,GAAG,CAAC;EACjC;AACA,CAAC;AACW,MAACI,WAAW,GAAGA,CAACC,KAAK,EAAEC,SAAS,KAAK;EAC/C,MAAMC,WAAW,GAAGC,SAAS,CAACH,KAAK,EAAE,UAAU,EAAE,KAAK,CAAC,EAAE;IAAEI,OAAO,EAAE;EAAI,CAAE,CAAC;EAC3E,MAAMC,OAAO,GAAIC,OAAO,IAAKJ,WAAW,CAACK,KAAK,CAACC,IAAI,CAAEf,IAAI,IAAKA,IAAI,CAACgB,GAAG,KAAKH,OAAO,CAACG,GAAG,CAAC;EACvF,SAASC,KAAKA,CAACjB,IAAI,EAAE;IACnB,IAAIC,EAAE;IACN,CAACA,EAAE,GAAGO,SAAS,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGb,EAAE,CAACgB,KAAK,CAACjB,IAAI,CAAC;EAC5D;EACE,SAASkB,UAAUA,CAACC,MAAM,GAAG,CAAC,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,CAAC,EAAE;IACtEV,WAAW,CAACK,KAAK,GAAGL,WAAW,CAACK,KAAK,CAACM,MAAM,CAAEC,GAAG,IAAK,CAACF,MAAM,CAACG,QAAQ,CAACD,GAAG,CAACE,MAAM,CAAC,CAAC;EACvF;EACE,SAASC,UAAUA,CAACxB,IAAI,EAAE;IACxBS,WAAW,CAACK,KAAK,GAAGL,WAAW,CAACK,KAAK,CAACM,MAAM,CAAEK,UAAU,IAAKA,UAAU,CAACT,GAAG,KAAKhB,IAAI,CAACgB,GAAG,CAAC;EAC7F;EACE,MAAMU,WAAW,GAAGA,CAACC,GAAG,EAAEd,OAAO,KAAK;IACpC,MAAMb,IAAI,GAAGY,OAAO,CAACC,OAAO,CAAC;IAC7B,IAAI,CAACb,IAAI,EACP;IACF4B,OAAO,CAACC,KAAK,CAACF,GAAG,CAAC;IAClB3B,IAAI,CAACuB,MAAM,GAAG,MAAM;IACpBC,UAAU,CAACxB,IAAI,CAAC;IAChBO,KAAK,CAACuB,OAAO,CAACH,GAAG,EAAE3B,IAAI,EAAES,WAAW,CAACK,KAAK,CAAC;IAC3CP,KAAK,CAACwB,QAAQ,CAAC/B,IAAI,EAAES,WAAW,CAACK,KAAK,CAAC;EAC3C,CAAG;EACD,MAAMkB,cAAc,GAAGA,CAACC,GAAG,EAAEpB,OAAO,KAAK;IACvC,MAAMb,IAAI,GAAGY,OAAO,CAACC,OAAO,CAAC;IAC7B,IAAI,CAACb,IAAI,EACP;IACFO,KAAK,CAAC2B,UAAU,CAACD,GAAG,EAAEjC,IAAI,EAAES,WAAW,CAACK,KAAK,CAAC;IAC9Cd,IAAI,CAACuB,MAAM,GAAG,WAAW;IACzBvB,IAAI,CAACmC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAACJ,GAAG,CAACK,OAAO,CAAC;EAC7C,CAAG;EACD,MAAMC,aAAa,GAAGA,CAACC,QAAQ,EAAE3B,OAAO,KAAK;IAC3C,MAAMb,IAAI,GAAGY,OAAO,CAACC,OAAO,CAAC;IAC7B,IAAI,CAACb,IAAI,EACP;IACFA,IAAI,CAACuB,MAAM,GAAG,SAAS;IACvBvB,IAAI,CAACwC,QAAQ,GAAGA,QAAQ;IACxBjC,KAAK,CAACkC,SAAS,CAACD,QAAQ,EAAExC,IAAI,EAAES,WAAW,CAACK,KAAK,CAAC;IAClDP,KAAK,CAACwB,QAAQ,CAAC/B,IAAI,EAAES,WAAW,CAACK,KAAK,CAAC;EAC3C,CAAG;EACD,MAAM4B,WAAW,GAAI1C,IAAI,IAAK;IAC5B,IAAI2C,KAAK,CAAC3C,IAAI,CAACgB,GAAG,CAAC,EACjBhB,IAAI,CAACgB,GAAG,GAAG4B,SAAS,EAAE;IACxB,MAAMnB,UAAU,GAAG;MACjBoB,IAAI,EAAE7C,IAAI,CAAC6C,IAAI;MACfV,UAAU,EAAE,CAAC;MACbZ,MAAM,EAAE,OAAO;MACfuB,IAAI,EAAE9C,IAAI,CAAC8C,IAAI;MACfC,GAAG,EAAE/C,IAAI;MACTgB,GAAG,EAAEhB,IAAI,CAACgB;IAChB,CAAK;IACD,IAAIT,KAAK,CAACyC,QAAQ,KAAK,cAAc,IAAIzC,KAAK,CAACyC,QAAQ,KAAK,SAAS,EAAE;MACrE,IAAI;QACFvB,UAAU,CAACvB,GAAG,GAAGE,GAAG,CAAC6C,eAAe,CAACjD,IAAI,CAAC;MAClD,CAAO,CAAC,OAAO2B,GAAG,EAAE;QACZuB,SAAS,CAACpD,KAAK,EAAE6B,GAAG,CAACwB,OAAO,CAAC;QAC7B5C,KAAK,CAACuB,OAAO,CAACH,GAAG,EAAEF,UAAU,EAAEhB,WAAW,CAACK,KAAK,CAAC;MACzD;IACA;IACIL,WAAW,CAACK,KAAK,GAAG,CAAC,GAAGL,WAAW,CAACK,KAAK,EAAEW,UAAU,CAAC;IACtDlB,KAAK,CAACwB,QAAQ,CAACN,UAAU,EAAEhB,WAAW,CAACK,KAAK,CAAC;EACjD,CAAG;EACD,MAAMsC,YAAY,GAAG,MAAOpD,IAAI,IAAK;IACnC,MAAMyB,UAAU,GAAGzB,IAAI,YAAYqD,IAAI,GAAGzC,OAAO,CAACZ,IAAI,CAAC,GAAGA,IAAI;IAC9D,IAAI,CAACyB,UAAU,EACb6B,UAAU,CAACxD,KAAK,EAAE,8BAA8B,CAAC;IACnD,MAAMyD,QAAQ,GAAIC,KAAK,IAAK;MAC1BvC,KAAK,CAACuC,KAAK,CAAC;MACZhC,UAAU,CAACgC,KAAK,CAAC;MACjBjD,KAAK,CAACkD,QAAQ,CAACD,KAAK,EAAE/C,WAAW,CAACK,KAAK,CAAC;MACxCf,mBAAmB,CAACyD,KAAK,CAAC;IAChC,CAAK;IACD,IAAIjD,KAAK,CAACmD,YAAY,EAAE;MACtB,MAAMC,MAAM,GAAG,MAAMpD,KAAK,CAACmD,YAAY,CAACjC,UAAU,EAAEhB,WAAW,CAACK,KAAK,CAAC;MACtE,IAAI6C,MAAM,KAAK,KAAK,EAClBJ,QAAQ,CAAC9B,UAAU,CAAC;IAC5B,CAAK,MAAM;MACL8B,QAAQ,CAAC9B,UAAU,CAAC;IAC1B;EACA,CAAG;EACD,SAASmC,MAAMA,CAAA,EAAG;IAChBnD,WAAW,CAACK,KAAK,CAACM,MAAM,CAAC,CAAC;MAAEG;IAAM,CAAE,KAAKA,MAAM,KAAK,OAAO,CAAC,CAACsC,OAAO,CAAC,CAAC;MAAEd;IAAG,CAAE,KAAK;MAChF,IAAI9C,EAAE;MACN,OAAO8C,GAAG,KAAK,CAAC9C,EAAE,GAAGO,SAAS,CAACM,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGb,EAAE,CAAC6D,MAAM,CAACf,GAAG,CAAC,CAAC;IAC9E,CAAK,CAAC;EACN;EACEgB,KAAK,CAAC,MAAMxD,KAAK,CAACyC,QAAQ,EAAGgB,GAAG,IAAK;IACnC,IAAIA,GAAG,KAAK,cAAc,IAAIA,GAAG,KAAK,SAAS,EAAE;MAC/C;IACN;IACIvD,WAAW,CAACK,KAAK,GAAGL,WAAW,CAACK,KAAK,CAACmD,GAAG,CAAEjE,IAAI,IAAK;MAClD,MAAM;QAAE+C,GAAG;QAAE7C;MAAG,CAAE,GAAGF,IAAI;MACzB,IAAI,CAACE,GAAG,IAAI6C,GAAG,EAAE;QACf,IAAI;UACF/C,IAAI,CAACE,GAAG,GAAGE,GAAG,CAAC6C,eAAe,CAACF,GAAG,CAAC;QAC7C,CAAS,CAAC,OAAOpB,GAAG,EAAE;UACZpB,KAAK,CAACuB,OAAO,CAACH,GAAG,EAAE3B,IAAI,EAAES,WAAW,CAACK,KAAK,CAAC;QACrD;MACA;MACM,OAAOd,IAAI;IACjB,CAAK,CAAC;EACN,CAAG,CAAC;EACF+D,KAAK,CAACtD,WAAW,EAAGyD,KAAK,IAAK;IAC5B,KAAK,MAAMlE,IAAI,IAAIkE,KAAK,EAAE;MACxBlE,IAAI,CAACgB,GAAG,KAAKhB,IAAI,CAACgB,GAAG,GAAG4B,SAAS,EAAE,CAAC;MACpC5C,IAAI,CAACuB,MAAM,KAAKvB,IAAI,CAACuB,MAAM,GAAG,SAAS,CAAC;IAC9C;EACA,CAAG,EAAE;IAAE4C,SAAS,EAAE,IAAI;IAAEC,IAAI,EAAE;EAAI,CAAE,CAAC;EACnC,OAAO;IACL3D,WAAW;IACXQ,KAAK;IACLC,UAAU;IACVQ,WAAW;IACXM,cAAc;IACdU,WAAW;IACXH,aAAa;IACba,YAAY;IACZQ,MAAM;IACN7D;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}