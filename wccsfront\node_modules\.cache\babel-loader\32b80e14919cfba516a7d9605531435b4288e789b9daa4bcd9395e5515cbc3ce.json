{"ast": null, "code": "import isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n  reIsPlainProp = /^\\w*$/;\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' || value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) || object != null && value in Object(object);\n}\nexport default isKey;", "map": {"version": 3, "names": ["isArray", "isSymbol", "reIsDeepProp", "reIsPlainProp", "is<PERSON>ey", "value", "object", "type", "test", "Object"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_isKey.js"], "sourcesContent": ["import isArray from './isArray.js';\nimport isSymbol from './isSymbol.js';\n\n/** Used to match property names within property paths. */\nvar reIsDeepProp = /\\.|\\[(?:[^[\\]]*|([\"'])(?:(?!\\1)[^\\\\]|\\\\.)*?\\1)\\]/,\n    reIsPlainProp = /^\\w*$/;\n\n/**\n * Checks if `value` is a property name and not a property path.\n *\n * @private\n * @param {*} value The value to check.\n * @param {Object} [object] The object to query keys on.\n * @returns {boolean} Returns `true` if `value` is a property name, else `false`.\n */\nfunction isKey(value, object) {\n  if (isArray(value)) {\n    return false;\n  }\n  var type = typeof value;\n  if (type == 'number' || type == 'symbol' || type == 'boolean' ||\n      value == null || isSymbol(value)) {\n    return true;\n  }\n  return reIsPlainProp.test(value) || !reIsDeepProp.test(value) ||\n    (object != null && value in Object(object));\n}\n\nexport default isKey;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,cAAc;AAClC,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,YAAY,GAAG,kDAAkD;EACjEC,aAAa,GAAG,OAAO;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5B,IAAIN,OAAO,CAACK,KAAK,CAAC,EAAE;IAClB,OAAO,KAAK;EACd;EACA,IAAIE,IAAI,GAAG,OAAOF,KAAK;EACvB,IAAIE,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,SAAS,IACzDF,KAAK,IAAI,IAAI,IAAIJ,QAAQ,CAACI,KAAK,CAAC,EAAE;IACpC,OAAO,IAAI;EACb;EACA,OAAOF,aAAa,CAACK,IAAI,CAACH,KAAK,CAAC,IAAI,CAACH,YAAY,CAACM,IAAI,CAACH,KAAK,CAAC,IAC1DC,MAAM,IAAI,IAAI,IAAID,KAAK,IAAII,MAAM,CAACH,MAAM,CAAE;AAC/C;AAEA,eAAeF,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}