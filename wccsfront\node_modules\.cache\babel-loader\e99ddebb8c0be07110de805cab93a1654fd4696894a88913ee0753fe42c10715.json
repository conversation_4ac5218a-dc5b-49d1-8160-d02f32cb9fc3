{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport { isVNode, createVNode, render } from 'vue';\nimport NotificationConstructor from './notification2.mjs';\nimport { notificationTypes } from './notification.mjs';\nimport { isClient } from '@vueuse/core';\nimport { isString, isFunction } from '@vue/shared';\nimport { isElement, isUndefined } from '../../../utils/types.mjs';\nimport { debugWarn } from '../../../utils/error.mjs';\nconst notifications = {\n  \"top-left\": [],\n  \"top-right\": [],\n  \"bottom-left\": [],\n  \"bottom-right\": []\n};\nconst GAP_SIZE = 16;\nlet seed = 1;\nconst notify = function (options = {}, context) {\n  if (!isClient) return {\n    close: () => void 0\n  };\n  if (isString(options) || isVNode(options)) {\n    options = {\n      message: options\n    };\n  }\n  const position = options.position || \"top-right\";\n  let verticalOffset = options.offset || 0;\n  notifications[position].forEach(({\n    vm: vm2\n  }) => {\n    var _a;\n    verticalOffset += (((_a = vm2.el) == null ? void 0 : _a.offsetHeight) || 0) + GAP_SIZE;\n  });\n  verticalOffset += GAP_SIZE;\n  const id = `notification_${seed++}`;\n  const userOnClose = options.onClose;\n  const props = {\n    ...options,\n    offset: verticalOffset,\n    id,\n    onClose: () => {\n      close(id, position, userOnClose);\n    }\n  };\n  let appendTo = document.body;\n  if (isElement(options.appendTo)) {\n    appendTo = options.appendTo;\n  } else if (isString(options.appendTo)) {\n    appendTo = document.querySelector(options.appendTo);\n  }\n  if (!isElement(appendTo)) {\n    debugWarn(\"ElNotification\", \"the appendTo option is not an HTMLElement. Falling back to document.body.\");\n    appendTo = document.body;\n  }\n  const container = document.createElement(\"div\");\n  const vm = createVNode(NotificationConstructor, props, isFunction(props.message) ? props.message : isVNode(props.message) ? () => props.message : null);\n  vm.appContext = isUndefined(context) ? notify._context : context;\n  vm.props.onDestroy = () => {\n    render(null, container);\n  };\n  render(vm, container);\n  notifications[position].push({\n    vm\n  });\n  appendTo.appendChild(container.firstElementChild);\n  return {\n    close: () => {\n      vm.component.exposed.visible.value = false;\n    }\n  };\n};\nnotificationTypes.forEach(type => {\n  notify[type] = (options = {}, appContext) => {\n    if (isString(options) || isVNode(options)) {\n      options = {\n        message: options\n      };\n    }\n    return notify({\n      ...options,\n      type\n    }, appContext);\n  };\n});\nfunction close(id, position, userOnClose) {\n  const orientedNotifications = notifications[position];\n  const idx = orientedNotifications.findIndex(({\n    vm: vm2\n  }) => {\n    var _a;\n    return ((_a = vm2.component) == null ? void 0 : _a.props.id) === id;\n  });\n  if (idx === -1) return;\n  const {\n    vm\n  } = orientedNotifications[idx];\n  if (!vm) return;\n  userOnClose == null ? void 0 : userOnClose(vm);\n  const removedHeight = vm.el.offsetHeight;\n  const verticalPos = position.split(\"-\")[0];\n  orientedNotifications.splice(idx, 1);\n  const len = orientedNotifications.length;\n  if (len < 1) return;\n  for (let i = idx; i < len; i++) {\n    const {\n      el,\n      component\n    } = orientedNotifications[i].vm;\n    const pos = Number.parseInt(el.style[verticalPos], 10) - removedHeight - GAP_SIZE;\n    component.props.offset = pos;\n  }\n}\nfunction closeAll() {\n  for (const orientedNotifications of Object.values(notifications)) {\n    orientedNotifications.forEach(({\n      vm\n    }) => {\n      vm.component.exposed.visible.value = false;\n    });\n  }\n}\nnotify.closeAll = closeAll;\nnotify._context = null;\nexport { close, closeAll, notify as default };", "map": {"version": 3, "names": ["notifications", "GAP_SIZE", "seed", "notify", "options", "context", "isClient", "close", "isString", "isVNode", "message", "position", "verticalOffset", "offset", "for<PERSON>ach", "vm", "vm2", "_a", "el", "offsetHeight", "id", "userOnClose", "onClose", "props", "appendTo", "document", "body", "isElement", "querySelector", "debugWarn", "container", "createElement", "createVNode", "NotificationConstructor", "isFunction", "appContext", "isUndefined", "_context", "onDestroy", "render", "push", "append<PERSON><PERSON><PERSON>", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "component", "exposed", "visible", "value", "notificationTypes", "type", "orientedNotifications", "idx", "findIndex", "removedHeight", "verticalPos", "split", "splice", "len", "length", "i", "pos", "Number", "parseInt", "style", "closeAll", "Object", "values"], "sources": ["../../../../../../packages/components/notification/src/notify.ts"], "sourcesContent": ["import { createVNode, isVNode, render } from 'vue'\nimport {\n  debugWarn,\n  isClient,\n  isElement,\n  isFunction,\n  isString,\n  isUndefined,\n} from '@element-plus/utils'\nimport NotificationConstructor from './notification.vue'\nimport { notificationTypes } from './notification'\n\nimport type { Ref, VNode } from 'vue'\nimport type {\n  NotificationOptions,\n  NotificationProps,\n  NotificationQueue,\n  Notify,\n  NotifyFn,\n} from './notification'\n\n// This should be a queue but considering there were `non-autoclosable` notifications.\nconst notifications: Record<\n  NotificationOptions['position'],\n  NotificationQueue\n> = {\n  'top-left': [],\n  'top-right': [],\n  'bottom-left': [],\n  'bottom-right': [],\n}\n\n// the gap size between each notification\nconst GAP_SIZE = 16\nlet seed = 1\n\nconst notify: NotifyFn & Partial<Notify> = function (options = {}, context) {\n  if (!isClient) return { close: () => undefined }\n\n  if (isString(options) || isVNode(options)) {\n    options = { message: options }\n  }\n\n  const position = options.position || 'top-right'\n\n  let verticalOffset = options.offset || 0\n  notifications[position].forEach(({ vm }) => {\n    verticalOffset += (vm.el?.offsetHeight || 0) + GAP_SIZE\n  })\n  verticalOffset += GAP_SIZE\n\n  const id = `notification_${seed++}`\n  const userOnClose = options.onClose\n  const props: Partial<NotificationProps> = {\n    ...options,\n    offset: verticalOffset,\n    id,\n    onClose: () => {\n      close(id, position, userOnClose)\n    },\n  }\n\n  let appendTo: HTMLElement | null = document.body\n  if (isElement(options.appendTo)) {\n    appendTo = options.appendTo\n  } else if (isString(options.appendTo)) {\n    appendTo = document.querySelector(options.appendTo)\n  }\n\n  // should fallback to default value with a warning\n  if (!isElement(appendTo)) {\n    debugWarn(\n      'ElNotification',\n      'the appendTo option is not an HTMLElement. Falling back to document.body.'\n    )\n    appendTo = document.body\n  }\n\n  const container = document.createElement('div')\n\n  const vm = createVNode(\n    NotificationConstructor,\n    props,\n    isFunction(props.message)\n      ? props.message\n      : isVNode(props.message)\n      ? () => props.message\n      : null\n  )\n  vm.appContext = isUndefined(context) ? notify._context : context\n\n  // clean notification element preventing mem leak\n  vm.props!.onDestroy = () => {\n    render(null, container)\n  }\n\n  // instances will remove this item when close function gets called. So we do not need to worry about it.\n  render(vm, container)\n  notifications[position].push({ vm })\n  appendTo.appendChild(container.firstElementChild!)\n\n  return {\n    // instead of calling the onClose function directly, setting this value so that we can have the full lifecycle\n    // for out component, so that all closing steps will not be skipped.\n    close: () => {\n      ;(vm.component!.exposed as { visible: Ref<boolean> }).visible.value =\n        false\n    },\n  }\n}\nnotificationTypes.forEach((type) => {\n  notify[type] = (options = {}, appContext) => {\n    if (isString(options) || isVNode(options)) {\n      options = {\n        message: options,\n      }\n    }\n    return notify({ ...options, type }, appContext)\n  }\n})\n\n/**\n * This function gets called when user click `x` button or press `esc` or the time reached its limitation.\n * Emitted by transition@before-leave event so that we can fetch the current notification.offsetHeight, if this was called\n * by @after-leave the DOM element will be removed from the page thus we can no longer fetch the offsetHeight.\n * @param {String} id notification id to be closed\n * @param {Position} position the positioning strategy\n * @param {Function} userOnClose the callback called when close passed by user\n */\nexport function close(\n  id: string,\n  position: NotificationOptions['position'],\n  userOnClose?: (vm: VNode) => void\n): void {\n  // maybe we can store the index when inserting the vm to notification list.\n  const orientedNotifications = notifications[position]\n  const idx = orientedNotifications.findIndex(\n    ({ vm }) => vm.component?.props.id === id\n  )\n  if (idx === -1) return\n  const { vm } = orientedNotifications[idx]\n  if (!vm) return\n  // calling user's on close function before notification gets removed from DOM.\n  userOnClose?.(vm)\n\n  // note that this is called @before-leave, that's why we were able to fetch this property.\n  const removedHeight = vm.el!.offsetHeight\n  const verticalPos = position.split('-')[0]\n  orientedNotifications.splice(idx, 1)\n  const len = orientedNotifications.length\n  if (len < 1) return\n  // starting from the removing item.\n  for (let i = idx; i < len; i++) {\n    // new position equals the current offsetTop minus removed height plus 16px(the gap size between each item)\n    const { el, component } = orientedNotifications[i].vm\n    const pos =\n      Number.parseInt(el!.style[verticalPos], 10) - removedHeight - GAP_SIZE\n    component!.props.offset = pos\n  }\n}\n\nexport function closeAll(): void {\n  // loop through all directions, close them at once.\n  for (const orientedNotifications of Object.values(notifications)) {\n    orientedNotifications.forEach(({ vm }) => {\n      // same as the previous close method, we'd like to make sure lifecycle gets handle properly.\n      ;(vm.component!.exposed as { visible: Ref<boolean> }).visible.value =\n        false\n    })\n  }\n}\n\nnotify.closeAll = closeAll\nnotify._context = null\n\nexport default notify as Notify\n"], "mappings": ";;;;;;;;;;AAWA,MAAMA,aAAa,GAAG;EACpB,UAAU,EAAE,EAAE;EACd,WAAW,EAAE,EAAE;EACf,aAAa,EAAE,EAAE;EACjB,cAAc,EAAE;AAClB,CAAC;AACD,MAAMC,QAAQ,GAAG,EAAE;AACnB,IAAIC,IAAI,GAAG,CAAC;AACP,MAACC,MAAM,GAAG,SAAAA,CAASC,OAAO,GAAG,EAAE,EAAEC,OAAO,EAAE;EAC7C,IAAI,CAACC,QAAQ,EACX,OAAO;IAAEC,KAAK,EAAEA,CAAA,KAAM,KAAK;EAAC,CAAE;EAChC,IAAIC,QAAQ,CAACJ,OAAO,CAAC,IAAIK,OAAO,CAACL,OAAO,CAAC,EAAE;IACzCA,OAAO,GAAG;MAAEM,OAAO,EAAEN;IAAO,CAAE;EAClC;EACE,MAAMO,QAAQ,GAAGP,OAAO,CAACO,QAAQ,IAAI,WAAW;EAChD,IAAIC,cAAc,GAAGR,OAAO,CAACS,MAAM,IAAI,CAAC;EACxCb,aAAa,CAACW,QAAQ,CAAC,CAACG,OAAO,CAAC,CAAC;IAAEC,EAAE,EAAEC;EAAG,CAAE,KAAK;IAC/C,IAAIC,EAAE;IACNL,cAAc,IAAI,CAAC,CAAC,CAACK,EAAE,GAAGD,GAAG,CAACE,EAAE,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,YAAY,KAAK,CAAC,IAAIlB,QAAQ;EAC1F,CAAG,CAAC;EACFW,cAAc,IAAIX,QAAQ;EAC1B,MAAMmB,EAAE,GAAG,gBAAgBlB,IAAI,EAAE,EAAE;EACnC,MAAMmB,WAAW,GAAGjB,OAAO,CAACkB,OAAO;EACnC,MAAMC,KAAK,GAAG;IACZ,GAAGnB,OAAO;IACVS,MAAM,EAAED,cAAc;IACtBQ,EAAE;IACFE,OAAO,EAAEA,CAAA,KAAM;MACbf,KAAK,CAACa,EAAE,EAAET,QAAQ,EAAEU,WAAW,CAAC;IACtC;EACA,CAAG;EACD,IAAIG,QAAQ,GAAGC,QAAQ,CAACC,IAAI;EAC5B,IAAIC,SAAS,CAACvB,OAAO,CAACoB,QAAQ,CAAC,EAAE;IAC/BA,QAAQ,GAAGpB,OAAO,CAACoB,QAAQ;EAC/B,CAAG,MAAM,IAAIhB,QAAQ,CAACJ,OAAO,CAACoB,QAAQ,CAAC,EAAE;IACrCA,QAAQ,GAAGC,QAAQ,CAACG,aAAa,CAACxB,OAAO,CAACoB,QAAQ,CAAC;EACvD;EACE,IAAI,CAACG,SAAS,CAACH,QAAQ,CAAC,EAAE;IACxBK,SAAS,CAAC,gBAAgB,EAAE,2EAA2E,CAAC;IACxGL,QAAQ,GAAGC,QAAQ,CAACC,IAAI;EAC5B;EACE,MAAMI,SAAS,GAAGL,QAAQ,CAACM,aAAa,CAAC,KAAK,CAAC;EAC/C,MAAMhB,EAAE,GAAGiB,WAAW,CAACC,uBAAuB,EAAEV,KAAK,EAAEW,UAAU,CAACX,KAAK,CAACb,OAAO,CAAC,GAAGa,KAAK,CAACb,OAAO,GAAGD,OAAO,CAACc,KAAK,CAACb,OAAO,CAAC,GAAG,MAAMa,KAAK,CAACb,OAAO,GAAG,IAAI,CAAC;EACvJK,EAAE,CAACoB,UAAU,GAAGC,WAAW,CAAC/B,OAAO,CAAC,GAAGF,MAAM,CAACkC,QAAQ,GAAGhC,OAAO;EAChEU,EAAE,CAACQ,KAAK,CAACe,SAAS,GAAG,MAAM;IACzBC,MAAM,CAAC,IAAI,EAAET,SAAS,CAAC;EAC3B,CAAG;EACDS,MAAM,CAACxB,EAAE,EAAEe,SAAS,CAAC;EACrB9B,aAAa,CAACW,QAAQ,CAAC,CAAC6B,IAAI,CAAC;IAAEzB;EAAE,CAAE,CAAC;EACpCS,QAAQ,CAACiB,WAAW,CAACX,SAAS,CAACY,iBAAiB,CAAC;EACjD,OAAO;IACLnC,KAAK,EAAEA,CAAA,KAAM;MAEXQ,EAAE,CAAC4B,SAAS,CAACC,OAAO,CAACC,OAAO,CAACC,KAAK,GAAG,KAAK;IAChD;EACA,CAAG;AACH;AACAC,iBAAiB,CAACjC,OAAO,CAAEkC,IAAI,IAAK;EAClC7C,MAAM,CAAC6C,IAAI,CAAC,GAAG,CAAC5C,OAAO,GAAG,EAAE,EAAE+B,UAAU,KAAK;IAC3C,IAAI3B,QAAQ,CAACJ,OAAO,CAAC,IAAIK,OAAO,CAACL,OAAO,CAAC,EAAE;MACzCA,OAAO,GAAG;QACRM,OAAO,EAAEN;MACjB,CAAO;IACP;IACI,OAAOD,MAAM,CAAC;MAAE,GAAGC,OAAO;MAAE4C;IAAI,CAAE,EAAEb,UAAU,CAAC;EACnD,CAAG;AACH,CAAC,CAAC;AACK,SAAS5B,KAAKA,CAACa,EAAE,EAAET,QAAQ,EAAEU,WAAW,EAAE;EAC/C,MAAM4B,qBAAqB,GAAGjD,aAAa,CAACW,QAAQ,CAAC;EACrD,MAAMuC,GAAG,GAAGD,qBAAqB,CAACE,SAAS,CAAC,CAAC;IAAEpC,EAAE,EAAEC;EAAG,CAAE,KAAK;IAC3D,IAAIC,EAAE;IACN,OAAO,CAAC,CAACA,EAAE,GAAGD,GAAG,CAAC2B,SAAS,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG1B,EAAE,CAACM,KAAK,CAACH,EAAE,MAAMA,EAAE;EACvE,CAAG,CAAC;EACF,IAAI8B,GAAG,KAAK,CAAC,CAAC,EACZ;EACF,MAAM;IAAEnC;EAAE,CAAE,GAAGkC,qBAAqB,CAACC,GAAG,CAAC;EACzC,IAAI,CAACnC,EAAE,EACL;EACFM,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACN,EAAE,CAAC;EAC9C,MAAMqC,aAAa,GAAGrC,EAAE,CAACG,EAAE,CAACC,YAAY;EACxC,MAAMkC,WAAW,GAAG1C,QAAQ,CAAC2C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC1CL,qBAAqB,CAACM,MAAM,CAACL,GAAG,EAAE,CAAC,CAAC;EACpC,MAAMM,GAAG,GAAGP,qBAAqB,CAACQ,MAAM;EACxC,IAAID,GAAG,GAAG,CAAC,EACT;EACF,KAAK,IAAIE,CAAC,GAAGR,GAAG,EAAEQ,CAAC,GAAGF,GAAG,EAAEE,CAAC,EAAE,EAAE;IAC9B,MAAM;MAAExC,EAAE;MAAEyB;IAAS,CAAE,GAAGM,qBAAqB,CAACS,CAAC,CAAC,CAAC3C,EAAE;IACrD,MAAM4C,GAAG,GAAGC,MAAM,CAACC,QAAQ,CAAC3C,EAAE,CAAC4C,KAAK,CAACT,WAAW,CAAC,EAAE,EAAE,CAAC,GAAGD,aAAa,GAAGnD,QAAQ;IACjF0C,SAAS,CAACpB,KAAK,CAACV,MAAM,GAAG8C,GAAG;EAChC;AACA;AACO,SAASI,QAAQA,CAAA,EAAG;EACzB,KAAK,MAAMd,qBAAqB,IAAIe,MAAM,CAACC,MAAM,CAACjE,aAAa,CAAC,EAAE;IAChEiD,qBAAqB,CAACnC,OAAO,CAAC,CAAC;MAAEC;IAAE,CAAE,KAAK;MAExCA,EAAE,CAAC4B,SAAS,CAACC,OAAO,CAACC,OAAO,CAACC,KAAK,GAAG,KAAK;IAChD,CAAK,CAAC;EACN;AACA;AACA3C,MAAM,CAAC4D,QAAQ,GAAGA,QAAQ;AAC1B5D,MAAM,CAACkC,QAAQ,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}