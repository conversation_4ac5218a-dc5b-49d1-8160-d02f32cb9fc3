{"ast": null, "code": "import { defineComponent, openBlock, createElementBlock, normalizeClass, unref, createBlock, createCommentVNode } from 'vue';\nimport { PictureFilled } from '@element-plus/icons-vue';\nimport { skeletonItemProps } from './skeleton-item.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElSkeletonItem\"\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: skeletonItemProps,\n  setup(__props) {\n    const ns = useNamespace(\"skeleton\");\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        class: normalizeClass([unref(ns).e(\"item\"), unref(ns).e(_ctx.variant)])\n      }, [_ctx.variant === \"image\" ? (openBlock(), createBlock(unref(PictureFilled), {\n        key: 0\n      })) : createCommentVNode(\"v-if\", true)], 2);\n    };\n  }\n});\nvar SkeletonItem = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"skeleton-item.vue\"]]);\nexport { SkeletonItem as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace"], "sources": ["../../../../../../packages/components/skeleton/src/skeleton-item.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.e('item'), ns.e(variant)]\">\n    <picture-filled v-if=\"variant === 'image'\" />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport { PictureFilled } from '@element-plus/icons-vue'\nimport { skeletonItemProps } from './skeleton-item'\n\ndefineOptions({\n  name: 'ElSkeletonItem',\n})\ndefineProps(skeletonItemProps)\nconst ns = useNamespace('skeleton')\n</script>\n"], "mappings": ";;;;;mCAWc;EACZA,IAAM;AACR;;;;;IAEM,MAAAC,EAAA,GAAKC,YAAA,CAAa,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}