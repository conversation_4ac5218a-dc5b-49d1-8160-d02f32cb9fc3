{"ast": null, "code": "import { defineComponent, inject, computed, toRef, openBlock, createElementBlock, mergeProps, unref, createElementVNode, normalizeClass, normalizeStyle, createCommentVNode } from 'vue';\nimport { maskProps } from './mask2.mjs';\nimport { tourKey } from './helper.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useLockscreen } from '../../../hooks/use-lockscreen/index.mjs';\nconst __default__ = defineComponent({\n  name: \"ElTourMask\",\n  inheritAttrs: false\n});\nconst _sfc_main = /* @__PURE__ */defineComponent({\n  ...__default__,\n  props: maskProps,\n  setup(__props) {\n    const props = __props;\n    const {\n      ns\n    } = inject(tourKey);\n    const radius = computed(() => {\n      var _a, _b;\n      return (_b = (_a = props.pos) == null ? void 0 : _a.radius) != null ? _b : 2;\n    });\n    const roundInfo = computed(() => {\n      const v = radius.value;\n      const baseInfo = `a${v},${v} 0 0 1`;\n      return {\n        topRight: `${baseInfo} ${v},${v}`,\n        bottomRight: `${baseInfo} ${-v},${v}`,\n        bottomLeft: `${baseInfo} ${-v},${-v}`,\n        topLeft: `${baseInfo} ${v},${-v}`\n      };\n    });\n    const path = computed(() => {\n      const width = window.innerWidth;\n      const height = window.innerHeight;\n      const info = roundInfo.value;\n      const _path = `M${width},0 L0,0 L0,${height} L${width},${height} L${width},0 Z`;\n      const _radius = radius.value;\n      return props.pos ? `${_path} M${props.pos.left + _radius},${props.pos.top} h${props.pos.width - _radius * 2} ${info.topRight} v${props.pos.height - _radius * 2} ${info.bottomRight} h${-props.pos.width + _radius * 2} ${info.bottomLeft} v${-props.pos.height + _radius * 2} ${info.topLeft} z` : _path;\n    });\n    const pathStyle = computed(() => {\n      return {\n        fill: props.fill,\n        pointerEvents: \"auto\",\n        cursor: \"auto\"\n      };\n    });\n    useLockscreen(toRef(props, \"visible\"), {\n      ns\n    });\n    return (_ctx, _cache) => {\n      return _ctx.visible ? (openBlock(), createElementBlock(\"div\", mergeProps({\n        key: 0,\n        class: unref(ns).e(\"mask\"),\n        style: {\n          position: \"fixed\",\n          left: 0,\n          right: 0,\n          top: 0,\n          bottom: 0,\n          zIndex: _ctx.zIndex,\n          pointerEvents: _ctx.pos && _ctx.targetAreaClickable ? \"none\" : \"auto\"\n        }\n      }, _ctx.$attrs), [(openBlock(), createElementBlock(\"svg\", {\n        style: {\n          width: \"100%\",\n          height: \"100%\"\n        }\n      }, [createElementVNode(\"path\", {\n        class: normalizeClass(unref(ns).e(\"hollow\")),\n        style: normalizeStyle(unref(pathStyle)),\n        d: unref(path)\n      }, null, 14, [\"d\"])]))], 16)) : createCommentVNode(\"v-if\", true);\n    };\n  }\n});\nvar ElTourMask = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"mask.vue\"]]);\nexport { ElTourMask as default };", "map": {"version": 3, "names": ["name", "inheritAttrs", "ns", "inject", "tourKey", "radius", "computed", "_a", "_b", "props", "pos", "roundInfo", "v", "value", "baseInfo", "topRight", "bottomRight", "bottomLeft", "topLeft", "path", "width", "window", "innerWidth", "height", "innerHeight", "info", "_path", "_radius", "left", "top", "pathStyle", "fill", "pointerEvents", "cursor"], "sources": ["../../../../../../packages/components/tour/src/mask.vue"], "sourcesContent": ["<template>\n  <div\n    v-if=\"visible\"\n    :class=\"ns.e('mask')\"\n    :style=\"({\n    position: 'fixed',\n    left: 0,\n    right: 0,\n    top: 0,\n    bottom: 0,\n    zIndex,\n    pointerEvents: pos && targetAreaClickable ? 'none' : 'auto',\n  } as any)\"\n    v-bind=\"$attrs\"\n  >\n    <svg\n      :style=\"{\n        width: '100%',\n        height: '100%',\n      }\"\n    >\n      <path :class=\"ns.e('hollow')\" :style=\"pathStyle\" :d=\"path\" />\n    </svg>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject, toRef } from 'vue'\nimport { useLockscreen } from '@element-plus/hooks'\nimport { maskProps } from './mask'\nimport { tourKey } from './helper'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElTourMask',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(maskProps)\n\nconst { ns } = inject(tourKey)!\nconst radius = computed(() => props.pos?.radius ?? 2)\nconst roundInfo = computed(() => {\n  const v = radius.value\n  const baseInfo = `a${v},${v} 0 0 1`\n  return {\n    topRight: `${baseInfo} ${v},${v}`,\n    bottomRight: `${baseInfo} ${-v},${v}`,\n    bottomLeft: `${baseInfo} ${-v},${-v}`,\n    topLeft: `${baseInfo} ${v},${-v}`,\n  }\n})\n\nconst path = computed(() => {\n  const width = window.innerWidth\n  const height = window.innerHeight\n  const info = roundInfo.value\n  const _path = `M${width},0 L0,0 L0,${height} L${width},${height} L${width},0 Z`\n  const _radius = radius.value\n  return props.pos\n    ? `${_path} M${props.pos.left + _radius},${props.pos.top} h${\n        props.pos.width - _radius * 2\n      } ${info.topRight} v${props.pos.height - _radius * 2} ${\n        info.bottomRight\n      } h${-props.pos.width + _radius * 2} ${info.bottomLeft} v${\n        -props.pos.height + _radius * 2\n      } ${info.topLeft} z`\n    : _path\n})\n\nconst pathStyle = computed<CSSProperties>(() => {\n  return {\n    fill: props.fill,\n    pointerEvents: 'auto',\n    cursor: 'auto',\n  }\n})\n\nuseLockscreen(toRef(props, 'visible'), {\n  ns,\n})\n</script>\n"], "mappings": ";;;;;mCAiCc;EACZA,IAAM;EACNC,YAAc;AAChB;;;;;;IAIA,MAAM;MAAEC;IAAA,CAAO,GAAAC,MAAA,CAAOC,OAAO;IAC7B,MAAMC,MAAA,GAASC,QAAS,OAAM;MACxB,IAAAC,EAAA,EAAAC,EAAA;MACJ,QAAAA,EAAU,GAAO,CAAAD,EAAA,GAAAE,KAAA,CAAAC,GAAA,qBAAAH,EAAA,CAAAF,MAAA,YAAAG,EAAA;IACjB;IACO,MAAAG,SAAA,GAAAL,QAAA;MAAA,MAAAM,CAAA,GAAAP,MACQ,CAAAQ,KAAA;MAAkB,MAAAC,QAAA,GACf,IAAAF,CAAA,IAAQA,CAAA;MACxB;QACAG,QAAA,EAAY,GAAAD,QAAA,IAAaF,CAAA,IAAKA,CAAC;QACjCI,WAAA,KAAAF,QAAA,KAAAF,CAAA,IAAAA,CAAA;QACDK,UAAA,KAAAH,QAAA,KAAAF,CAAA,KAAAA,CAAA;QAEKM,OAAA,EAAO,GAAAJ,QAAe,IAAAF,CAAA,KAAAA,CAAA;MAC1B;IACA;IACA,MAAAO,IAAA,GAAAb,QAAuB;MACjB,MAAAc,KAAA,GAAQC,MAAI,CAAAC,UAAmB;MACrC,MAAMC,MAAA,GAAAF,MAAiB,CAAAG,WAAA;MAChB,MAAAC,IAAA,GAAMd,SACN,CAAAE,KAAA;MAQR,MAAAa,KAAA,OAAAN,KAAA,cAAAG,MAAA,KAAAH,KAAA,IAAAG,MAAA,KAAAH,KAAA;MAEK,MAAAO,OAAA,GAAYtB,MAAA,CAAAQ,KAA8B;MACvC,OAAAJ,KAAA,CAAAC,GAAA,MAAAgB,KAAA,KAAAjB,KAAA,CAAAC,GAAA,CAAAkB,IAAA,GAAAD,OAAA,IAAAlB,KAAA,CAAAC,GAAA,CAAAmB,GAAA,KAAApB,KAAA,CAAAC,GAAA,CAAAU,KAAA,GAAAO,OAAA,QAAAF,IAAA,CAAAV,QAAA,KAAAN,KAAA,CAAAC,GAAA,CAAAa,MAAA,GAAAI,OAAA,QAAAF,IAAA,CAAAT,WAAA,MAAAP,KAAA,CAAAC,GAAA,CAAAU,KAAA,GAAAO,OAAA,QAAAF,IAAA,CAAAR,UAAA,MAAAR,KAAA,CAAAC,GAAA,CAAAa,MAAA,GAAAI,OAAA,QAAAF,IAAA,CAAAP,OAAA,OAAAQ,KAAA;IAAA;IACO,MACGI,SAAA,GAAAxB,QAAA;MAAA,OACP;QACVyB,IAAA,EAAAtB,KAAA,CAAAsB,IAAA;QACDC,aAAA;QAEaC,MAAA;MAAyB,CACrC;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}