{"ast": null, "code": "import eq from './eq.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used by `_.defaults` to customize its `_.assignIn` use to assign properties\n * of source objects to the destination object for all destination properties\n * that resolve to `undefined`.\n *\n * @private\n * @param {*} objValue The destination value.\n * @param {*} srcValue The source value.\n * @param {string} key The key of the property to assign.\n * @param {Object} object The parent object of `objValue`.\n * @returns {*} Returns the value to assign.\n */\nfunction customDefaultsAssignIn(objValue, srcValue, key, object) {\n  if (objValue === undefined || eq(objValue, objectProto[key]) && !hasOwnProperty.call(object, key)) {\n    return srcValue;\n  }\n  return objValue;\n}\nexport default customDefaultsAssignIn;", "map": {"version": 3, "names": ["eq", "objectProto", "Object", "prototype", "hasOwnProperty", "customDefaultsAssignIn", "objValue", "srcValue", "key", "object", "undefined", "call"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_customDefaultsAssignIn.js"], "sourcesContent": ["import eq from './eq.js';\n\n/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Used by `_.defaults` to customize its `_.assignIn` use to assign properties\n * of source objects to the destination object for all destination properties\n * that resolve to `undefined`.\n *\n * @private\n * @param {*} objValue The destination value.\n * @param {*} srcValue The source value.\n * @param {string} key The key of the property to assign.\n * @param {Object} object The parent object of `objValue`.\n * @returns {*} Returns the value to assign.\n */\nfunction customDefaultsAssignIn(objValue, srcValue, key, object) {\n  if (objValue === undefined ||\n      (eq(objValue, objectProto[key]) && !hasOwnProperty.call(object, key))) {\n    return srcValue;\n  }\n  return objValue;\n}\n\nexport default customDefaultsAssignIn;\n"], "mappings": "AAAA,OAAOA,EAAE,MAAM,SAAS;;AAExB;AACA,IAAIC,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,MAAM,EAAE;EAC/D,IAAIH,QAAQ,KAAKI,SAAS,IACrBV,EAAE,CAACM,QAAQ,EAAEL,WAAW,CAACO,GAAG,CAAC,CAAC,IAAI,CAACJ,cAAc,CAACO,IAAI,CAACF,MAAM,EAAED,GAAG,CAAE,EAAE;IACzE,OAAOD,QAAQ;EACjB;EACA,OAAOD,QAAQ;AACjB;AAEA,eAAeD,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}