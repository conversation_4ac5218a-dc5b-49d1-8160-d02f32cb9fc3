{"ast": null, "code": "import { defineComponent, ref, inject, getCurrentInstance, onMounted, watch, onBeforeUnmount, computed, reactive, openBlock, createElementBlock, normalizeStyle, unref, normalizeClass, createCommentVNode, createElementVNode, renderSlot, createBlock, withCtx, resolveDynamicComponent, createVNode, toDisplayString, createTextVNode } from 'vue';\nimport { ElIcon } from '../../icon/index.mjs';\nimport { Check, Close } from '@element-plus/icons-vue';\nimport { stepProps } from './item2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nconst __default__ = defineComponent({\n  name: \"ElStep\"\n});\nconst _sfc_main = defineComponent({\n  ...__default__,\n  props: stepProps,\n  setup(__props) {\n    const props = __props;\n    const ns = useNamespace(\"step\");\n    const index = ref(-1);\n    const lineStyle = ref({});\n    const internalStatus = ref(\"\");\n    const parent = inject(\"ElSteps\");\n    const currentInstance = getCurrentInstance();\n    onMounted(() => {\n      watch([() => parent.props.active, () => parent.props.processStatus, () => parent.props.finishStatus], ([active]) => {\n        updateStatus(active);\n      }, {\n        immediate: true\n      });\n    });\n    onBeforeUnmount(() => {\n      parent.removeStep(stepItemState.uid);\n    });\n    const currentStatus = computed(() => {\n      return props.status || internalStatus.value;\n    });\n    const prevStatus = computed(() => {\n      const prevStep = parent.steps.value[index.value - 1];\n      return prevStep ? prevStep.currentStatus : \"wait\";\n    });\n    const isCenter = computed(() => {\n      return parent.props.alignCenter;\n    });\n    const isVertical = computed(() => {\n      return parent.props.direction === \"vertical\";\n    });\n    const isSimple = computed(() => {\n      return parent.props.simple;\n    });\n    const stepsCount = computed(() => {\n      return parent.steps.value.length;\n    });\n    const isLast = computed(() => {\n      var _a;\n      return ((_a = parent.steps.value[stepsCount.value - 1]) == null ? void 0 : _a.uid) === (currentInstance == null ? void 0 : currentInstance.uid);\n    });\n    const space = computed(() => {\n      return isSimple.value ? \"\" : parent.props.space;\n    });\n    const containerKls = computed(() => {\n      return [ns.b(), ns.is(isSimple.value ? \"simple\" : parent.props.direction), ns.is(\"flex\", isLast.value && !space.value && !isCenter.value), ns.is(\"center\", isCenter.value && !isVertical.value && !isSimple.value)];\n    });\n    const style = computed(() => {\n      const style2 = {\n        flexBasis: isNumber(space.value) ? `${space.value}px` : space.value ? space.value : `${100 / (stepsCount.value - (isCenter.value ? 0 : 1))}%`\n      };\n      if (isVertical.value) return style2;\n      if (isLast.value) {\n        style2.maxWidth = `${100 / stepsCount.value}%`;\n      }\n      return style2;\n    });\n    const setIndex = val => {\n      index.value = val;\n    };\n    const calcProgress = status => {\n      const isWait = status === \"wait\";\n      const style2 = {\n        transitionDelay: `${isWait ? \"-\" : \"\"}${150 * index.value}ms`\n      };\n      const step = status === parent.props.processStatus || isWait ? 0 : 100;\n      style2.borderWidth = step && !isSimple.value ? \"1px\" : 0;\n      style2[parent.props.direction === \"vertical\" ? \"height\" : \"width\"] = `${step}%`;\n      lineStyle.value = style2;\n    };\n    const updateStatus = activeIndex => {\n      if (activeIndex > index.value) {\n        internalStatus.value = parent.props.finishStatus;\n      } else if (activeIndex === index.value && prevStatus.value !== \"error\") {\n        internalStatus.value = parent.props.processStatus;\n      } else {\n        internalStatus.value = \"wait\";\n      }\n      const prevChild = parent.steps.value[index.value - 1];\n      if (prevChild) prevChild.calcProgress(internalStatus.value);\n    };\n    const stepItemState = reactive({\n      uid: currentInstance.uid,\n      currentStatus,\n      setIndex,\n      calcProgress\n    });\n    parent.addStep(stepItemState);\n    return (_ctx, _cache) => {\n      return openBlock(), createElementBlock(\"div\", {\n        style: normalizeStyle(unref(style)),\n        class: normalizeClass(unref(containerKls))\n      }, [createCommentVNode(\" icon & line \"), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"head\"), unref(ns).is(unref(currentStatus))])\n      }, [!unref(isSimple) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"line\"))\n      }, [createElementVNode(\"i\", {\n        class: normalizeClass(unref(ns).e(\"line-inner\")),\n        style: normalizeStyle(lineStyle.value)\n      }, null, 6)], 2)) : createCommentVNode(\"v-if\", true), createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"icon\"), unref(ns).is(_ctx.icon || _ctx.$slots.icon ? \"icon\" : \"text\")])\n      }, [renderSlot(_ctx.$slots, \"icon\", {}, () => [_ctx.icon ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"icon-inner\"))\n      }, {\n        default: withCtx(() => [(openBlock(), createBlock(resolveDynamicComponent(_ctx.icon)))]),\n        _: 1\n      }, 8, [\"class\"])) : unref(currentStatus) === \"success\" ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"icon-inner\"), unref(ns).is(\"status\")])\n      }, {\n        default: withCtx(() => [createVNode(unref(Check))]),\n        _: 1\n      }, 8, [\"class\"])) : unref(currentStatus) === \"error\" ? (openBlock(), createBlock(unref(ElIcon), {\n        key: 2,\n        class: normalizeClass([unref(ns).e(\"icon-inner\"), unref(ns).is(\"status\")])\n      }, {\n        default: withCtx(() => [createVNode(unref(Close))]),\n        _: 1\n      }, 8, [\"class\"])) : !unref(isSimple) ? (openBlock(), createElementBlock(\"div\", {\n        key: 3,\n        class: normalizeClass(unref(ns).e(\"icon-inner\"))\n      }, toDisplayString(index.value + 1), 3)) : createCommentVNode(\"v-if\", true)])], 2)], 2), createCommentVNode(\" title & description \"), createElementVNode(\"div\", {\n        class: normalizeClass(unref(ns).e(\"main\"))\n      }, [createElementVNode(\"div\", {\n        class: normalizeClass([unref(ns).e(\"title\"), unref(ns).is(unref(currentStatus))])\n      }, [renderSlot(_ctx.$slots, \"title\", {}, () => [createTextVNode(toDisplayString(_ctx.title), 1)])], 2), unref(isSimple) ? (openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        class: normalizeClass(unref(ns).e(\"arrow\"))\n      }, null, 2)) : (openBlock(), createElementBlock(\"div\", {\n        key: 1,\n        class: normalizeClass([unref(ns).e(\"description\"), unref(ns).is(unref(currentStatus))])\n      }, [renderSlot(_ctx.$slots, \"description\", {}, () => [createTextVNode(toDisplayString(_ctx.description), 1)])], 2))], 2)], 6);\n    };\n  }\n});\nvar Step = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"item.vue\"]]);\nexport { Step as default };", "map": {"version": 3, "names": ["name", "ns", "useNamespace", "index", "ref", "lineStyle", "internalStatus", "parent", "inject", "currentInstance", "getCurrentInstance", "onMounted", "watch", "props", "active", "processStatus", "finishStatus", "updateStatus", "immediate", "onBeforeUnmount", "removeStep", "stepItemState", "uid", "currentStatus", "computed", "status", "value", "prevStatus", "prevStep", "steps", "isCenter", "alignCenter", "isVertical", "direction", "isSimple", "simple", "stepsCount", "length", "isLast", "_a", "space", "containerKls", "b", "is", "style", "style2", "flexBasis", "isNumber", "max<PERSON><PERSON><PERSON>", "setIndex", "val", "calcProgress", "is<PERSON>ait", "transitionDelay", "step", "borderWidth", "activeIndex", "prev<PERSON><PERSON><PERSON>", "reactive", "addStep", "_ctx", "_cache"], "sources": ["../../../../../../packages/components/steps/src/item.vue"], "sourcesContent": ["<template>\n  <div :style=\"style\" :class=\"containerKls\">\n    <!-- icon & line -->\n    <div :class=\"[ns.e('head'), ns.is(currentStatus)]\">\n      <div v-if=\"!isSimple\" :class=\"ns.e('line')\">\n        <i :class=\"ns.e('line-inner')\" :style=\"lineStyle\" />\n      </div>\n\n      <div\n        :class=\"[ns.e('icon'), ns.is(icon || $slots.icon ? 'icon' : 'text')]\"\n      >\n        <slot name=\"icon\">\n          <el-icon v-if=\"icon\" :class=\"ns.e('icon-inner')\">\n            <component :is=\"icon\" />\n          </el-icon>\n          <el-icon\n            v-else-if=\"currentStatus === 'success'\"\n            :class=\"[ns.e('icon-inner'), ns.is('status')]\"\n          >\n            <Check />\n          </el-icon>\n          <el-icon\n            v-else-if=\"currentStatus === 'error'\"\n            :class=\"[ns.e('icon-inner'), ns.is('status')]\"\n          >\n            <Close />\n          </el-icon>\n          <div v-else-if=\"!isSimple\" :class=\"ns.e('icon-inner')\">\n            {{ index + 1 }}\n          </div>\n        </slot>\n      </div>\n    </div>\n    <!-- title & description -->\n    <div :class=\"ns.e('main')\">\n      <div :class=\"[ns.e('title'), ns.is(currentStatus)]\">\n        <slot name=\"title\">{{ title }}</slot>\n      </div>\n      <div v-if=\"isSimple\" :class=\"ns.e('arrow')\" />\n      <div v-else :class=\"[ns.e('description'), ns.is(currentStatus)]\">\n        <slot name=\"description\">{{ description }}</slot>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  inject,\n  onBeforeUnmount,\n  onMounted,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { Check, Close } from '@element-plus/icons-vue'\nimport { isNumber } from '@element-plus/utils'\nimport { stepProps } from './item'\n\nimport type { CSSProperties, Ref } from 'vue'\n\nexport interface IStepsProps {\n  space: number | string\n  active: number\n  direction: string\n  alignCenter: boolean\n  simple: boolean\n  finishStatus: string\n  processStatus: string\n}\n\nexport interface StepItemState {\n  uid: number\n  currentStatus: string\n  setIndex: (val: number) => void\n  calcProgress: (status: string) => void\n}\n\nexport interface IStepsInject {\n  props: IStepsProps\n  steps: Ref<StepItemState[]>\n  addStep: (item: StepItemState) => void\n  removeStep: (uid: number) => void\n}\n\ndefineOptions({\n  name: 'ElStep',\n})\n\nconst props = defineProps(stepProps)\nconst ns = useNamespace('step')\nconst index = ref(-1)\nconst lineStyle = ref({})\nconst internalStatus = ref('')\nconst parent = inject('ElSteps') as IStepsInject\nconst currentInstance = getCurrentInstance()\n\nonMounted(() => {\n  watch(\n    [\n      () => parent.props.active,\n      () => parent.props.processStatus,\n      () => parent.props.finishStatus,\n    ],\n    ([active]) => {\n      updateStatus(active)\n    },\n    { immediate: true }\n  )\n})\n\nonBeforeUnmount(() => {\n  parent.removeStep(stepItemState.uid)\n})\n\nconst currentStatus = computed(() => {\n  return props.status || internalStatus.value\n})\n\nconst prevStatus = computed(() => {\n  const prevStep = parent.steps.value[index.value - 1]\n  return prevStep ? prevStep.currentStatus : 'wait'\n})\n\nconst isCenter = computed(() => {\n  return parent.props.alignCenter\n})\n\nconst isVertical = computed(() => {\n  return parent.props.direction === 'vertical'\n})\n\nconst isSimple = computed(() => {\n  return parent.props.simple\n})\n\nconst stepsCount = computed(() => {\n  return parent.steps.value.length\n})\n\nconst isLast = computed(() => {\n  return parent.steps.value[stepsCount.value - 1]?.uid === currentInstance?.uid\n})\n\nconst space = computed(() => {\n  return isSimple.value ? '' : parent.props.space\n})\n\nconst containerKls = computed(() => {\n  return [\n    ns.b(),\n    ns.is(isSimple.value ? 'simple' : parent.props.direction),\n    ns.is('flex', isLast.value && !space.value && !isCenter.value),\n    ns.is('center', isCenter.value && !isVertical.value && !isSimple.value),\n  ]\n})\n\nconst style = computed(() => {\n  const style: CSSProperties = {\n    flexBasis: isNumber(space.value)\n      ? `${space.value}px`\n      : space.value\n      ? space.value\n      : `${100 / (stepsCount.value - (isCenter.value ? 0 : 1))}%`,\n  }\n  if (isVertical.value) return style\n  if (isLast.value) {\n    style.maxWidth = `${100 / stepsCount.value}%`\n  }\n  return style\n})\n\nconst setIndex = (val: number) => {\n  index.value = val\n}\n\nconst calcProgress = (status: string) => {\n  const isWait = status === 'wait'\n  const style: CSSProperties = {\n    transitionDelay: `${isWait ? '-' : ''}${150 * index.value}ms`,\n  }\n  const step = status === parent.props.processStatus || isWait ? 0 : 100\n\n  style.borderWidth = step && !isSimple.value ? '1px' : 0\n  style[parent.props.direction === 'vertical' ? 'height' : 'width'] = `${step}%`\n  lineStyle.value = style\n}\n\nconst updateStatus = (activeIndex: number) => {\n  if (activeIndex > index.value) {\n    internalStatus.value = parent.props.finishStatus\n  } else if (activeIndex === index.value && prevStatus.value !== 'error') {\n    internalStatus.value = parent.props.processStatus\n  } else {\n    internalStatus.value = 'wait'\n  }\n  const prevChild = parent.steps.value[index.value - 1]\n  if (prevChild) prevChild.calcProgress(internalStatus.value)\n}\n\nconst stepItemState = reactive({\n  uid: currentInstance!.uid,\n  currentStatus,\n  setIndex,\n  calcProgress,\n})\n\nparent.addStep(stepItemState)\n</script>\n"], "mappings": ";;;;;;;mCAyFc;EACZA,IAAM;AACR;;;;;;IAGM,MAAAC,EAAA,GAAKC,YAAA,CAAa,MAAM;IACxB,MAAAC,KAAA,GAAQC,GAAA,CAAI,CAAE;IACd,MAAAC,SAAA,GAAYD,GAAI,GAAE;IAClB,MAAAE,cAAA,GAAiBF,GAAA,CAAI,EAAE;IACvB,MAAAG,MAAA,GAASC,MAAA,CAAO,SAAS;IAC/B,MAAMC,eAAA,GAAkBC,kBAAmB;IAE3CC,SAAA,CAAU,MAAM;MACdC,KAAA,EACE,MAAAL,MAAA,CAAAM,KAAA,CAAAC,MAAA,EACE,MAAAP,MAAA,CAAAM,KAAmB,CAAAE,aAAA,EACnB,MAAAR,MAAA,CAAAM,KAAmB,CAAAG,YAAA,CACnB,IAAAF,MAAA,MAAa;QACfG,YAAA,CAAAH,MAAA;MAAA,CACA,EAAC;QAACI,SAAY;MAAA;IACZ;IACFC,eAAA;MACAZ,MAAA,CAAAa,UAAkB,CAAAC,aAAA,CAAAC,GAAA;IAAA,CACpB;IACF,MAACC,aAAA,GAAAC,QAAA;MAED,OAAAX,KAAA,CAAAY,MAAsB,IAAAnB,cAAA,CAAAoB,KAAA;IACpB,CAAO;IACT,MAACC,UAAA,GAAAH,QAAA;MAEK,MAAAI,QAAA,GAAArB,MAAA,CAAAsB,KAAyB,CAAMH,KAAA,CAAAvB,KAAA,CAAAuB,KAAA;MAC5B,OAAAE,QAAA,GAAAA,QAA+B,CAAAL,aAAA;IAAA,CACvC;IAEK,MAAAO,QAAA,GAAAN,QAAA,OAA4B;MAChC,OAAAjB,MAAA,CAAAM,KAAwB,CAAAkB,WAAY;IACpC,CAAO;IACT,MAACC,UAAA,GAAAR,QAAA;MAEK,OAAAjB,MAAA,CAAAM,KAAA,CAAAoB,SAA0B;IAC9B;IACF,MAACC,QAAA,GAAAV,QAAA;MAEK,OAAAjB,MAAA,CAAAM,KAAA,CAAAsB,MAAsB;IAC1B,CAAO;IACT,MAACC,UAAA,GAAAZ,QAAA;MAEK,OAAAjB,MAAA,CAAAsB,KAAA,CAAAH,KAA0B,CAAAW,MAAA;IAC9B;IACF,MAACC,MAAA,GAAAd,QAAA;MAEK,IAAAe,EAAA;MACG,SAAAA,EAAA,GAAOhC,MAAM,CAAMsB,KAAA,CAAAH,KAAA,CAAAU,UAAA,CAAAV,KAAA,0BAAAa,EAAA,CAAAjB,GAAA,OAAAb,eAAA,oBAAAA,eAAA,CAAAa,GAAA;IAAA,CAC3B;IAEK,MAAAkB,KAAA,GAAAhB,QAAA,OAAwB;MACrB,OAAAU,QAAA,CAAAR,KAAmB,QAAAnB,MAAA,CAAAM,KAAA,CAAA2B,KAAoB;IAA4B,CAC3E;IAEK,MAAAC,YAAA,GAAAjB,QAAuB;MAC3B,OAAO,CACRvB,EAAA,CAAAyC,CAAA,IAEKzC,EAAA,CAAA0C,EAAA,CAAAT,QAAA,CAAAR,KAAA,WAA8B,GAAAnB,MAAA,CAAAM,KAAA,CAAAoB,SAAA,GAC3BhC,EAAA,CAAA0C,EAAA,SAAAL,MAAA,CAAAZ,KAAA,KAAAc,KAAA,CAAAd,KAAA,KAAAI,QAAA,CAAAJ,KAAA,GACLzB,EAAA,CAAG0C,EAAE,WAAAb,QAAA,CAAAJ,KAAA,KAAAM,UAAA,CAAAN,KAAA,KAAAQ,QAAA,CAAAR,KAAA;IACmD,CACxD;IACA,MAAAkB,KAAM,GAAApB,QAAmB;MAC3B,MAAAqB,MAAA;QACDC,SAAA,EAAAC,QAAA,CAAAP,KAAA,CAAAd,KAAA,OAAAc,KAAA,CAAAd,KAAA,OAAAc,KAAA,CAAAd,KAAA,GAAAc,KAAA,CAAAd,KAAA,aAAAU,UAAA,CAAAV,KAAA,IAAAI,QAAA,CAAAJ,KAAA;MAED,CAAM;MACJ,IAAAM,UAA6B,CAAAN,KAAA,EAC3B,OAAAmB,MAAA;MAKF,IAAAP,MAAA,CAAAZ,KAAA;QACImB,MAAA,CAAAG,QAAA,MAAyB,MAAAZ,UAAA,CAAAV,KAAA;MAC7B;MACE,OAAMmB,MAAA;IAAoC,CAC5C;IACO,MAAAI,QAAA,GAAAC,GAAA;MACR/C,KAAA,CAAAuB,KAAA,GAAAwB,GAAA;IAED,CAAM;IACJ,MAAAC,YAAc,GAAA1B,MAAA;MAChB,MAAA2B,MAAA,GAAA3B,MAAA;MAEM,MAAAoB,MAAA;QACJQ,eAA0B,KAAAD,MAAA,oBAAAjD,KAAA,CAAAuB,KAAA;MAC1B;MACE,MAAA4B,IAAA,GAAA7B,MAAiB,KAAYlB,MAAA,CAAAM,KAAA,CAAME,aAAA,IAAiBqC,MAAK;MAC3DP,MAAA,CAAAU,WAAA,GAAAD,IAAA,KAAApB,QAAA,CAAAR,KAAA;MACAmB,MAAM,CAAAtC,MAAO,CAAWM,KAAA,CAAAoB,SAAA,KAAa,+BAA8B,OAAAqB,IAAA;MAEnEjD,SAAoB,CAAAqB,KAAA,GAAAmB,MAAA;IACpB;IACA,MAAA5B,YAAkB,GAAAuC,WAAA;MACpB,IAAAA,WAAA,GAAArD,KAAA,CAAAuB,KAAA;QAEMpB,cAAA,CAAAoB,KAAwC,GAAAnB,MAAA,CAAAM,KAAA,CAAAG,YAAA;MAC5C,CAAI,UAAAwC,WAAA,KAA2BrD,KAAA,CAAAuB,KAAA,IAAAC,UAAA,CAAAD,KAAA;QACdpB,cAAA,CAAAoB,KAAA,GAAQnB,MAAA,CAAOM,KAAM,CAAAE,aAAA;MAAA;QAErBT,cAAA,CAAAoB,KAAA,GAAQ;MAAa;MAEpC,MAAA+B,SAAA,GAAuBlD,MAAA,CAAAsB,KAAA,CAAAH,KAAA,CAAAvB,KAAA,CAAAuB,KAAA;MACzB,IAAA+B,SAAA,EACAA,SAAA,CAAAN,YAAyB,CAAA7C,cAAY,CAAAoB,KAAA;IACrC;IACF,MAAAL,aAAA,GAAAqC,QAAA;MAEApC,GAAA,EAAAb,eAAsB,CAASa,GAAA;MAC7BC,aAAsB;MACtB0B,QAAA;MACAE;IAAA,CACA;IACF5C,MAAC,CAAAoD,OAAA,CAAAtC,aAAA;IAED,OAAO,CAAAuC,IAAA,EAAAC,MAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}