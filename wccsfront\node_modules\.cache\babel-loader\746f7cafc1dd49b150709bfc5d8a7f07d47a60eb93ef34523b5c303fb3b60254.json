{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * This method is like `_.flatMap` except that it recursively flattens the\n * mapped results.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [[[n, n]]];\n * }\n *\n * _.flatMapDeep([1, 2], duplicate);\n * // => [1, 1, 2, 2]\n */\nfunction flatMapDeep(collection, iteratee) {\n  return baseFlatten(map(collection, iteratee), INFINITY);\n}\nexport default flatMapDeep;", "map": {"version": 3, "names": ["baseFlatten", "map", "INFINITY", "flatMapDeep", "collection", "iteratee"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/flatMapDeep.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport map from './map.js';\n\n/** Used as references for various `Number` constants. */\nvar INFINITY = 1 / 0;\n\n/**\n * This method is like `_.flatMap` except that it recursively flattens the\n * mapped results.\n *\n * @static\n * @memberOf _\n * @since 4.7.0\n * @category Collection\n * @param {Array|Object} collection The collection to iterate over.\n * @param {Function} [iteratee=_.identity] The function invoked per iteration.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * function duplicate(n) {\n *   return [[[n, n]]];\n * }\n *\n * _.flatMapDeep([1, 2], duplicate);\n * // => [1, 1, 2, 2]\n */\nfunction flatMapDeep(collection, iteratee) {\n  return baseFlatten(map(collection, iteratee), INFINITY);\n}\n\nexport default flatMapDeep;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,GAAG,MAAM,UAAU;;AAE1B;AACA,IAAIC,QAAQ,GAAG,CAAC,GAAG,CAAC;;AAEpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAEC,QAAQ,EAAE;EACzC,OAAOL,WAAW,CAACC,GAAG,CAACG,UAAU,EAAEC,QAAQ,CAAC,EAAEH,QAAQ,CAAC;AACzD;AAEA,eAAeC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}