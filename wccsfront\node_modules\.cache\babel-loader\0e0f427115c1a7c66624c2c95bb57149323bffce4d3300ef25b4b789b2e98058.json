{"ast": null, "code": "import { buildProps, definePropType } from '../../../../utils/vue/props/runtime.mjs';\nconst basicCellProps = buildProps({\n  cell: {\n    type: definePropType(Object)\n  }\n});\nexport { basicCellProps };", "map": {"version": 3, "names": ["basicCellProps", "buildProps", "cell", "type", "definePropType", "Object"], "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-cell.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\nimport type { DateCell } from '../date-picker.type'\n\nexport const basicCellProps = buildProps({\n  cell: {\n    type: definePropType<DateCell>(Object),\n  },\n} as const)\n\nexport type BasicCellProps = ExtractPropTypes<typeof basicCellProps>\n"], "mappings": ";AACY,MAACA,cAAc,GAAGC,UAAU,CAAC;EACvCC,IAAI,EAAE;IACJC,IAAI,EAAEC,cAAc,CAACC,MAAM;EAC/B;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}