{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst descriptionsRowProps = buildProps({\n  row: {\n    type: definePropType(Array),\n    default: () => []\n  }\n});\nexport { descriptionsRowProps };", "map": {"version": 3, "names": ["descriptionsRowProps", "buildProps", "row", "type", "definePropType", "Array", "default"], "sources": ["../../../../../../packages/components/descriptions/src/descriptions-row.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { DescriptionItemVNode } from './description-item'\n\nexport const descriptionsRowProps = buildProps({\n  row: {\n    type: definePropType<DescriptionItemVNode[]>(Array),\n    default: () => [],\n  },\n} as const)\n"], "mappings": ";AACY,MAACA,oBAAoB,GAAGC,UAAU,CAAC;EAC7CC,GAAG,EAAE;IACHC,IAAI,EAAEC,cAAc,CAACC,KAAK,CAAC;IAC3BC,OAAO,EAAEA,CAAA,KAAM;EACnB;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}