{"ast": null, "code": "import constant from './constant.js';\nimport defineProperty from './_defineProperty.js';\nimport identity from './identity.js';\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function (func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\nexport default baseSetToString;", "map": {"version": 3, "names": ["constant", "defineProperty", "identity", "baseSetToString", "func", "string"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_baseSetToString.js"], "sourcesContent": ["import constant from './constant.js';\nimport defineProperty from './_defineProperty.js';\nimport identity from './identity.js';\n\n/**\n * The base implementation of `setToString` without support for hot loop shorting.\n *\n * @private\n * @param {Function} func The function to modify.\n * @param {Function} string The `toString` result.\n * @returns {Function} Returns `func`.\n */\nvar baseSetToString = !defineProperty ? identity : function(func, string) {\n  return defineProperty(func, 'toString', {\n    'configurable': true,\n    'enumerable': false,\n    'value': constant(string),\n    'writable': true\n  });\n};\n\nexport default baseSetToString;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;AACpC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,eAAe,GAAG,CAACF,cAAc,GAAGC,QAAQ,GAAG,UAASE,IAAI,EAAEC,MAAM,EAAE;EACxE,OAAOJ,cAAc,CAACG,IAAI,EAAE,UAAU,EAAE;IACtC,cAAc,EAAE,IAAI;IACpB,YAAY,EAAE,KAAK;IACnB,OAAO,EAAEJ,QAAQ,CAACK,MAAM,CAAC;IACzB,UAAU,EAAE;EACd,CAAC,CAAC;AACJ,CAAC;AAED,eAAeF,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}