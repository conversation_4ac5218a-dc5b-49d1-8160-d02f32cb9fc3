{"ast": null, "code": "import root from './_root.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsFinite = root.isFinite;\n\n/**\n * Checks if `value` is a finite primitive number.\n *\n * **Note:** This method is based on\n * [`Number.isFinite`](https://mdn.io/Number/isFinite).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a finite number, else `false`.\n * @example\n *\n * _.isFinite(3);\n * // => true\n *\n * _.isFinite(Number.MIN_VALUE);\n * // => true\n *\n * _.isFinite(Infinity);\n * // => false\n *\n * _.isFinite('3');\n * // => false\n */\nfunction isFinite(value) {\n  return typeof value == 'number' && nativeIsFinite(value);\n}\nexport default isFinite;", "map": {"version": 3, "names": ["root", "nativeIsFinite", "isFinite", "value"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/isFinite.js"], "sourcesContent": ["import root from './_root.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeIsFinite = root.isFinite;\n\n/**\n * Checks if `value` is a finite primitive number.\n *\n * **Note:** This method is based on\n * [`Number.isFinite`](https://mdn.io/Number/isFinite).\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a finite number, else `false`.\n * @example\n *\n * _.isFinite(3);\n * // => true\n *\n * _.isFinite(Number.MIN_VALUE);\n * // => true\n *\n * _.isFinite(Infinity);\n * // => false\n *\n * _.isFinite('3');\n * // => false\n */\nfunction isFinite(value) {\n  return typeof value == 'number' && nativeIsFinite(value);\n}\n\nexport default isFinite;\n"], "mappings": "AAAA,OAAOA,IAAI,MAAM,YAAY;;AAE7B;AACA,IAAIC,cAAc,GAAGD,IAAI,CAACE,QAAQ;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAO,OAAOA,KAAK,IAAI,QAAQ,IAAIF,cAAc,CAACE,KAAK,CAAC;AAC1D;AAEA,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}