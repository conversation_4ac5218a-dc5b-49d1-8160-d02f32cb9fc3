{"ast": null, "code": "/**\n * Checks `value` to determine whether a default value should be returned in\n * its place. The `defaultValue` is returned if `value` is `NaN`, `null`,\n * or `undefined`.\n *\n * @static\n * @memberOf _\n * @since 4.14.0\n * @category Util\n * @param {*} value The value to check.\n * @param {*} defaultValue The default value.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * _.defaultTo(1, 10);\n * // => 1\n *\n * _.defaultTo(undefined, 10);\n * // => 10\n */\nfunction defaultTo(value, defaultValue) {\n  return value == null || value !== value ? defaultValue : value;\n}\nexport default defaultTo;", "map": {"version": 3, "names": ["defaultTo", "value", "defaultValue"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/defaultTo.js"], "sourcesContent": ["/**\n * Checks `value` to determine whether a default value should be returned in\n * its place. The `defaultValue` is returned if `value` is `NaN`, `null`,\n * or `undefined`.\n *\n * @static\n * @memberOf _\n * @since 4.14.0\n * @category Util\n * @param {*} value The value to check.\n * @param {*} defaultValue The default value.\n * @returns {*} Returns the resolved value.\n * @example\n *\n * _.defaultTo(1, 10);\n * // => 1\n *\n * _.defaultTo(undefined, 10);\n * // => 10\n */\nfunction defaultTo(value, defaultValue) {\n  return (value == null || value !== value) ? defaultValue : value;\n}\n\nexport default defaultTo;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACtC,OAAQD,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAKA,KAAK,GAAIC,YAAY,GAAGD,KAAK;AAClE;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}