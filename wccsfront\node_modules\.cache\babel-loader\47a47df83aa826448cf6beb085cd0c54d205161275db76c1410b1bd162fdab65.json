{"ast": null, "code": "import baseClamp from './_baseClamp.js';\nimport copyArray from './_copyArray.js';\nimport shuffleSelf from './_shuffleSelf.js';\n\n/**\n * A specialized version of `_.sampleSize` for arrays.\n *\n * @private\n * @param {Array} array The array to sample.\n * @param {number} n The number of elements to sample.\n * @returns {Array} Returns the random elements.\n */\nfunction arraySampleSize(array, n) {\n  return shuffleSelf(copyArray(array), baseClamp(n, 0, array.length));\n}\nexport default arraySampleSize;", "map": {"version": 3, "names": ["baseClamp", "copyArray", "shuffleSelf", "arraySampleSize", "array", "n", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_arraySampleSize.js"], "sourcesContent": ["import baseClamp from './_baseClamp.js';\nimport copyArray from './_copyArray.js';\nimport shuffleSelf from './_shuffleSelf.js';\n\n/**\n * A specialized version of `_.sampleSize` for arrays.\n *\n * @private\n * @param {Array} array The array to sample.\n * @param {number} n The number of elements to sample.\n * @returns {Array} Returns the random elements.\n */\nfunction arraySampleSize(array, n) {\n  return shuffleSelf(copyArray(array), baseClamp(n, 0, array.length));\n}\n\nexport default arraySampleSize;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,SAAS,MAAM,iBAAiB;AACvC,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAEC,CAAC,EAAE;EACjC,OAAOH,WAAW,CAACD,SAAS,CAACG,KAAK,CAAC,EAAEJ,SAAS,CAACK,CAAC,EAAE,CAAC,EAAED,KAAK,CAACE,MAAM,CAAC,CAAC;AACrE;AAEA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}