{"ast": null, "code": "import { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nconst EventHandler = {\n  type: definePropType(Function)\n};\nconst tooltipV2TriggerProps = buildProps({\n  onBlur: EventHandler,\n  onClick: EventHandler,\n  onFocus: EventHandler,\n  onMouseDown: EventHandler,\n  onMouseEnter: EventHandler,\n  onMouseLeave: EventHandler\n});\nexport { tooltipV2TriggerProps };", "map": {"version": 3, "names": ["EventHandler", "type", "definePropType", "Function", "tooltipV2TriggerProps", "buildProps", "onBlur", "onClick", "onFocus", "onMouseDown", "onMouseEnter", "onMouseLeave"], "sources": ["../../../../../../packages/components/tooltip-v2/src/trigger.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { ExtractPropTypes } from 'vue'\n\nconst EventHandler = {\n  type: definePropType<(e: Event) => boolean | void>(Function),\n} as const\n\nexport const tooltipV2TriggerProps = buildProps({\n  onBlur: EventHandler,\n  onClick: EventHandler,\n  onFocus: EventHandler,\n  onMouseDown: EventHandler,\n  onMouseEnter: EventHandler,\n  onMouseLeave: EventHandler,\n} as const)\n\nexport type TooltipV2TriggerProps = ExtractPropTypes<\n  typeof tooltipV2TriggerProps\n>\n"], "mappings": ";AACA,MAAMA,YAAY,GAAG;EACnBC,IAAI,EAAEC,cAAc,CAACC,QAAQ;AAC/B,CAAC;AACW,MAACC,qBAAqB,GAAGC,UAAU,CAAC;EAC9CC,MAAM,EAAEN,YAAY;EACpBO,OAAO,EAAEP,YAAY;EACrBQ,OAAO,EAAER,YAAY;EACrBS,WAAW,EAAET,YAAY;EACzBU,YAAY,EAAEV,YAAY;EAC1BW,YAAY,EAAEX;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}