{"ast": null, "code": "import baseFlatten from './_baseFlatten.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Recursively flatten `array` up to `depth` times.\n *\n * @static\n * @memberOf _\n * @since 4.4.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @param {number} [depth=1] The maximum recursion depth.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * var array = [1, [2, [3, [4]], 5]];\n *\n * _.flattenDepth(array, 1);\n * // => [1, 2, [3, [4]], 5]\n *\n * _.flattenDepth(array, 2);\n * // => [1, 2, 3, [4], 5]\n */\nfunction flattenDepth(array, depth) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  depth = depth === undefined ? 1 : toInteger(depth);\n  return baseFlatten(array, depth);\n}\nexport default flattenDepth;", "map": {"version": 3, "names": ["baseFlatten", "toInteger", "flatten<PERSON><PERSON>h", "array", "depth", "length", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/flattenDepth.js"], "sourcesContent": ["import baseFlatten from './_baseFlatten.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Recursively flatten `array` up to `depth` times.\n *\n * @static\n * @memberOf _\n * @since 4.4.0\n * @category Array\n * @param {Array} array The array to flatten.\n * @param {number} [depth=1] The maximum recursion depth.\n * @returns {Array} Returns the new flattened array.\n * @example\n *\n * var array = [1, [2, [3, [4]], 5]];\n *\n * _.flattenDepth(array, 1);\n * // => [1, 2, [3, [4]], 5]\n *\n * _.flattenDepth(array, 2);\n * // => [1, 2, 3, [4], 5]\n */\nfunction flattenDepth(array, depth) {\n  var length = array == null ? 0 : array.length;\n  if (!length) {\n    return [];\n  }\n  depth = depth === undefined ? 1 : toInteger(depth);\n  return baseFlatten(array, depth);\n}\n\nexport default flattenDepth;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,KAAK,EAAE;EAClC,IAAIC,MAAM,GAAGF,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACE,MAAM;EAC7C,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,EAAE;EACX;EACAD,KAAK,GAAGA,KAAK,KAAKE,SAAS,GAAG,CAAC,GAAGL,SAAS,CAACG,KAAK,CAAC;EAClD,OAAOJ,WAAW,CAACG,KAAK,EAAEC,KAAK,CAAC;AAClC;AAEA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}