{"ast": null, "code": "import axios from 'axios';\n\n// 创建一个 axios 实例\n\nconst instance = axios.create({\n  // 设置全局的 URL 前缀\n  baseURL: 'http://172.17.10.202:8081',\n  timeout: 10000 // 设置请求超时时间\n});\nexport default instance;", "map": {"version": 3, "names": ["axios", "instance", "create", "baseURL", "timeout"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/src/axiosConfig.js"], "sourcesContent": ["import axios from 'axios';\r\n\r\n// 创建一个 axios 实例\r\n\r\nconst instance = axios.create({\r\n    // 设置全局的 URL 前缀\r\n    baseURL: 'http://172.17.10.202:8081',\r\n    timeout: 10000, // 设置请求超时时间\r\n});\r\n\r\n\r\nexport default instance;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;;AAEA,MAAMC,QAAQ,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC1B;EACAC,OAAO,EAAE,2BAA2B;EACpCC,OAAO,EAAE,KAAK,CAAE;AACpB,CAAC,CAAC;AAGF,eAAeH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}