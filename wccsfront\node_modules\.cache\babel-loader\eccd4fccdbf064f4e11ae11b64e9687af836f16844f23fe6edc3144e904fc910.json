{"ast": null, "code": "import { renderSlot, createVNode } from 'vue';\nimport { ElEmpty } from '../../../empty/index.mjs';\nconst Footer = (props, {\n  slots\n}) => {\n  const defaultSlot = renderSlot(slots, \"default\", {}, () => [createVNode(ElEmpty, null, null)]);\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"style\": props.style\n  }, [defaultSlot]);\n};\nFooter.displayName = \"ElTableV2Empty\";\nvar Empty = Footer;\nexport { Empty as default };", "map": {"version": 3, "names": ["slots", "defaultSlot", "renderSlot", "createVNode", "ElEmpty", "props", "class", "style", "Footer", "displayName"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/empty.tsx"], "sourcesContent": ["import { renderSlot } from 'vue'\nimport ElEmpty from '@element-plus/components/empty'\nimport type { CSSProperties, FunctionalComponent } from 'vue'\n\ntype EmptyRendererProps = {\n  class?: JSX.IntrinsicAttributes['class']\n  style?: CSSProperties\n}\n\nconst Footer: FunctionalComponent<EmptyRendererProps> = (props, { slots }) => {\n  const defaultSlot = renderSlot(slots, 'default', {}, () => [<ElEmpty />])\n  return (\n    <div class={props.class} style={props.style}>\n      {defaultSlot}\n    </div>\n  )\n}\n\nFooter.displayName = 'ElTableV2Empty'\n\nexport default Footer\n"], "mappings": ";;;EASAA;AAAkE;EAAY,MAAAC,WAAA,GAAAC,UAAA,CAAAF,KAAA,wBAAAG,WAAA,CAAAC,OAAA;EAC5E,OAAiBD,WAAA,CAAG,KAAU;IAC9B,SAAAE,KAAA,CAAAC,KAAA;IAAA,OACc,EAAAD,KAAK,CAACE;EADpB,IAAAN,WAAA,CACuC,CAAC;AADxC;AAKDO,MAPD,CAAAC,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}