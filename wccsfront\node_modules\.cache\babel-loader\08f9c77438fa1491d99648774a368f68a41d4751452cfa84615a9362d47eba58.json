{"ast": null, "code": "import mapCacheClear from './_mapCacheClear.js';\nimport mapCacheDelete from './_mapCacheDelete.js';\nimport mapCacheGet from './_mapCacheGet.js';\nimport mapCacheHas from './_mapCacheHas.js';\nimport mapCacheSet from './_mapCacheSet.js';\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n    length = entries == null ? 0 : entries.length;\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\nexport default MapCache;", "map": {"version": 3, "names": ["mapCacheClear", "mapCacheDelete", "mapCacheGet", "mapCacheHas", "mapCacheSet", "MapCache", "entries", "index", "length", "clear", "entry", "set", "prototype", "get", "has"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_MapCache.js"], "sourcesContent": ["import mapCacheClear from './_mapCacheClear.js';\nimport mapCacheDelete from './_mapCacheDelete.js';\nimport mapCacheGet from './_mapCacheGet.js';\nimport mapCacheHas from './_mapCacheHas.js';\nimport mapCacheSet from './_mapCacheSet.js';\n\n/**\n * Creates a map cache object to store key-value pairs.\n *\n * @private\n * @constructor\n * @param {Array} [entries] The key-value pairs to cache.\n */\nfunction MapCache(entries) {\n  var index = -1,\n      length = entries == null ? 0 : entries.length;\n\n  this.clear();\n  while (++index < length) {\n    var entry = entries[index];\n    this.set(entry[0], entry[1]);\n  }\n}\n\n// Add methods to `MapCache`.\nMapCache.prototype.clear = mapCacheClear;\nMapCache.prototype['delete'] = mapCacheDelete;\nMapCache.prototype.get = mapCacheGet;\nMapCache.prototype.has = mapCacheHas;\nMapCache.prototype.set = mapCacheSet;\n\nexport default MapCache;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;AAC/C,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,OAAO,EAAE;EACzB,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGF,OAAO,IAAI,IAAI,GAAG,CAAC,GAAGA,OAAO,CAACE,MAAM;EAEjD,IAAI,CAACC,KAAK,CAAC,CAAC;EACZ,OAAO,EAAEF,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIE,KAAK,GAAGJ,OAAO,CAACC,KAAK,CAAC;IAC1B,IAAI,CAACI,GAAG,CAACD,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;EAC9B;AACF;;AAEA;AACAL,QAAQ,CAACO,SAAS,CAACH,KAAK,GAAGT,aAAa;AACxCK,QAAQ,CAACO,SAAS,CAAC,QAAQ,CAAC,GAAGX,cAAc;AAC7CI,QAAQ,CAACO,SAAS,CAACC,GAAG,GAAGX,WAAW;AACpCG,QAAQ,CAACO,SAAS,CAACE,GAAG,GAAGX,WAAW;AACpCE,QAAQ,CAACO,SAAS,CAACD,GAAG,GAAGP,WAAW;AAEpC,eAAeC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}