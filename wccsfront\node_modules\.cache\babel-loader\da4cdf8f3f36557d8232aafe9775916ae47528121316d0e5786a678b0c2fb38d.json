{"ast": null, "code": "import { createApp } from 'vue';\nimport App from './App.vue';\n\n//引入element-ui\nimport ElementPlus from 'element-plus';\nimport 'element-plus/dist/index.css';\nimport * as ElementPlusIcons from '@element-plus/icons-vue';\n\n//引入路由\nimport VueRouter from './router';\n\n//引入reset.css\nimport 'reset-css/reset.css';\n\n//引入vue-audio-visual\nimport { AVPlugin } from 'vue-audio-visual';\nconst app = createApp(App);\napp.use(ElementPlus);\napp.use(VueRouter);\napp.use(ElementPlusIcons);\napp.use(AVPlugin);\napp.mount('#app');", "map": {"version": 3, "names": ["createApp", "App", "ElementPlus", "ElementPlusIcons", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "app", "use", "mount"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/src/main.js"], "sourcesContent": ["import { createApp } from 'vue'\nimport App from './App.vue'\n\n//引入element-ui\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport * as ElementPlusIcons from '@element-plus/icons-vue'\n\n//引入路由\nimport VueRouter from './router'\n\n//引入reset.css\nimport 'reset-css/reset.css'\n\n//引入vue-audio-visual\nimport { AVPlugin } from 'vue-audio-visual'\n\n\n\nconst app = createApp(App)\napp.use(ElementPlus)\napp.use(VueRouter)\napp.use(ElementPlusIcons)\napp.use(AVPlugin)\n\napp.mount('#app')\n\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,KAAK;AAC/B,OAAOC,GAAG,MAAM,WAAW;;AAE3B;AACA,OAAOC,WAAW,MAAM,cAAc;AACtC,OAAO,6BAA6B;AACpC,OAAO,KAAKC,gBAAgB,MAAM,yBAAyB;;AAE3D;AACA,OAAOC,SAAS,MAAM,UAAU;;AAEhC;AACA,OAAO,qBAAqB;;AAE5B;AACA,SAASC,QAAQ,QAAQ,kBAAkB;AAI3C,MAAMC,GAAG,GAAGN,SAAS,CAACC,GAAG,CAAC;AAC1BK,GAAG,CAACC,GAAG,CAACL,WAAW,CAAC;AACpBI,GAAG,CAACC,GAAG,CAACH,SAAS,CAAC;AAClBE,GAAG,CAACC,GAAG,CAACJ,gBAAgB,CAAC;AACzBG,GAAG,CAACC,GAAG,CAACF,QAAQ,CAAC;AAEjBC,GAAG,CAACE,KAAK,CAAC,MAAM,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}