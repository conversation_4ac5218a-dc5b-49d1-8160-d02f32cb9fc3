{"ast": null, "code": "import baseRandom from './_baseRandom.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseFloat = parseFloat;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min,\n  nativeRandom = Math.random;\n\n/**\n * Produces a random number between the inclusive `lower` and `upper` bounds.\n * If only one argument is provided a number between `0` and the given number\n * is returned. If `floating` is `true`, or either `lower` or `upper` are\n * floats, a floating-point number is returned instead of an integer.\n *\n * **Note:** JavaScript follows the IEEE-754 standard for resolving\n * floating-point values which can produce unexpected results.\n *\n * @static\n * @memberOf _\n * @since 0.7.0\n * @category Number\n * @param {number} [lower=0] The lower bound.\n * @param {number} [upper=1] The upper bound.\n * @param {boolean} [floating] Specify returning a floating-point number.\n * @returns {number} Returns the random number.\n * @example\n *\n * _.random(0, 5);\n * // => an integer between 0 and 5\n *\n * _.random(5);\n * // => also an integer between 0 and 5\n *\n * _.random(5, true);\n * // => a floating-point number between 0 and 5\n *\n * _.random(1.2, 5.2);\n * // => a floating-point number between 1.2 and 5.2\n */\nfunction random(lower, upper, floating) {\n  if (floating && typeof floating != 'boolean' && isIterateeCall(lower, upper, floating)) {\n    upper = floating = undefined;\n  }\n  if (floating === undefined) {\n    if (typeof upper == 'boolean') {\n      floating = upper;\n      upper = undefined;\n    } else if (typeof lower == 'boolean') {\n      floating = lower;\n      lower = undefined;\n    }\n  }\n  if (lower === undefined && upper === undefined) {\n    lower = 0;\n    upper = 1;\n  } else {\n    lower = toFinite(lower);\n    if (upper === undefined) {\n      upper = lower;\n      lower = 0;\n    } else {\n      upper = toFinite(upper);\n    }\n  }\n  if (lower > upper) {\n    var temp = lower;\n    lower = upper;\n    upper = temp;\n  }\n  if (floating || lower % 1 || upper % 1) {\n    var rand = nativeRandom();\n    return nativeMin(lower + rand * (upper - lower + freeParseFloat('1e-' + ((rand + '').length - 1))), upper);\n  }\n  return baseRandom(lower, upper);\n}\nexport default random;", "map": {"version": 3, "names": ["baseRandom", "isIterateeCall", "toFinite", "freeParseFloat", "parseFloat", "nativeMin", "Math", "min", "nativeRandom", "random", "lower", "upper", "floating", "undefined", "temp", "rand", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/random.js"], "sourcesContent": ["import baseRandom from './_baseRandom.js';\nimport isIterateeCall from './_isIterateeCall.js';\nimport toFinite from './toFinite.js';\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseFloat = parseFloat;\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min,\n    nativeRandom = Math.random;\n\n/**\n * Produces a random number between the inclusive `lower` and `upper` bounds.\n * If only one argument is provided a number between `0` and the given number\n * is returned. If `floating` is `true`, or either `lower` or `upper` are\n * floats, a floating-point number is returned instead of an integer.\n *\n * **Note:** JavaScript follows the IEEE-754 standard for resolving\n * floating-point values which can produce unexpected results.\n *\n * @static\n * @memberOf _\n * @since 0.7.0\n * @category Number\n * @param {number} [lower=0] The lower bound.\n * @param {number} [upper=1] The upper bound.\n * @param {boolean} [floating] Specify returning a floating-point number.\n * @returns {number} Returns the random number.\n * @example\n *\n * _.random(0, 5);\n * // => an integer between 0 and 5\n *\n * _.random(5);\n * // => also an integer between 0 and 5\n *\n * _.random(5, true);\n * // => a floating-point number between 0 and 5\n *\n * _.random(1.2, 5.2);\n * // => a floating-point number between 1.2 and 5.2\n */\nfunction random(lower, upper, floating) {\n  if (floating && typeof floating != 'boolean' && isIterateeCall(lower, upper, floating)) {\n    upper = floating = undefined;\n  }\n  if (floating === undefined) {\n    if (typeof upper == 'boolean') {\n      floating = upper;\n      upper = undefined;\n    }\n    else if (typeof lower == 'boolean') {\n      floating = lower;\n      lower = undefined;\n    }\n  }\n  if (lower === undefined && upper === undefined) {\n    lower = 0;\n    upper = 1;\n  }\n  else {\n    lower = toFinite(lower);\n    if (upper === undefined) {\n      upper = lower;\n      lower = 0;\n    } else {\n      upper = toFinite(upper);\n    }\n  }\n  if (lower > upper) {\n    var temp = lower;\n    lower = upper;\n    upper = temp;\n  }\n  if (floating || lower % 1 || upper % 1) {\n    var rand = nativeRandom();\n    return nativeMin(lower + (rand * (upper - lower + freeParseFloat('1e-' + ((rand + '').length - 1)))), upper);\n  }\n  return baseRandom(lower, upper);\n}\n\nexport default random;\n"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,OAAOC,cAAc,MAAM,sBAAsB;AACjD,OAAOC,QAAQ,MAAM,eAAe;;AAEpC;AACA,IAAIC,cAAc,GAAGC,UAAU;;AAE/B;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;EACpBC,YAAY,GAAGF,IAAI,CAACG,MAAM;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,EAAE;EACtC,IAAIA,QAAQ,IAAI,OAAOA,QAAQ,IAAI,SAAS,IAAIX,cAAc,CAACS,KAAK,EAAEC,KAAK,EAAEC,QAAQ,CAAC,EAAE;IACtFD,KAAK,GAAGC,QAAQ,GAAGC,SAAS;EAC9B;EACA,IAAID,QAAQ,KAAKC,SAAS,EAAE;IAC1B,IAAI,OAAOF,KAAK,IAAI,SAAS,EAAE;MAC7BC,QAAQ,GAAGD,KAAK;MAChBA,KAAK,GAAGE,SAAS;IACnB,CAAC,MACI,IAAI,OAAOH,KAAK,IAAI,SAAS,EAAE;MAClCE,QAAQ,GAAGF,KAAK;MAChBA,KAAK,GAAGG,SAAS;IACnB;EACF;EACA,IAAIH,KAAK,KAAKG,SAAS,IAAIF,KAAK,KAAKE,SAAS,EAAE;IAC9CH,KAAK,GAAG,CAAC;IACTC,KAAK,GAAG,CAAC;EACX,CAAC,MACI;IACHD,KAAK,GAAGR,QAAQ,CAACQ,KAAK,CAAC;IACvB,IAAIC,KAAK,KAAKE,SAAS,EAAE;MACvBF,KAAK,GAAGD,KAAK;MACbA,KAAK,GAAG,CAAC;IACX,CAAC,MAAM;MACLC,KAAK,GAAGT,QAAQ,CAACS,KAAK,CAAC;IACzB;EACF;EACA,IAAID,KAAK,GAAGC,KAAK,EAAE;IACjB,IAAIG,IAAI,GAAGJ,KAAK;IAChBA,KAAK,GAAGC,KAAK;IACbA,KAAK,GAAGG,IAAI;EACd;EACA,IAAIF,QAAQ,IAAIF,KAAK,GAAG,CAAC,IAAIC,KAAK,GAAG,CAAC,EAAE;IACtC,IAAII,IAAI,GAAGP,YAAY,CAAC,CAAC;IACzB,OAAOH,SAAS,CAACK,KAAK,GAAIK,IAAI,IAAIJ,KAAK,GAAGD,KAAK,GAAGP,cAAc,CAAC,KAAK,IAAI,CAACY,IAAI,GAAG,EAAE,EAAEC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAE,EAAEL,KAAK,CAAC;EAC9G;EACA,OAAOX,UAAU,CAACU,KAAK,EAAEC,KAAK,CAAC;AACjC;AAEA,eAAeF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}