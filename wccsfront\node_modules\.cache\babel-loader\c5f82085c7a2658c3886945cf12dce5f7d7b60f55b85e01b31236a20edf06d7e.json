{"ast": null, "code": "import { createVNode, mergeProps, isVNode } from 'vue';\nimport Table from '../table-grid.mjs';\nfunction _isSlot(s) {\n  return typeof s === \"function\" || Object.prototype.toString.call(s) === \"[object Object]\" && !isVNode(s);\n}\nconst LeftTable = (props, {\n  slots\n}) => {\n  if (!props.columns.length) return;\n  const {\n    leftTableRef,\n    ...rest\n  } = props;\n  return createVNode(Table, mergeProps({\n    \"ref\": leftTableRef\n  }, rest), _isSlot(slots) ? slots : {\n    default: () => [slots]\n  });\n};\nvar LeftTable$1 = LeftTable;\nexport { LeftTable$1 as default };", "map": {"version": 3, "names": ["props", "columns", "length", "leftTableRef", "rest", "createVNode", "Table", "mergeProps", "_isSlot", "slots", "default", "LeftTable$1", "LeftTable"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/left-table.tsx"], "sourcesContent": ["import Table from '../table-grid'\n\nimport type { FunctionalComponent, Ref } from 'vue'\nimport type { TableV2GridProps } from '../grid'\nimport type { TableGridInstance } from '../table-grid'\n\ntype LeftTableProps = TableV2GridProps & {\n  leftTableRef: Ref<TableGridInstance | undefined>\n}\n\nconst LeftTable: FunctionalComponent<LeftTableProps> = (props, { slots }) => {\n  if (!props.columns.length) return\n\n  const { leftTableRef, ...rest } = props\n\n  return (\n    <Table ref={leftTableRef} {...rest}>\n      {slots}\n    </Table>\n  )\n}\n\nexport default LeftTable\n"], "mappings": ";;;;;;;AAUA,MAAM;EAA2D,KAAAA,KAAA,CAAAC,OAAA,CAAAC,MAAA,EAAY;EAC3E,MAAU;IAEJC,YAAA;IAAE,GAAFC;MAAmBJ,KAAA;EAAnB,OAANK,WAAA,CAAAC,KAAA,EAAAC,UAAA;IAEA,OAAAJ;KACcC,IAAA,GAAAI,OAAA,CAAAC,KAAA,IAAAA,KAAA;IADdC,OAAA,EAAAA,CAAA,MAEKD,KAFL;EAAA;AAAA;AAKD,IAAAE,WAAA,GAVDC,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}