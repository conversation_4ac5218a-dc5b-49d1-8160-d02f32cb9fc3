{"ast": null, "code": "import { createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, normalizeStyle as _normalizeStyle, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass, withModifiers as _withModifiers, createBlock as _createBlock } from \"vue\";\nimport _imports_0 from '../assets/会话详情面板空页.png';\nconst _hoisted_1 = {\n  class: \"operate-area\"\n};\nconst _hoisted_2 = {\n  key: 0,\n  class: \"detailChat-info\"\n};\nconst _hoisted_3 = {\n  class: \"sender\"\n};\nconst _hoisted_4 = {\n  class: \"receiver\"\n};\nconst _hoisted_5 = {\n  key: 1,\n  class: \"detailChat-info\"\n};\nconst _hoisted_6 = {\n  class: \"sender\"\n};\nconst _hoisted_7 = {\n  class: \"refresh-area\"\n};\nconst _hoisted_8 = {\n  class: \"search-area\"\n};\nconst _hoisted_9 = {\n  class: \"filter-options\"\n};\nconst _hoisted_10 = {\n  class: \"filter-type\"\n};\nconst _hoisted_11 = {\n  key: 1,\n  class: \"loading-more\"\n};\nconst _hoisted_12 = {\n  key: 2\n};\nconst _hoisted_13 = {\n  key: 0,\n  class: \"avatar\"\n};\nconst _hoisted_14 = [\"src\"];\nconst _hoisted_15 = {\n  key: 1,\n  class: \"message-content\"\n};\nconst _hoisted_16 = {\n  class: \"sender-info\"\n};\nconst _hoisted_17 = {\n  class: \"sender-name\"\n};\nconst _hoisted_18 = {\n  class: \"message-time\"\n};\nconst _hoisted_19 = {\n  key: 0,\n  class: \"message-bubble text-content\"\n};\nconst _hoisted_20 = [\"innerHTML\"];\nconst _hoisted_21 = [\"onClick\"];\nconst _hoisted_22 = {\n  class: \"left\"\n};\nconst _hoisted_23 = {\n  class: \"file-name\"\n};\nconst _hoisted_24 = {\n  class: \"file-size\"\n};\nconst _hoisted_25 = {\n  class: \"right\"\n};\nconst _hoisted_26 = [\"src\"];\nconst _hoisted_27 = [\"onClick\"];\nconst _hoisted_28 = [\"src\"];\nconst _hoisted_29 = {\n  key: 3,\n  class: \"message-bubble voice-content\"\n};\nconst _hoisted_30 = {\n  class: \"voice-wrapper\"\n};\nconst _hoisted_31 = {\n  class: \"voice-controls\"\n};\nconst _hoisted_32 = {\n  class: \"voice-duration\"\n};\nconst _hoisted_33 = {\n  class: \"voice-visual-container\"\n};\nconst _hoisted_34 = {\n  class: \"voice-waveform-placeholder\"\n};\nconst _hoisted_35 = {\n  class: \"voice-bars\"\n};\nconst _hoisted_36 = [\"src\", \"onPlay\", \"onPause\", \"onEnded\", \"onError\", \"onLoadedmetadata\"];\nconst _hoisted_37 = {\n  key: 4,\n  class: \"message-bubble video-content\"\n};\nconst _hoisted_38 = {\n  class: \"video-container\"\n};\nconst _hoisted_39 = [\"src\", \"onPlay\", \"onPause\", \"onEnded\", \"onError\"];\nconst _hoisted_40 = {\n  key: 5,\n  class: \"message-bubble card-content\"\n};\nconst _hoisted_41 = {\n  class: \"card-container\"\n};\nconst _hoisted_42 = {\n  class: \"top\"\n};\nconst _hoisted_43 = {\n  class: \"left\"\n};\nconst _hoisted_44 = {\n  class: \"card-company\"\n};\nconst _hoisted_45 = {\n  class: \"card-userId\"\n};\nconst _hoisted_46 = {\n  class: \"card-Name\"\n};\nconst _hoisted_47 = {\n  class: \"right\"\n};\nconst _hoisted_48 = {\n  class: \"card-avatar\"\n};\nconst _hoisted_49 = [\"src\"];\nconst _hoisted_50 = {\n  key: 6,\n  class: \"message-bubble link-content\"\n};\nconst _hoisted_51 = [\"onClick\"];\nconst _hoisted_52 = {\n  class: \"link-image-container\"\n};\nconst _hoisted_53 = [\"src\"];\nconst _hoisted_54 = {\n  class: \"link-content\"\n};\nconst _hoisted_55 = {\n  class: \"link-description\"\n};\nconst _hoisted_56 = {\n  class: \"link-url\"\n};\nconst _hoisted_57 = {\n  class: \"url-text\"\n};\nconst _hoisted_58 = {\n  key: 2,\n  class: \"recall-lable\"\n};\nconst _hoisted_59 = {\n  key: 3,\n  class: \"agree\"\n};\nconst _hoisted_60 = {\n  class: \"agree-time\"\n};\nconst _hoisted_61 = {\n  key: 4,\n  class: \"disagree\"\n};\nconst _hoisted_62 = {\n  class: \"disagree-time\"\n};\nconst _hoisted_63 = {\n  key: 1,\n  class: \"switch-label\"\n};\nconst _hoisted_64 = {\n  class: \"switch-time\"\n};\nconst _hoisted_65 = [\"src\"];\nconst _hoisted_66 = {\n  class: \"file-dialog-content\"\n};\nconst _hoisted_67 = {\n  class: \"file-info\"\n};\nconst _hoisted_68 = [\"src\"];\nconst _hoisted_69 = {\n  class: \"file-details\"\n};\nconst _hoisted_70 = {\n  class: \"file-name\"\n};\nconst _hoisted_71 = {\n  class: \"file-size\"\n};\nconst _hoisted_72 = {\n  class: \"file-actions\"\n};\nconst _hoisted_73 = {\n  class: \"preview-container\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_input = _resolveComponent(\"el-input\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_tooltip = _resolveComponent(\"el-tooltip\");\n  const _component_el_alert = _resolveComponent(\"el-alert\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [$setup.chatBoardVisible ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"chat-board\",\n    style: _normalizeStyle($setup.chatBoardStyle)\n  }, [_createElementVNode(\"div\", _hoisted_1, [$setup.selectedChat.type == '' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createCommentVNode(\" <span class=\\\"sender\\\">{{ whichDetailChat.sender }}</span> \"), _createElementVNode(\"span\", _hoisted_3, _toDisplayString($setup.selectedChat.fromName), 1 /* TEXT */), _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \" 与 \", -1 /* HOISTED */)), _createCommentVNode(\" <span class=\\\"receiver\\\">{{ whichDetailChat.receiver }}</span> \"), _createElementVNode(\"span\", _hoisted_4, _toDisplayString($setup.selectedChat.toName), 1 /* TEXT */), _cache[7] || (_cache[7] = _createElementVNode(\"span\", null, \" 的会话记录\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true), $setup.selectedChat.type == 'groupchat' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [_createCommentVNode(\" <span class=\\\"sender\\\">{{ whichDetailChat.sender }}</span> \"), _createElementVNode(\"span\", _hoisted_6, \"群聊 \" + _toDisplayString($setup.selectedChat.fromName) + \" 的会话记录\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_icon, {\n    size: \"18px\",\n    onClick: $setup.refreshChat\n  }, {\n    default: _withCtx(() => [_createVNode($setup[\"Refresh\"])]),\n    _: 1 /* STABLE */\n  })])]), _createCommentVNode(\" 顶部搜索区域 \"), _createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" <el-tooltip content=\\\"搜索功能开发中，敬请期待\\\" placement=\\\"top\\\" effect=\\\"light\\\"> \"), _createVNode(_component_el_input, {\n    modelValue: $setup.searchName,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.searchName = $event),\n    placeholder: \"搜索聊天记录\",\n    class: \"search-input\",\n    clearable: \"\"\n  }, {\n    prefix: _withCtx(() => [_createVNode(_component_el_icon, {\n      class: \"search-icon\"\n    }, {\n      default: _withCtx(() => [_createVNode($setup[\"Search\"])]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" </el-tooltip> \"), _createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_tooltip, {\n    content: \"功能开发中，敬请期待\",\n    placement: \"top\",\n    effect: \"light\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      plain: \"\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Filter\"])]),\n        _: 1 /* STABLE */\n      }), _cache[8] || (_cache[8] = _createTextVNode(\" 筛选条件 \"))]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })]), _createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_tooltip, {\n    content: \"功能开发中，敬请期待\",\n    placement: \"top\",\n    effect: \"light\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      plain: \"\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"ChatDotRound\"])]),\n        _: 1 /* STABLE */\n      }), _cache[9] || (_cache[9] = _createTextVNode(\" 消息 \"))]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_tooltip, {\n    content: \"功能开发中，敬请期待\",\n    placement: \"top\",\n    effect: \"light\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      plain: \"\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Document\"])]),\n        _: 1 /* STABLE */\n      }), _cache[10] || (_cache[10] = _createTextVNode(\" 文件 \"))]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_tooltip, {\n    content: \"功能开发中，敬请期待\",\n    placement: \"top\",\n    effect: \"light\"\n  }, {\n    default: _withCtx(() => [_createVNode(_component_el_button, {\n      type: \"primary\",\n      plain: \"\",\n      size: \"small\"\n    }, {\n      default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n        default: _withCtx(() => [_createVNode($setup[\"Picture\"])]),\n        _: 1 /* STABLE */\n      }), _cache[11] || (_cache[11] = _createTextVNode(\" 图片 \"))]),\n      _: 1 /* STABLE */\n    })]),\n    _: 1 /* STABLE */\n  })])]), _createCommentVNode(\" 聊天内容区域 \"), _createElementVNode(\"div\", {\n    class: \"chat-content\",\n    ref: \"chatContentRef\",\n    onScroll: $setup.handleScroll,\n    style: _normalizeStyle({\n      pointerEvents: $setup.isRefreshing ? 'none' : 'auto'\n    })\n  }, [_createCommentVNode(\" 添加遮罩层 \"), $setup.isRefreshing ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"content-overlay\",\n    style: _normalizeStyle({\n      top: $setup.overlayTop + 'px'\n    })\n  }, [_createVNode(_component_el_icon, {\n    class: \"loading-icon\",\n    size: 30\n  }, {\n    default: _withCtx(() => [_createVNode($setup[\"Loading\"])]),\n    _: 1 /* STABLE */\n  }), _cache[12] || (_cache[12] = _createElementVNode(\"span\", {\n    class: \"loading-text\"\n  }, \"刷新中...\", -1 /* HOISTED */))], 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 加载更多的提示 \"), $setup.isLoading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_11, [_createVNode(_component_el_icon, {\n    class: \"loading-icon\",\n    size: 20\n  }, {\n    default: _withCtx(() => [_createVNode($setup[\"Loading\"])]),\n    _: 1 /* STABLE */\n  }), _cache[13] || (_cache[13] = _createElementVNode(\"span\", null, \"加载更多消息...\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true), $setup.isProcessing ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.chatDetailMessages, (message, index) => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: index\n    }, [_createCommentVNode(\" 消息内容 \"), message.action !== 'switch' ? (_openBlock(), _createElementBlock(\"div\", {\n      key: 0,\n      class: _normalizeClass(['message-container', $setup.handleMessageType(message)])\n    }, [_createCommentVNode(\" 头像 \"), _createCommentVNode(\" 头像、时间、具体消息内容的显示与否直接通过message.msgtype是否等于aggree、disagree控制 \"), message.msgType !== 'agree' && message.msgType !== 'disagree' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [_createElementVNode(\"img\", {\n      src: $setup.avatar_from_or_to(message),\n      alt: \"人员头像\"\n    }, null, 8 /* PROPS */, _hoisted_14)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 消息气泡 \"), message.msgType !== 'agree' && message.msgType !== 'disagree' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [_createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"span\", _hoisted_17, _toDisplayString($setup.preprocessedNames.get(message.fromUser)), 1 /* TEXT */), _createElementVNode(\"span\", {\n      class: _normalizeClass([\"sender-lable\", $setup.getLableClass(message)])\n    }, _toDisplayString($setup.getLable(message)), 3 /* TEXT, CLASS */), _createElementVNode(\"span\", _hoisted_18, _toDisplayString($setup.convertTime(message.msgTime)), 1 /* TEXT */)]), _createCommentVNode(\" 此处区分不同消息类型 \"), _createCommentVNode(\" 纯文本消息类型 \"), $setup.handleMessageContentType(message) === 'text' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_19, [_createElementVNode(\"p\", {\n      innerHTML: $setup.highlightText($setup.messageContent[message.msgid]?.content || '', $setup.searchName)\n    }, null, 8 /* PROPS */, _hoisted_20)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 文件消息类型 \"), $setup.handleMessageContentType(message) === 'file' ? (_openBlock(), _createElementBlock(\"div\", {\n      key: 1,\n      class: \"message-bubble file-content\",\n      onClick: $event => $setup.getFileContent($setup.messageContent[message.msgid])\n    }, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"p\", _hoisted_23, _toDisplayString($setup.messageContent[message.msgid]?.filename), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_24, _toDisplayString($setup.formatFileSize($setup.messageContent[message.msgid]?.filesize)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_25, [_createElementVNode(\"img\", {\n      src: $setup.getFileIcon($setup.messageContent[message.msgid]?.fileext),\n      alt: \"文件类型\"\n    }, null, 8 /* PROPS */, _hoisted_26)])], 8 /* PROPS */, _hoisted_21)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 图片消息类型  \"), $setup.handleMessageContentType(message) === 'image' ? (_openBlock(), _createElementBlock(\"div\", {\n      key: 2,\n      class: \"message-bubble image-content\",\n      onClick: $event => $setup.handleImageClick(message)\n    }, [_createElementVNode(\"img\", {\n      src: $setup.getImageUrl(message),\n      alt: \"图片\"\n    }, null, 8 /* PROPS */, _hoisted_28)], 8 /* PROPS */, _hoisted_27)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" emotion消息类型 ：通过handleMessageContentType方法转换使用图片消息类型的模板\"), _createCommentVNode(\" 语音类型消息 \"), $setup.handleMessageContentType(message) === 'voice' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_29, [_createElementVNode(\"div\", _hoisted_30, [_createElementVNode(\"div\", _hoisted_31, [_createVNode(_component_el_button, {\n      icon: $setup.getVoicePlayIcon(message.msgid),\n      circle: \"\",\n      size: \"small\",\n      onClick: $event => $setup.toggleVoicePlay(message.msgid, $setup.messageContent[message.msgid]),\n      loading: $setup.voiceStates[message.msgid]?.loading,\n      class: \"voice-play-btn\"\n    }, null, 8 /* PROPS */, [\"icon\", \"onClick\", \"loading\"]), _createElementVNode(\"span\", _hoisted_32, _toDisplayString($setup.formatVoiceDuration($setup.messageContent[message.msgid]?.voice_size)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_33, [_createCommentVNode(\" 简化的语音播放界面 \"), _createElementVNode(\"div\", _hoisted_34, [_createElementVNode(\"div\", _hoisted_35, [(_openBlock(), _createElementBlock(_Fragment, null, _renderList(20, i => {\n      return _createElementVNode(\"div\", {\n        class: \"bar\",\n        key: i\n      });\n    }), 64 /* STABLE_FRAGMENT */))]), _cache[14] || (_cache[14] = _createElementVNode(\"span\", {\n      class: \"voice-text\"\n    }, \"语音消息\", -1 /* HOISTED */))])]), _createCommentVNode(\" 隐藏的audio元素用于播放控制 \"), _createElementVNode(\"audio\", {\n      ref_for: true,\n      ref: el => $setup.setAudioRef(message.msgid, el),\n      src: $setup.voiceUrls[$setup.get_sdkfileid_from_message(message)],\n      onPlay: $event => $setup.handleAudioPlay(message.msgid),\n      onPause: $event => $setup.handleAudioPause(message.msgid),\n      onEnded: $event => $setup.handleAudioEnded(message.msgid),\n      onError: $event => $setup.handleAudioError(message.msgid),\n      onLoadedmetadata: $event => $setup.handleAudioLoaded(message.msgid),\n      style: {\n        \"display\": \"none\"\n      }\n    }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_36)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 视频消息类型  \"), $setup.handleMessageContentType(message) === 'video' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_37, [_createElementVNode(\"div\", _hoisted_38, [_createElementVNode(\"video\", {\n      controls: \"\",\n      class: \"video-player\",\n      src: $setup.getVideoUrl(message),\n      onPlay: $event => $setup.handleVideoPlay(message),\n      onPause: $event => $setup.handleVideoPause(message),\n      onEnded: $event => $setup.handleVideoEnded(message),\n      onError: $event => $setup.handleVideoError(message),\n      ref_for: true,\n      ref: el => $setup.setVideoRef(message.msgid, el)\n    }, [...(_cache[15] || (_cache[15] = [_createElementVNode(\"span\", null, \"您的浏览器不支持视频播放\", -1 /* HOISTED */)]))], 40 /* PROPS, NEED_HYDRATION */, _hoisted_39)])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 卡片/名片类型消息 \"), $setup.handleMessageContentType(message) === 'card' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_40, [_createElementVNode(\"div\", _hoisted_41, [_createElementVNode(\"div\", _hoisted_42, [_createElementVNode(\"div\", _hoisted_43, [_createElementVNode(\"div\", _hoisted_44, [_createElementVNode(\"span\", null, _toDisplayString($setup.messageContent[message.msgid]?.corpname), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_45, [_createElementVNode(\"span\", null, _toDisplayString($setup.getCardUserId($setup.messageContent[message.msgid]?.userid)), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_46, [_createElementVNode(\"span\", null, _toDisplayString($setup.preprocessedNames.get(message.fromUser)), 1 /* TEXT */)])]), _createElementVNode(\"div\", _hoisted_47, [_createElementVNode(\"div\", _hoisted_48, [_createElementVNode(\"img\", {\n      src: $setup.avatar_from_or_to(message),\n      alt: \"卡片头像\"\n    }, null, 8 /* PROPS */, _hoisted_49)])])]), _cache[16] || (_cache[16] = _createElementVNode(\"div\", {\n      class: \"bottom\"\n    }, [_createElementVNode(\"span\", null, \"个人名片\")], -1 /* HOISTED */))])])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 链接消息 \"), $setup.handleMessageContentType(message) === 'link' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_50, [_createElementVNode(\"div\", {\n      class: \"link-card-container\",\n      onClick: $event => $setup.handleLinkClick($setup.messageContent[message.msgid]?.link_url)\n    }, [_createElementVNode(\"div\", _hoisted_52, [_createElementVNode(\"img\", {\n      src: $setup.messageContent[message.msgid]?.image_url,\n      alt: \"链接图片\",\n      class: \"link-image\"\n    }, null, 8 /* PROPS */, _hoisted_53)]), _createElementVNode(\"div\", _hoisted_54, [_createElementVNode(\"div\", {\n      class: _normalizeClass([\"link-title\", _ctx.hasSpecialSymbol ? 'title-with-symbol' : ''])\n    }, _toDisplayString($setup.messageContent[message.msgid]?.title), 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_55, _toDisplayString($setup.messageContent[message.msgid]?.description), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_56, [_createElementVNode(\"span\", _hoisted_57, _toDisplayString($setup.truncateUrl($setup.messageContent[message.msgid]?.link_url)), 1 /* TEXT */), _cache[17] || (_cache[17] = _createElementVNode(\"span\", {\n      class: \"icon-external-link\"\n    }, \"→\", -1 /* HOISTED */))])])], 8 /* PROPS */, _hoisted_51)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), message.msgType === 'revoke' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_58, [_createVNode(_component_el_icon, null, {\n      default: _withCtx(() => [_createVNode($setup[\"RefreshLeft\"])]),\n      _: 1 /* STABLE */\n    }), _cache[18] || (_cache[18] = _createElementVNode(\"span\", null, \"撤回消息\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 同意会话聊天内容 \"), $setup.handleMessageContentType(message) === 'agree' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_59, [_createElementVNode(\"div\", _hoisted_60, _toDisplayString($setup.convertTime($setup.messageContent[message.msgid]?.agree_time)), 1 /* TEXT */), _cache[19] || (_cache[19] = _createElementVNode(\"p\", null, \"客户同意会话存档\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 不同意会话聊天内容 \"), $setup.handleMessageContentType(message) === 'disagree' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_61, [_createElementVNode(\"div\", _hoisted_62, _toDisplayString($setup.convertTime($setup.messageContent[message.msgid]?.disagree_time)), 1 /* TEXT */), _cache[20] || (_cache[20] = _createElementVNode(\"p\", null, \"客户不同意会话存档\", -1 /* HOISTED */))])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 切换企业日志 \"), message.action === 'switch' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_63, [_createElementVNode(\"div\", _hoisted_64, _toDisplayString($setup.convertTime(message.time)), 1 /* TEXT */), _createElementVNode(\"p\", null, _toDisplayString(message.user) + \" 切换了企业\", 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true)]);\n  }), 128 /* KEYED_FRAGMENT */))])) : _createCommentVNode(\"v-if\", true)], 36 /* STYLE, NEED_HYDRATION */)], 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), !$setup.chatBoardVisible ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 1,\n    class: \"chat-board-nonShow\",\n    style: _normalizeStyle($setup.chatBoardStyle)\n  }, _cache[21] || (_cache[21] = [_createElementVNode(\"h3\", {\n    class: \"non-picked\"\n  }, \"未选择查看任何会话\", -1 /* HOISTED */), _createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"会话详情面板空页\"\n  }, null, -1 /* HOISTED */)]), 4 /* STYLE */)) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 添加聊天记录弹窗组件 \"), _createVNode($setup[\"ChatRecordPopUp\"], {\n    visible: $setup.chatRecordVisible,\n    \"onUpdate:visible\": _cache[1] || (_cache[1] = $event => $setup.chatRecordVisible = $event),\n    title: $setup.currentChatRecord?.title,\n    \"chat-records\": $setup.currentChatRecord?.item || [],\n    onClose: _cache[2] || (_cache[2] = $event => $setup.chatRecordVisible = false)\n  }, null, 8 /* PROPS */, [\"visible\", \"title\", \"chat-records\"]), _createCommentVNode(\" 添加图片预览遮罩层 \"), $setup.imagePreviewVisible ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 2,\n    class: \"image-preview-mask\",\n    onClick: $setup.closeImagePreview\n  }, [_createElementVNode(\"div\", {\n    class: \"image-preview-container\",\n    onClick: _cache[3] || (_cache[3] = _withModifiers(() => {}, [\"stop\"])),\n    onWheel: _withModifiers($setup.handleWheel, [\"prevent\"])\n  }, [_createElementVNode(\"img\", {\n    src: $setup.previewImageUrl,\n    alt: \"预览图片\",\n    class: \"preview-image\",\n    style: _normalizeStyle({\n      width: `${$setup.baseWidth * $setup.scale}px`,\n      height: `${$setup.baseHeight * $setup.scale}px`,\n      minWidth: '100px',\n      minHeight: '100px'\n    }),\n    onLoad: $setup.handleImageLoad\n  }, null, 44 /* STYLE, PROPS, NEED_HYDRATION */, _hoisted_65)], 32 /* NEED_HYDRATION */)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 文件操作弹窗 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.fileDialogVisible,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = $event => $setup.fileDialogVisible = $event),\n    title: \"文件操作\",\n    width: \"30%\",\n    \"show-close\": true,\n    \"close-on-click-modal\": true,\n    \"close-on-press-escape\": true,\n    class: \"file-dialog\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_66, [_createElementVNode(\"div\", _hoisted_67, [_createElementVNode(\"img\", {\n      src: $setup.getFileIcon($setup.currentFile?.fileext),\n      alt: \"文件类型\",\n      class: \"file-icon\"\n    }, null, 8 /* PROPS */, _hoisted_68), _createElementVNode(\"div\", _hoisted_69, [_createElementVNode(\"p\", _hoisted_70, _toDisplayString($setup.currentFile?.filename), 1 /* TEXT */), _createElementVNode(\"p\", _hoisted_71, _toDisplayString($setup.formatFileSize($setup.currentFile?.filesize)), 1 /* TEXT */)])]), _createVNode(_component_el_alert, {\n      title: \"当前文件预览只支持docx、xlsx、pdf格式文件\",\n      type: \"info\",\n      closable: false,\n      \"show-icon\": \"\",\n      style: {\n        \"margin-bottom\": \"20px\"\n      }\n    }), _createElementVNode(\"div\", _hoisted_72, [_createVNode(_component_el_button, {\n      type: \"primary\",\n      onClick: $setup.previewFile,\n      disabled: !['docx', 'xlsx', 'pdf'].includes($setup.currentFile?.fileext?.toLowerCase())\n    }, {\n      default: _withCtx(() => _cache[22] || (_cache[22] = [_createTextVNode(\" 预览 \")])),\n      _: 1 /* STABLE */\n    }, 8 /* PROPS */, [\"disabled\"]), _createVNode(_component_el_button, {\n      type: \"success\",\n      onClick: $setup.downloadFile\n    }, {\n      default: _withCtx(() => _cache[23] || (_cache[23] = [_createTextVNode(\"下载\")])),\n      _: 1 /* STABLE */\n    })])])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createCommentVNode(\" 文件预览弹窗 \"), _createVNode(_component_el_dialog, {\n    modelValue: $setup.previewDialogVisible,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = $event => $setup.previewDialogVisible = $event),\n    title: $setup.currentFile?.filename,\n    width: \"80%\",\n    \"show-close\": true,\n    \"close-on-click-modal\": false,\n    \"close-on-press-escape\": true,\n    class: \"preview-dialog\"\n  }, {\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_73, [_createCommentVNode(\" docx文件预览 \"), $setup.currentFile?.fileext?.toLowerCase() === 'docx' ? (_openBlock(), _createBlock($setup[\"VueOfficeDocx\"], {\n      key: 0,\n      src: $setup.currentFileUrl,\n      onRendered: $setup.handleDocxRendered,\n      onError: $setup.handlePreviewError\n    }, null, 8 /* PROPS */, [\"src\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" excel文件预览 \"), $setup.currentFile?.fileext?.toLowerCase() === 'xlsx' ? (_openBlock(), _createBlock($setup[\"VueOfficeExcel\"], {\n      key: 1,\n      src: $setup.currentFileUrl,\n      onRendered: $setup.handleExcelRendered,\n      onError: $setup.handlePreviewError\n    }, null, 8 /* PROPS */, [\"src\"])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" pdf文件预览 \"), $setup.currentFile?.fileext?.toLowerCase() === 'pdf' ? (_openBlock(), _createBlock($setup[\"VueOfficePdf\"], {\n      key: 2,\n      src: $setup.currentFileUrl,\n      onRendered: $setup.handlePdfRendered,\n      onError: $setup.handlePreviewError\n    }, null, 8 /* PROPS */, [\"src\"])) : _createCommentVNode(\"v-if\", true)])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "class", "key", "_createElementBlock", "_Fragment", "$setup", "chatBoardVisible", "style", "_normalizeStyle", "chatBoardStyle", "_createElementVNode", "_hoisted_1", "selectedC<PERSON>", "type", "_hoisted_2", "_createCommentVNode", "_hoisted_3", "_toDisplayString", "fromName", "_hoisted_4", "to<PERSON>ame", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_createVNode", "_component_el_icon", "size", "onClick", "refreshChat", "default", "_withCtx", "_", "_hoisted_8", "_component_el_input", "modelValue", "searchName", "_cache", "$event", "placeholder", "clearable", "prefix", "_hoisted_9", "_component_el_tooltip", "content", "placement", "effect", "_component_el_button", "plain", "_createTextVNode", "_hoisted_10", "ref", "onScroll", "handleScroll", "pointerEvents", "isRefreshing", "top", "overlayTop", "isLoading", "_hoisted_11", "isProcessing", "_hoisted_12", "_renderList", "chatDetailMessages", "message", "index", "action", "_normalizeClass", "handleMessageType", "msgType", "_hoisted_13", "src", "avatar_from_or_to", "alt", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "preprocessedNames", "get", "fromUser", "getLableClass", "getLable", "_hoisted_18", "convertTime", "msgTime", "handleMessageContentType", "_hoisted_19", "innerHTML", "highlightText", "messageContent", "msgid", "_hoisted_20", "getFileContent", "_hoisted_22", "_hoisted_23", "filename", "_hoisted_24", "formatFileSize", "filesize", "_hoisted_25", "getFileIcon", "fileext", "_hoisted_26", "_hoisted_21", "handleImageClick", "getImageUrl", "_hoisted_28", "_hoisted_27", "_hoisted_29", "_hoisted_30", "_hoisted_31", "icon", "getVoicePlayIcon", "circle", "toggleVoicePlay", "loading", "voiceStates", "_hoisted_32", "formatVoiceDuration", "voice_size", "_hoisted_33", "_hoisted_34", "_hoisted_35", "i", "ref_for", "el", "setAudioRef", "voiceUrls", "get_sdkfileid_from_message", "onPlay", "handleAudioPlay", "onPause", "handleAudioPause", "onEnded", "handleAudioEnded", "onError", "handleAudioError", "onLoadedmetadata", "handleAudioLoaded", "_hoisted_36", "_hoisted_37", "_hoisted_38", "controls", "getVideoUrl", "handleVideoPlay", "handleVideoPause", "handleVideoEnded", "handleVideoError", "setVideoRef", "_hoisted_39", "_hoisted_40", "_hoisted_41", "_hoisted_42", "_hoisted_43", "_hoisted_44", "corpname", "_hoisted_45", "getCardUserId", "userid", "_hoisted_46", "_hoisted_47", "_hoisted_48", "_hoisted_49", "_hoisted_50", "handleLinkClick", "link_url", "_hoisted_52", "image_url", "_hoisted_53", "_hoisted_54", "_ctx", "hasSpecialSymbol", "title", "_hoisted_55", "description", "_hoisted_56", "_hoisted_57", "truncateUrl", "_hoisted_51", "_hoisted_58", "_hoisted_59", "_hoisted_60", "agree_time", "_hoisted_61", "_hoisted_62", "disagree_time", "_hoisted_63", "_hoisted_64", "time", "user", "visible", "chatRecordVisible", "currentChatRecord", "item", "onClose", "imagePreviewVisible", "closeImagePreview", "_withModifiers", "onWheel", "handleWheel", "previewImageUrl", "baseWidth", "scale", "baseHeight", "onLoad", "handleImageLoad", "_hoisted_65", "_component_el_dialog", "fileDialogVisible", "width", "_hoisted_66", "_hoisted_67", "currentFile", "_hoisted_68", "_hoisted_69", "_hoisted_70", "_hoisted_71", "_component_el_alert", "closable", "_hoisted_72", "previewFile", "disabled", "includes", "toLowerCase", "downloadFile", "previewDialogVisible", "_hoisted_73", "_createBlock", "currentFileUrl", "onRendered", "handleDocxRendered", "handlePreviewError", "handleExcelRendered", "handlePdfRendered"], "sources": ["D:\\Code\\VUE\\WCCSFront\\wccsfront\\src\\components\\DetailChatBoard.vue"], "sourcesContent": ["<template>\n\n    <div class=\"chat-board\" :style=\"chatBoardStyle\" v-if=\"chatBoardVisible\">\n        <div class=\"operate-area\">\n            <div class=\"detailChat-info\" v-if=\"selectedChat.type == ''\">\n                <!-- <span class=\"sender\">{{ whichDetailChat.sender }}</span> -->\n                <span class=\"sender\">{{ selectedChat.fromName }}</span>\n                <span> 与 </span>\n                <!-- <span class=\"receiver\">{{ whichDetailChat.receiver }}</span> -->\n                <span class=\"receiver\">{{ selectedChat.toName }}</span>\n                <span> 的会话记录</span>\n            </div>\n            <div class=\"detailChat-info\" v-if=\"selectedChat.type == 'groupchat'\">\n                <!-- <span class=\"sender\">{{ whichDetailChat.sender }}</span> -->\n                <span class=\"sender\">群聊 {{ selectedChat.fromName }} 的会话记录</span>\n            </div>\n            <div class=\"refresh-area\">\n                <el-icon size=\"18px\" @click=\"refreshChat\">\n                    <Refresh />\n                </el-icon>\n            </div>\n        </div>\n        <!-- 顶部搜索区域 -->\n        <div class=\"search-area\">\n            <!-- <el-tooltip content=\"搜索功能开发中，敬请期待\" placement=\"top\" effect=\"light\"> -->\n            <el-input v-model=\"searchName\" placeholder=\"搜索聊天记录\" class=\"search-input\" clearable>\n                <template #prefix>\n                    <el-icon class=\"search-icon\">\n                        <Search />\n                    </el-icon>\n                </template>\n            </el-input>\n            <!-- </el-tooltip> -->\n\n            <div class=\"filter-options\">\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <Filter />\n                        </el-icon>\n                        筛选条件\n                    </el-button>\n                </el-tooltip>\n            </div>\n\n            <div class=\"filter-type\">\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <ChatDotRound />\n                        </el-icon>\n                        消息\n                    </el-button>\n                </el-tooltip>\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <Document />\n                        </el-icon>\n                        文件\n                    </el-button>\n                </el-tooltip>\n                <el-tooltip content=\"功能开发中，敬请期待\" placement=\"top\" effect=\"light\">\n                    <el-button type=\"primary\" plain size=\"small\">\n                        <el-icon>\n                            <Picture />\n                        </el-icon>\n                        图片\n                    </el-button>\n                </el-tooltip>\n            </div>\n\n        </div>\n\n        <!-- 聊天内容区域 -->\n        <div class=\"chat-content\" ref=\"chatContentRef\" @scroll=\"handleScroll\"\n            :style=\"{ pointerEvents: isRefreshing ? 'none' : 'auto' }\">\n            <!-- 添加遮罩层 -->\n            <div class=\"content-overlay\" v-if=\"isRefreshing\" :style=\"{ top: overlayTop + 'px' }\">\n                <el-icon class=\"loading-icon\" :size=\"30\">\n                    <Loading />\n                </el-icon>\n                <span class=\"loading-text\">刷新中...</span>\n            </div>\n            <!-- 加载更多的提示 -->\n            <div v-if=\"isLoading\" class=\"loading-more\">\n                <el-icon class=\"loading-icon\" :size=\"20\">\n                    <Loading />\n                </el-icon>\n                <span>加载更多消息...</span>\n            </div>\n            <div v-if=\"isProcessing\">\n                <div v-for=\"(message, index) in chatDetailMessages\" :key=\"index\">\n                    <!-- 消息内容 -->\n\n                    <div :class=\"['message-container', handleMessageType(message)]\" v-if=\"message.action !== 'switch'\">\n                        <!-- 头像 -->\n                        <!-- 头像、时间、具体消息内容的显示与否直接通过message.msgtype是否等于aggree、disagree控制 -->\n                        <div class=\"avatar\" v-if=\"message.msgType !== 'agree' && message.msgType !== 'disagree'\">\n                            <img :src=\"avatar_from_or_to(message)\" alt=\"人员头像\">\n                        </div>\n\n                        <!-- 消息气泡 -->\n                        <div class=\"message-content\"\n                            v-if=\"message.msgType !== 'agree' && message.msgType !== 'disagree'\">\n                            <div class=\"sender-info\">\n                                <span class=\"sender-name\">{{ preprocessedNames.get(message.fromUser) }}</span>\n                                <span class=\"sender-lable\" :class=\"getLableClass(message)\">{{ getLable(message)\n                                    }}</span>\n                                <span class=\"message-time\">{{ convertTime(message.msgTime) }}</span>\n                            </div>\n                            <!-- 此处区分不同消息类型 -->\n                            <!-- 纯文本消息类型 -->\n                            <div class=\"message-bubble text-content\"\n                                v-if=\"handleMessageContentType(message) === 'text'\">\n                                <p v-html=\"highlightText(messageContent[message.msgid]?.content || '', searchName)\"></p>\n                            </div>\n\n                            <!-- 文件消息类型 -->\n                            <div class=\"message-bubble file-content\" v-if=\"handleMessageContentType(message) === 'file'\"\n                                @click=\"getFileContent(messageContent[message.msgid])\">\n                                <div class=\"left\">\n                                    <p class=\"file-name\">{{ messageContent[message.msgid]?.filename }}</p>\n                                    <p class=\"file-size\">{{ formatFileSize(messageContent[message.msgid]?.filesize) }}\n                                    </p>\n                                </div>\n                                <div class=\"right\">\n                                    <img :src=\"getFileIcon(messageContent[message.msgid]?.fileext)\" alt=\"文件类型\">\n                                </div>\n                            </div>\n\n                            <!-- 图片消息类型  -->\n                            <div class=\"message-bubble image-content\"\n                                v-if=\"handleMessageContentType(message) === 'image'\" @click=\"handleImageClick(message)\">\n                                <img :src=\"getImageUrl(message)\" alt=\"图片\">\n                            </div>\n\n                            <!-- emotion消息类型 ：通过handleMessageContentType方法转换使用图片消息类型的模板-->\n\n                            <!-- 语音类型消息 -->\n                            <div class=\"message-bubble voice-content\"\n                                v-if=\"handleMessageContentType(message) === 'voice'\">\n                                <div class=\"voice-wrapper\">\n                                    <div class=\"voice-controls\">\n                                        <el-button\n                                            :icon=\"getVoicePlayIcon(message.msgid)\"\n                                            circle\n                                            size=\"small\"\n                                            @click=\"toggleVoicePlay(message.msgid, messageContent[message.msgid])\"\n                                            :loading=\"voiceStates[message.msgid]?.loading\"\n                                            class=\"voice-play-btn\">\n                                        </el-button>\n                                        <span class=\"voice-duration\">\n                                            {{ formatVoiceDuration(messageContent[message.msgid]?.voice_size) }}\n                                        </span>\n                                    </div>\n                                    <div class=\"voice-visual-container\">\n                                        <!-- 简化的语音播放界面 -->\n                                        <div class=\"voice-waveform-placeholder\">\n                                            <div class=\"voice-bars\">\n                                                <div class=\"bar\" v-for=\"i in 20\" :key=\"i\"></div>\n                                            </div>\n                                            <span class=\"voice-text\">语音消息</span>\n                                        </div>\n                                    </div>\n                                    <!-- 隐藏的audio元素用于播放控制 -->\n                                    <audio\n                                        :ref=\"el => setAudioRef(message.msgid, el)\"\n                                        :src=\"voiceUrls[get_sdkfileid_from_message(message)]\"\n                                        @play=\"handleAudioPlay(message.msgid)\"\n                                        @pause=\"handleAudioPause(message.msgid)\"\n                                        @ended=\"handleAudioEnded(message.msgid)\"\n                                        @error=\"handleAudioError(message.msgid)\"\n                                        @loadedmetadata=\"handleAudioLoaded(message.msgid)\"\n                                        style=\"display: none;\">\n                                    </audio>\n                                </div>\n                            </div>\n\n                            <!-- 视频消息类型  -->\n                            <div class=\"message-bubble video-content\"\n                                v-if=\"handleMessageContentType(message) === 'video'\">\n                                <div class=\"video-container\">\n                                    <video controls class=\"video-player\"\n                                        :src=\"getVideoUrl(message)\"\n                                        @play=\"handleVideoPlay(message)\" @pause=\"handleVideoPause(message)\"\n                                        @ended=\"handleVideoEnded(message)\" @error=\"handleVideoError(message)\"\n                                        :ref=\"el => setVideoRef(message.msgid, el)\">\n                                        <span>您的浏览器不支持视频播放</span>\n                                    </video>\n                                </div>\n                            </div>\n\n                            <!-- 卡片/名片类型消息 -->\n                            <div class=\"message-bubble card-content\"\n                                v-if=\"handleMessageContentType(message) === 'card'\">\n                                <div class=\"card-container\">\n                                    <div class=\"top\">\n                                        <div class=\"left\">\n                                            <div class=\"card-company\">\n                                                <span>{{ messageContent[message.msgid]?.corpname }}</span>\n                                            </div>\n                                            <div class=\"card-userId\">\n                                                <span>{{ getCardUserId(messageContent[message.msgid]?.userid) }}</span>\n                                            </div>\n                                            <div class=\"card-Name\">\n                                                <span>{{ preprocessedNames.get(message.fromUser) }}</span>\n                                            </div>\n                                        </div>\n                                        <div class=\"right\">\n                                            <div class=\"card-avatar\">\n                                                <img :src=\"avatar_from_or_to(message)\" alt=\"卡片头像\">\n                                            </div>\n                                        </div>\n                                    </div>\n                                    <div class=\"bottom\">\n                                        <span>个人名片</span>\n                                    </div>\n                                </div>\n                            </div>\n\n                            <!-- 链接消息 -->\n                            <div class=\"message-bubble link-content\"\n                                v-if=\"handleMessageContentType(message) === 'link'\">\n                                <div class=\"link-card-container\"\n                                    @click=\"handleLinkClick(messageContent[message.msgid]?.link_url)\">\n                                    <div class=\"link-image-container\">\n                                        <img :src=\"messageContent[message.msgid]?.image_url\" alt=\"链接图片\"\n                                            class=\"link-image\">\n                                    </div>\n                                    <div class=\"link-content\">\n                                        <div class=\"link-title\" :class=\"hasSpecialSymbol ? 'title-with-symbol' : ''\">\n                                            {{ messageContent[message.msgid]?.title }}\n                                        </div>\n                                        <div class=\"link-description\">\n                                            {{ messageContent[message.msgid]?.description }}\n                                        </div>\n                                        <div class=\"link-url\">\n                                            <span class=\"url-text\">{{\n                                                truncateUrl(messageContent[message.msgid]?.link_url)\n                                                }}</span>\n                                            <span class=\"icon-external-link\">→</span>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n\n                        </div>\n\n                        <div class=\"recall-lable\" v-if=\"message.msgType === 'revoke'\">\n                            <el-icon>\n                                <RefreshLeft />\n                            </el-icon>\n                            <span>撤回消息</span>\n                        </div>\n\n                        <!-- 同意会话聊天内容 -->\n                        <div class=\"agree\" v-if=\"handleMessageContentType(message) === 'agree'\">\n                            <div class=\"agree-time\">{{ convertTime(messageContent[message.msgid]?.agree_time) }}</div>\n                            <p>客户同意会话存档</p>\n                        </div>\n\n                        <!-- 不同意会话聊天内容 -->\n                        <div class=\"disagree\" v-if=\"handleMessageContentType(message) === 'disagree'\">\n                            <div class=\"disagree-time\">{{ convertTime(messageContent[message.msgid]?.disagree_time) }}\n                            </div>\n                            <p>客户不同意会话存档</p>\n                        </div>\n\n                    </div>\n\n                    <!-- 切换企业日志 -->\n                    <div class=\"switch-label\" v-if=\"message.action === 'switch'\">\n                        <div class=\"switch-time\">{{ convertTime(message.time) }}</div>\n                        <p>{{ message.user }} 切换了企业</p>\n                    </div>\n\n\n\n                </div>\n            </div>\n\n        </div>\n    </div>\n\n    <div class=\"chat-board-nonShow\" :style=\"chatBoardStyle\" v-if=\"!chatBoardVisible\">\n        <h3 class=\"non-picked\">未选择查看任何会话</h3>\n        <img src=\"../assets/会话详情面板空页.png\" alt=\"会话详情面板空页\">\n    </div>\n    <!-- 添加聊天记录弹窗组件 -->\n    <ChatRecordPopUp v-model:visible=\"chatRecordVisible\" :title=\"currentChatRecord?.title\"\n        :chat-records=\"currentChatRecord?.item || []\" @close=\"chatRecordVisible = false\" />\n\n    <!-- 添加图片预览遮罩层 -->\n    <div class=\"image-preview-mask\" v-if=\"imagePreviewVisible\" @click=\"closeImagePreview\">\n        <div class=\"image-preview-container\" @click.stop @wheel.prevent=\"handleWheel\">\n            <img :src=\"previewImageUrl\" alt=\"预览图片\" class=\"preview-image\" :style=\"{\n                width: `${baseWidth * scale}px`,\n                height: `${baseHeight * scale}px`,\n                minWidth: '100px',\n                minHeight: '100px'\n            }\" @load=\"handleImageLoad\">\n        </div>\n    </div>\n\n    <!-- 文件操作弹窗 -->\n    <el-dialog v-model=\"fileDialogVisible\" title=\"文件操作\" width=\"30%\" :show-close=\"true\" :close-on-click-modal=\"true\"\n        :close-on-press-escape=\"true\" class=\"file-dialog\">\n        <div class=\"file-dialog-content\">\n            <div class=\"file-info\">\n                <img :src=\"getFileIcon(currentFile?.fileext)\" alt=\"文件类型\" class=\"file-icon\">\n                <div class=\"file-details\">\n                    <p class=\"file-name\">{{ currentFile?.filename }}</p>\n                    <p class=\"file-size\">{{ formatFileSize(currentFile?.filesize) }}</p>\n                </div>\n            </div>\n            <el-alert title=\"当前文件预览只支持docx、xlsx、pdf格式文件\" type=\"info\" :closable=\"false\" show-icon\n                style=\"margin-bottom: 20px;\" />\n            <div class=\"file-actions\">\n                <el-button type=\"primary\" @click=\"previewFile\"\n                    :disabled=\"!['docx', 'xlsx', 'pdf'].includes(currentFile?.fileext?.toLowerCase())\">\n                    预览\n                </el-button>\n                <el-button type=\"success\" @click=\"downloadFile\">下载</el-button>\n            </div>\n        </div>\n    </el-dialog>\n\n    <!-- 文件预览弹窗 -->\n    <el-dialog v-model=\"previewDialogVisible\" :title=\"currentFile?.filename\" width=\"80%\" :show-close=\"true\"\n        :close-on-click-modal=\"false\" :close-on-press-escape=\"true\" class=\"preview-dialog\">\n        <div class=\"preview-container\">\n            <!-- docx文件预览 -->\n            <vue-office-docx v-if=\"currentFile?.fileext?.toLowerCase() === 'docx'\" :src=\"currentFileUrl\"\n                @rendered=\"handleDocxRendered\" @error=\"handlePreviewError\" />\n            <!-- excel文件预览 -->\n            <vue-office-excel v-if=\"currentFile?.fileext?.toLowerCase() === 'xlsx'\" :src=\"currentFileUrl\"\n                @rendered=\"handleExcelRendered\" @error=\"handlePreviewError\" />\n            <!-- pdf文件预览 -->\n            <vue-office-pdf v-if=\"currentFile?.fileext?.toLowerCase() === 'pdf'\" :src=\"currentFileUrl\"\n                @rendered=\"handlePdfRendered\" @error=\"handlePreviewError\" />\n        </div>\n    </el-dialog>\n</template>\n\n<script setup>\nimport { Refresh, RefreshLeft, Loading, VideoPlay, VideoPause } from '@element-plus/icons-vue'\nimport { ref, inject, watch, nextTick, onUnmounted, computed } from 'vue'\n// , Clock, Location, User, CollectionTag, Microphone\nimport { Search, Filter, Document, Picture, ChatDotRound } from '@element-plus/icons-vue'\nimport ChatRecordPopUp from './ChatRecordPopUp.vue'\nimport axiosInstance from '@/axiosConfig.js'; // 引入 axios 实例\nimport { ElMessage } from 'element-plus';\n\n\n//文件在线预览方案\nimport VueOfficeDocx from '@vue-office/docx';\nimport VueOfficeExcel from '@vue-office/excel';\nimport VueOfficePdf from '@vue-office/pdf';\n\nimport BenzAMRRecorder from 'benz-amr-recorder';\n\n\n// 搜索关键词\nconst searchName = ref('')\n\n//----------------------------------------- chat-board显示以及会话详情数据获取 ----------------------------------------\n\n\n// 获取会话详情接口的参数信息,调用接口获取会话详情数据\nconst selectedChat = inject('selectedChat', ref(null))\n\nconst chatDetailMessages = ref([]) //存储详情会话数组\n\nconst chatDetailMessagesTotal = ref(null)//记录获取到的详情会话总数\n\nconst fetchedPages = ref(new Set());//记录已经获取过的页码\n\nconst mediaLoadingCount = ref(0); // 添加媒体加载计数器\nconst totalMediaCount = ref(0);   // 添加媒体总数计数器\n\n// 调用获取会话详情接口\nconst getDetailChatMessages = (from, to, type, searchName, page, limit) => {\n\n    console.log(\"调用获取会话详情接口\")\n\n\n    if (fetchedPages.value.has(page)) {\n        return;//如果该页数据已经获取过，直接返回\n    }\n\n    const jwt_token = localStorage.getItem('access_token')\n    // 构建请求参数\n    const params = {\n        from: from,\n        to: to,\n        type: type || '',  // 确保type不为undefined\n        searchName: searchName,\n        page: page,\n        limit: limit\n    }\n    // 只有当searchName有值且不为空字符串时才添加到参数中\n    // if (searchName && searchName.trim() !== '') {\n    //     params.searchName = searchName.trim()\n    // }\n    axiosInstance.post('/api/chatmessage/detail', params, {\n        headers: { Authorization: 'Bearer ' + jwt_token }\n    }).then(res => {\n        const newMessages = res.data.data.data\n\n        // 重置媒体计数器\n        mediaLoadingCount.value = 0;\n        totalMediaCount.value = 0;\n\n        // 计算需要加载的媒体总数\n        newMessages.forEach(message => {\n            if (message.msgType === 'image' ||\n                message.msgType === 'emotion' ||\n                message.msgType === 'voice' ||\n                message.msgType === 'video' ||\n                message.msgType === 'file') {\n                totalMediaCount.value++;\n            }\n        });\n\n        // 检查chatDetailMessages.value是否为空数组\n        if (chatDetailMessages.value.length === 0) {\n            chatDetailMessages.value = newMessages.sort((a, b) => {\n                return a.msgTime - b.msgTime;\n            });\n            // 只有在没有媒体需要加载时才立即滚动\n            if (totalMediaCount.value === 0) {\n                scrollToBottom();\n            }\n        } else {\n            chatDetailMessages.value = [...chatDetailMessages.value, ...newMessages].sort((a, b) => {\n                return a.msgTime - b.msgTime;\n            });\n        }\n\n        chatDetailMessagesTotal.value = newMessages.length\n\n        //记录已经获取过的页码\n        fetchedPages.value.add(page);\n\n    }).catch(error => {\n        console.log(error);\n        ElMessage.error('获取会话详情失败，请检查网络或联系管理员');\n    }).finally(\n    );\n}\n\n// ------------------------------------------------------------------------------------------- Utils\n// ----------------------------------------------------- 显示相关\n//控制chat-board的显示\nconst chatBoardVisible = ref(false)\n\nconst old_selectedChat = ref(null)\n\nwatch(selectedChat, (newValue) => {\n    console.log('DetailChatBoard检测到selectedChat存在变化', newValue);\n\n    if (newValue) {\n        currentPage.value = 1\n        chatBoardVisible.value = true\n\n        chatDetailMessages.value = [] //清空消息数组\n        fetchedPages.value = new Set() //清空已获取页码的记录\n        searchName.value = ''\n        hasMore.value = true\n        old_selectedChat.value = newValue\n\n        getDetailChatMessages(newValue.from, newValue.to, newValue.type, searchName.value, newValue.page, newValue.limit)\n    }\n    else {\n        chatBoardVisible.value = false\n        // console.log('selectedChat为空');\n    }\n\n})\n\n// 监听搜索关键词变化\nwatch(searchName, (newValue) => {\n    if ((selectedChat.value && newValue != \"\" && newValue.length != 0 && newValue != null) || (old_selectedChat.value == selectedChat.value)) {\n        currentPage.value = 1\n        chatDetailMessages.value = [] //清空消息数组\n        fetchedPages.value = new Set() //清空已获取页码的记录\n        hasMore.value = true\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, newValue, selectedChat.value.page, selectedChat.value.limit)\n    }\n})\n\n// 控制使用sent还是receive样式\nconst sender_or_reciever = (chatMessage) => {\n    //当为群聊时，selectedChat.value.from=群聊id，selectedChat.value.to=''\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return 'sent'\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return 'received'\n    }\n    return 'received' //群聊默认\n}\n\n//控制使用员工头像还是用户头像\nconst avatar_from_or_to = (chatMessage) => {\n\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return selectedChat.value.fromAvatar\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return selectedChat.value.toAvatar\n    }\n    return require('@/assets/人员头像.png') //目前客户会话功能暂时无法拉取客户头像，所以会话详情面板界面展示的头像均使用静态图\n}\n\n//控制人员标签颜色 \nconst getLableClass = (chatMessage) => {\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return 'employee'\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        if (selectedChat.value.toType == 1) {\n            return 'wechat'\n        } else {\n            return 'other'\n        }\n    } else {\n        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {\n            const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type\n            switch (type) {\n                case 1:\n                    return 'wechat';\n                case 2:\n                    return 'other';\n                default:\n                    return 'other'\n            }\n        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {\n            return 'employee';\n        }\n        return 'other'\n    }\n\n\n}\n\n//控制人员标签显示内容\nconst getLable = (chatMessage) => {\n    if (chatMessage.fromUser == selectedChat.value.from) {\n        return selectedChat.value.fromLable\n    } else if (chatMessage.fromUser == selectedChat.value.to) {\n        return selectedChat.value.toLable\n    } else {\n        if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.externalUserId) {\n            const type = sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.type\n            switch (type) {\n                case 1:\n                    return '@微信';\n                case 2:\n                    return sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName ? `@${sender_nameAndLable_cache.value[chatMessage.fromUser].from_user.corpName}` : '@未知企业';\n                default:\n                    return ''\n            }\n        } else if (sender_nameAndLable_cache.value[chatMessage.fromUser]?.from_user.userid) {\n            return '@员工';\n        }\n        return ''\n    }\n\n}\n\n//处理显示的发送方、接收方名称\n// const handle_sender_reciever_name = (fromName) => {\n//     if (fromName == selectedChat.value.from) {\n//         return selectedChat.value.fromName\n//     } else if (fromName == selectedChat.value.to) {\n//         return selectedChat.value.toName\n//     } else {\n\n//         // 先检查缓存中是否已有该用户的名称\n//         if (sender_nameAndLable.value[fromName]) {\n//             return sender_nameAndLable.value[fromName]?.from_user.name || '加载中...';\n//         }\n//         // 如果缓存中没有且不在请求中，则调用接口获取\n//         if (!pending_requests.value.has(fromName)) {\n//             get_sender_nameAndLable(fromName);\n//         }\n//         return '加载中...';\n//     }\n// }\n\n//使用计算属性优化handle_sender_reciever_name方法反复调用\nconst preprocessedNames = computed(() => {\n    // console.log('处理显示的发送方、接收方名称，chatmessages数组的长度为：', chatDetailMessages.value.length)\n    const namesMap = new Map();\n    chatDetailMessages.value.forEach(message => {\n        const fromName = message.fromUser\n        if (namesMap.has(fromName)) return;\n        if (fromName == selectedChat.value.from) {\n            namesMap.set(fromName, selectedChat.value.fromName)\n        } else if (fromName == selectedChat.value.to) {\n            namesMap.set(fromName, selectedChat.value.toName)\n        } else {\n            namesMap.set(fromName,\n                sender_nameAndLable_cache.value[fromName]?.from_user.name || (checkAndFetchName(fromName) ? '加载中...' : '未知用户')\n            )\n        }\n    });\n    return namesMap;\n})\n\n//提取判断逻辑\nconst checkAndFetchName = (fromUser) => {\n    if (!sender_nameAndLable.value[fromUser] && !pending_requests.value.has(fromUser)) {\n        get_sender_nameAndLable(fromUser);\n        return true;\n    }\n    return false;\n}\n\n\n// 键值存储已获取的人员的 id, 避免接口冗余调用\nconst sender_nameAndLable = ref({});\nconst sender_nameAndLable_cache = ref({});\n// 记录正在请求中的用户ID\nconst pending_requests = ref(new Set());\n\n//一般情况下只有选中群聊会话才会调用此方法\nconst get_sender_nameAndLable = (from_userId) => {\n    // 如果已经在获取中或已有缓存，则不重复获取\n    if (pending_requests.value.has(from_userId) || sender_nameAndLable_cache.value[from_userId]) {\n        return;\n    }\n\n\n    // 添加到正在请求的集合中\n    pending_requests.value.add(from_userId);\n\n    // console.log('调用真实名称、标签获取接口')\n    const jwt_token = localStorage.getItem('access_token');\n    axiosInstance.get('/api/chatmessage/detail/getNameAndLable', {\n        params: { from_userId },\n        headers: { Authorization: 'Bearer ' + jwt_token }\n    }).then(res => {\n        if (res.data.code === 0) {\n            const data = res.data.data;\n            sender_nameAndLable.value[from_userId] = data;\n            sender_nameAndLable_cache.value[from_userId] = data;\n        }\n    }).catch(error => {\n        console.error('获取真实人员信息失败:', error);\n        sender_nameAndLable.value[from_userId] = { from_user: { name: '未知用户' } };\n        sender_nameAndLable_cache.value[from_userId] = { from_user: { name: '未知用户' } };\n    }).finally(\n        // 请求完成后从集合中移除\n        pending_requests.value.delete(from_userId)\n\n    );\n\n}\n\n//将时间戳转换为时间格式\nconst convertTime = (timestamp) => {\n    if (!timestamp) return '';\n\n    // 判断时间戳是否为13位，如果是10位则转换为13位\n    const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;\n\n    const date = new Date(ts);\n\n    // 获取年月日时分秒\n    const year = date.getFullYear();\n    const month = (date.getMonth() + 1).toString().padStart(2, '0');\n    const day = date.getDate().toString().padStart(2, '0');\n    const hours = date.getHours().toString().padStart(2, '0');\n    const minutes = date.getMinutes().toString().padStart(2, '0');\n    const seconds = date.getSeconds().toString().padStart(2, '0');\n\n    // 拼接成指定格式\n    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\n}\n\n//-----------------------------------------------------------------------------------------------------文件消息\nconst fileUrls = ref([''])\nconst fileUrls_cache = ref([''])\nconst currentFile = ref(null)\nconst currentFileUrl = ref('')\n\n\n// 添加文件操作相关的状态变量\nconst fileDialogVisible = ref(false)\n\n// 根据文件扩展名获取对应的图标\nconst getFileIcon = (fileext) => {\n    const iconMap = {\n        // 文档类\n        'doc': require('@/assets/文件类型图片/docx.png'),\n        'docx': require('@/assets/文件类型图片/docx.png'),\n        'pdf': require('@/assets/文件类型图片/pdf.png'),\n        'txt': require('@/assets/文件类型图片/txt.png'),\n        // 表格类\n        'xls': require('@/assets/文件类型图片/xlsx.png'),\n        'xlsx': require('@/assets/文件类型图片/xlsx.png'),\n        // 演示类\n        'ppt': require('@/assets/文件类型图片/ppt.png'),\n        'pptx': require('@/assets/文件类型图片/ppt.png'),\n        // 压缩包类\n        'zip': require('@/assets/文件类型图片/zip.png'),\n        'rar': require('@/assets/文件类型图片/zip.png'),\n        '7z': require('@/assets/文件类型图片/zip.png'),\n        // 图片类\n        'jpg': require('@/assets/文件类型图片/jpg.png'),\n        'jpeg': require('@/assets/文件类型图片/jpg.png'),\n        'png': require('@/assets/文件类型图片/jpg.png'),\n        'gif': require('@/assets/文件类型图片/jpg.png'),\n        // 视频类\n        'mp4': require('@/assets/文件类型图片/mp4.png'),\n        'avi': require('@/assets/文件类型图片/mp4.png'),\n        'mov': require('@/assets/文件类型图片/mp4.png'),\n        // 音频类\n        'mp3': require('@/assets/文件类型图片/mp3.png'),\n        'wav': require('@/assets/文件类型图片/mp3.png'),\n    }\n\n\n    // 返回对应的图标，如果没有匹配则返回默认图标\n    return iconMap[fileext] || require('@/assets/文件类型图片/default.png')\n}\n\n// 格式化文件大小\nconst formatFileSize = (size) => {\n    if (size < 1024) {\n        return size + 'B'\n    } else if (size < 1024 * 1024) {\n        return (size / 1024).toFixed(2) + 'KB'\n    } else if (size < 1024 * 1024 * 1024) {\n        return (size / (1024 * 1024)).toFixed(2) + 'MB'\n    } else {\n        return (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB'\n    }\n}\n\nconst getFileContent = (message) => {\n    // console.log('点击',message.sdkfileid)\n\n\n    const sdkfileid = message.sdkfileid\n    // console.log('sdkfileid:', sdkfileid)\n    const url = fileUrls.value[sdkfileid]\n    if (url) {\n        currentFileUrl.value = url\n        currentFile.value = message\n        fileDialogVisible.value = true\n    }\n}\n\nconst previewDialogVisible = ref(false)\n\n// 修改预览文件方法\nconst previewFile = () => {\n    if (currentFileUrl.value && currentFile.value) {\n        const fileExt = currentFile.value.fileext?.toLowerCase()\n        if (['docx', 'xlsx', 'pdf'].includes(fileExt)) {\n            previewDialogVisible.value = true\n            fileDialogVisible.value = false\n        }\n    }\n}\n\n// 添加预览组件的回调方法\nconst handleDocxRendered = () => {\n    console.log('docx渲染完成')\n}\n\nconst handleExcelRendered = () => {\n    console.log('excel渲染完成')\n}\n\nconst handlePdfRendered = () => {\n    console.log('pdf渲染完成')\n}\n\nconst handlePreviewError = (error) => {\n    console.error('预览出错:', error)\n    ElMessage.error('文件预览失败，请尝试下载后查看')\n    previewDialogVisible.value = false\n}\n\n// 下载文件\nconst downloadFile = () => {\n    if (currentFileUrl.value && currentFile.value) {\n        const link = document.createElement('a')\n        link.href = currentFileUrl.value\n        link.download = currentFile.value.filename\n        document.body.appendChild(link)\n        link.click()\n        document.body.removeChild(link)\n    }\n    fileDialogVisible.value = false\n}\n\n\n// ----------------------------------------------------------------------------------------语音消息\n//定义一个变量用于存储语音\nconst voiceUrls = ref([''])\nconst voiceUrls_cache = ref([''])\n\n// vue-audio-visual 相关状态管理\nconst audioRefs = ref({}) // 存储每个语音消息的 audio 元素引用\nconst voiceStates = ref({}) // 存储每个语音消息的播放状态\nconst currentPlayingVoice = ref(null) // 当前正在播放的语音消息ID\n\n// 初始化语音播放状态\nconst initVoiceState = (msgid) => {\n    if (!voiceStates.value[msgid]) {\n        voiceStates.value[msgid] = {\n            isPlaying: false,\n            loading: false,\n            duration: 0,\n            currentTime: 0\n        }\n    }\n}\n\n// 获取语音播放按钮图标\nconst getVoicePlayIcon = (msgid) => {\n    const state = voiceStates.value[msgid]\n    if (state?.loading) return Loading\n    return state?.isPlaying ? VideoPause : VideoPlay\n}\n\n// 格式化语音时长显示\nconst formatVoiceDuration = (voiceSize) => {\n    if (!voiceSize) return '0\"'\n    // 假设 voice_size 是以毫秒为单位，转换为秒\n    const seconds = Math.ceil(voiceSize / 1000)\n    return `${seconds}\"`\n}\n\n\n\n// 设置audio元素引用\nconst setAudioRef = (msgid, el) => {\n    if (el) {\n        audioRefs.value[msgid] = el\n    }\n}\n\n// 获取消息的sdkfileid（用于模板中）\nconst get_sdkfileid_from_message = (message) => {\n    return get_sdkfileid(message)\n}\n\n\n\n// 处理音频播放事件\nconst handleAudioPlay = (msgid) => {\n    voiceStates.value[msgid].isPlaying = true\n    currentPlayingVoice.value = msgid\n    console.log('音频开始播放:', msgid)\n}\n\n// 处理音频暂停事件\nconst handleAudioPause = (msgid) => {\n    voiceStates.value[msgid].isPlaying = false\n    if (currentPlayingVoice.value === msgid) {\n        currentPlayingVoice.value = null\n    }\n    console.log('音频暂停:', msgid)\n}\n\n// 处理音频结束事件\nconst handleAudioEnded = (msgid) => {\n    voiceStates.value[msgid].isPlaying = false\n    currentPlayingVoice.value = null\n    voiceStates.value[msgid].currentTime = 0\n    console.log('音频播放结束:', msgid)\n}\n\n// 处理音频错误事件\nconst handleAudioError = (msgid) => {\n    console.error('音频播放错误:', msgid)\n    voiceStates.value[msgid].loading = false\n    voiceStates.value[msgid].isPlaying = false\n    ElMessage.error('语音播放失败')\n}\n\n// 处理音频加载完成事件\nconst handleAudioLoaded = (msgid) => {\n    const audio = audioRefs.value[msgid]\n    if (audio) {\n        voiceStates.value[msgid].loading = false\n        voiceStates.value[msgid].duration = audio.duration\n        console.log('音频加载完成:', msgid, '时长:', audio.duration)\n    }\n}\n\n// 切换语音播放状态\nconst toggleVoicePlay = async (msgid, voiceContent) => {\n    try {\n        initVoiceState(msgid)\n\n        // 暂停其他正在播放的语音\n        if (currentPlayingVoice.value && currentPlayingVoice.value !== msgid) {\n            const currentAudio = audioRefs.value[currentPlayingVoice.value]\n            if (currentAudio && !currentAudio.paused) {\n                currentAudio.pause()\n            }\n        }\n\n        const audio = audioRefs.value[msgid]\n\n        if (!audio) {\n            console.error('音频元素未找到:', msgid)\n            return\n        }\n\n        // 检查音频源是否存在\n        if (!audio.src) {\n            const sdkfileid = voiceContent?.sdkfileid\n            if (!sdkfileid) {\n                ElMessage.error('语音文件ID不存在')\n                return\n            }\n\n            const audioUrl = voiceUrls.value[sdkfileid]\n            if (!audioUrl) {\n                ElMessage.error('语音文件未加载')\n                return\n            }\n\n            audio.src = audioUrl\n        }\n\n        // 切换播放状态\n        if (voiceStates.value[msgid].isPlaying) {\n            audio.pause()\n        } else {\n            audio.play()\n        }\n    } catch (error) {\n        console.error('语音播放切换失败:', error)\n        voiceStates.value[msgid].loading = false\n        ElMessage.error('语音播放失败')\n    }\n}\n\n\n\n\n\n\n\n\n// ----------------------------------------------------------------------------------------图片消息\n//定义一个变量用于存储图片url\nconst imageUrls = ref([''])\nconst imageUrls_cache = ref([''])\n\nconst getImageUrl = (message) => {\n    const sdkfileid = get_sdkfileid(message)\n    return imageUrls.value[sdkfileid]\n\n}\n\n// ----------------------------------------------------------------------------------------视频消息\nconst videoUrls = ref([''])\nconst videoUrls_cache = ref([''])\n\n// 添加视频相关的状态管理\nconst videoRefs = ref({});  // 存储视频元素引用\nconst videoStates = ref({}); // 存储视频状态\n\n// 设置视频引用\nconst setVideoRef = (msgid, el) => {\n    if (el) {\n        videoRefs.value[msgid] = el;\n    }\n};\n\n// 处理视频播放\nconst handleVideoPlay = (message) => {\n    try {\n        const currentVideo = videoRefs.value[message.msgid];\n        if (!currentVideo) return;\n\n        // 暂停其他正在播放的视频\n        Object.entries(videoRefs.value).forEach(([msgid, video]) => {\n            if (msgid !== message.msgid && !video.paused) {\n                video.pause();\n            }\n        });\n\n        // 更新当前视频状态\n        videoStates.value[message.msgid] = 'playing';\n        console.log('视频开始播放:', message.msgid);\n    } catch (error) {\n        console.error('视频播放处理失败:', error);\n    }\n};\n\n// 处理视频暂停\nconst handleVideoPause = (message) => {\n    try {\n        videoStates.value[message.msgid] = 'paused';\n        console.log('视频暂停:', message.msgid);\n    } catch (error) {\n        console.error('视频暂停处理失败:', error);\n    }\n};\n\n// 处理视频播放结束\nconst handleVideoEnded = (message) => {\n    try {\n        videoStates.value[message.msgid] = 'ended';\n        console.log('视频播放结束:', message.msgid);\n    } catch (error) {\n        console.error('视频结束处理失败:', error);\n    }\n};\n\n// 处理视频错误\nconst handleVideoError = (message) => {\n    try {\n        const video = videoRefs.value[message?.msgid];\n        if (!video) return;\n\n        console.error('视频加载失败:', video.error);\n        videoStates.value[message?.msgid] = 'error';\n\n        // 尝试重新加载视频\n        const sdkfileid = get_sdkfileid(message);\n        if (videoUrls_cache.value[sdkfileid]) {\n            console.log('尝试从缓存重新加载视频');\n            video.src = videoUrls_cache.value[sdkfileid];\n            video.load();\n        }\n\n        // ElMessage.error('视频加载失败，请重试');\n    } catch (error) {\n        console.error('视频错误处理失败:', error);\n    }\n};\n\n// 获取视频URL的方法\nconst getVideoUrl = (message) => {\n    console.log('获取视频url', message)\n    try {\n        const sdkfileid = get_sdkfileid(message);\n        const url = videoUrls.value[sdkfileid];\n\n        // 如果URL不存在但有缓存，使用缓存\n        if (!url && videoUrls_cache.value[sdkfileid]) {\n            return videoUrls_cache.value[sdkfileid];\n        }\n\n        return url || '';\n    } catch (error) {\n        console.error('获取视频URL失败:', error);\n        return '';\n    }\n};\n\n// 在组件卸载时清理资源\nonUnmounted(() => {\n    // 清理视频URL\n    Object.values(videoUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n            URL.revokeObjectURL(url);\n        }\n    });\n\n    // 清理视频状态\n    videoStates.value = {};\n    videoRefs.value = {};\n\n    // 清理音频引用和状态\n    audioRefs.value = {};\n    voiceStates.value = {};\n    currentPlayingVoice.value = null;\n\n    // 清理语音URL\n    Object.values(voiceUrls.value).forEach(url => {\n        if (url && url.startsWith('blob:')) {\n            URL.revokeObjectURL(url);\n        }\n    });\n});\n\n// ----------------------------------------------------------------------------------------名片消息\nconst getCardUserId = (id) => {\n    if (id && id.length > 20) {\n        return id.slice(0, 20) + '...'\n    }\n    return id\n}\n\n// ----------------------------------------------------------------------------------------链接消息\n// 处理链接点击事件\nconst handleLinkClick = (link_url) => {\n    if (link_url) {\n        window.open(link_url, '_blank');\n    }\n};\n\n// 截断URL显示\nconst truncateUrl = (url) => {\n    if (!url) return '';\n    const baseUrl = new URL(url);\n    let displayUrl = baseUrl.host;\n    const path = baseUrl.pathname;\n\n    if (path.length > 10) {\n        displayUrl += path.substring(0, 10) + '...';\n    } else {\n        displayUrl += path;\n    }\n\n    return displayUrl;\n};\n\n\n\n// ----------------------------------------------------- 功能相关\n//刷新按钮\n// 添加刷新状态控制\nconst isRefreshing = ref(false)\n\nconst overlayTop = ref(0) // 遮罩层距离顶部的距离\n\n// 计算超出可视区域的高度（scrollHeight - clientHeight）\nconst calculateOverlayTop = () => {\n    if (!chatContentRef.value) return;\n    if (chatContentRef.value) {\n        const scrollHeight = chatContentRef.value.scrollHeight;\n        const clientHeight = chatContentRef.value.clientHeight;\n        overlayTop.value = scrollHeight - clientHeight;\n    }\n}\n\nconst refreshChat = async () => {\n    if (isRefreshing.value) return; // 防止重复点击\n    calculateOverlayTop();\n\n    try {\n        isRefreshing.value = true;\n        const jwt_token = localStorage.getItem('access_token');\n        currentPage.value = 1\n        searchName.value = ''\n\n        // 调用刷新接口\n        const response = await axiosInstance.post('/api/wechat/chatdata/download', {\n            seq: 0,\n            limit: 200,\n            proxy: \"\",\n            password: \"\",\n            timeout: 30,\n            type: \"AUTO_MODE\"\n        }, {\n            headers: { Authorization: 'Bearer ' + jwt_token }\n        });\n\n\n        if (response.data.code !== 0) {\n            ElMessage.error(response.data.msg || '刷新失败');\n            return;\n        }\n\n        ElMessage.success('刷新成功');\n\n    } catch (error) {\n        console.error('刷新失败:', error);\n        ElMessage.error('刷新失败，请检查网络或联系管理员');\n    } finally {\n        searchName.value = ''\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, '', selectedChat.value.page, selectedChat.value.limit)\n        // 请求后等待5秒\n        await new Promise(resolve => setTimeout(resolve, 2000));\n        isRefreshing.value = false;\n    }\n}\n\n// ---------------------------------------------------------------------------------------------------- 消息处理\n//消息位置：如果消息类型是同意会话类型，显示在中间；\n// 如果是其他消息类型，需判断发送/接收方，发送方显示在右边，反之\nconst handleMessageType = (message) => {\n    if (message.msgtype === 'agree' || message.msgtype === 'disagree') {\n        return message.msgtype\n    }\n    else {\n        return sender_or_reciever(message)\n    }\n}\n\n\n//处理消息内容类型，若为撤回消息，可以通过此方法读取pre_msgid，从而进一步获取被撤回的消息，调用相应的渲染方法\nconst handleMessageContentType = (message) => {\n    if (message.msgType == 'revoke') {\n        // 获取chatMsg中的pre_msgid\n        if (!message.chatMsg) {\n            console.log('检查 message.chatMsg 是否存在: 该字段不存在');\n            return \" \";\n        }\n        const ChatMsg = JSON.parse(message.chatMsg);\n        if (!ChatMsg || !ChatMsg.msgtype) {\n            console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');\n            return \" \";\n        }\n\n        // 如果缓存中已有该消息，直接返回\n        if (recall_msg_cache.value[ChatMsg.msgid]) {\n            return recall_msg_cache.value[ChatMsg.msgid].msgType;\n        }\n\n        return \"text\"; // 默认返回text类型\n    } else if (message.msgType === 'emotion') {\n        return 'image'\n    } else if (message.msgType === 'video') {\n        return 'video'\n    }\n    else {\n        return message.msgType\n    }\n}\n\n//处理消息内容，若为撤回消息，可以通过此方法读取premise_id，从而获取被撤回的消息内容类型，调用相应的渲染方法\nconst messageContent = ref([])\nconst handleMessageContent = (message) => {\n    const msgid = message.msgid\n    try {\n        //判断message.msgType是不是revoke,如果是的话message替换成从recall_msg中message.msgid对应的值\n        if (message.msgType === 'revoke' && recall_msg.value[msgid]) {\n            console.log('替换撤回消息内容');\n            message = recall_msg.value[msgid];\n        }\n\n        // 检查 message 对象中 chatMsg 字段是否存在\n        if (!message.chatMsg) {\n            console.log('检查 message.chatMsg 是否存在: 该字段不存在');\n            return;\n        }\n\n        // 若 chatMsg 字段存在，尝试将其从字符串解析为 JSON 对象\n        const ChatMsg = JSON.parse(message.chatMsg);\n\n        // console.log('解析后的 ChatMsg 对象:', ChatMsg);\n\n        // 检查解析后的 ChatMsg 对象是否有效，以及其内部的 msgtype 字段是否存在\n        if (!ChatMsg || !ChatMsg.msgtype) {\n            console.log('解析后的 ChatMsg 对象无效或 msgtype 字段不存在');\n            return;\n        }\n\n        //根据不同消息类型，返回消息信息\n        switch (ChatMsg.msgtype) {\n            case 'text':\n                // return ChatMsg.text || \" \";\n                messageContent.value[msgid] = ChatMsg.text;\n                // console.log('text100:',messageContent[message.msgid].content)\n                break;\n            case 'file':\n                // return ChatMsg.file || \" \";\n                messageContent.value[msgid] = ChatMsg.file;\n                break;\n            case 'image':\n            case 'emotion':\n                break;\n            case 'agree':\n                // return ChatMsg.agree || \" \";\n                messageContent.value[msgid] = ChatMsg.agree;\n                break;\n            case 'disagree':\n                // return ChatMsg.disagree || \" \";\n                messageContent.value[msgid] = ChatMsg.disagree;\n                break;\n            case 'video':\n                // return ChatMsg.video || \" \";\n                messageContent.value[msgid] = ChatMsg.video;\n                console.log('解析后的 ChatMsg 对象:', ChatMsg.video);\n                break;\n            case 'voice':\n                // return ChatMsg.voice || \" \";\n                messageContent.value[msgid] = ChatMsg.voice;\n                console.log('解析后的 ChatMsg 对象:', ChatMsg.voice);\n                break;\n            case 'card':\n                // return ChatMsg.card || \" \";\n                messageContent.value[msgid] = ChatMsg.card;\n                break;\n            case 'link':\n                // return ChatMsg.link || \" \";\n                messageContent.value[msgid] = ChatMsg.link;\n                break;\n            default:\n                break;\n        }\n    } catch (error) {\n        console.error('解析 chatMsg 时出错:', error);\n        return;\n    }\n}\n\n// --------------------------------------------------------- 撤回消息相关处理\n\n//定义一个变量用于存储撤回消息\nconst recall_msg = ref({})  // 修改为对象类型，使用 pre_msgid 作为 key\nconst recall_msg_cache = ref({}) // 新增缓存对象\n\n// 获取撤回消息的方法\nconst fetchRecallMessage = (message) => {\n    // console.log('调用获取撤回消息方法')\n    if (!message.chatMsg) return;\n\n    const ChatMsg = JSON.parse(message.chatMsg);\n    if (!ChatMsg || !ChatMsg.msgtype || !ChatMsg.revoke) return;\n\n    const pre_msgid = ChatMsg.revoke.pre_msgid;//撤回原消息的id\n    const msgid = ChatMsg.msgid;//撤回消息的id\n    // console.log('撤回消息的id', msgid)\n\n    // 如果缓存中已有该消息，直接返回\n    if (recall_msg_cache.value[msgid]) {\n        recall_msg.value[msgid] = recall_msg_cache.value[msgid];\n        return;\n    }\n\n    const jwt_token = localStorage.getItem('access_token');\n    axiosInstance.get('/api/chatmessage/revoke_premsg', {\n        params: { pre_msgid },\n        headers: { Authorization: 'Bearer ' + jwt_token }\n    }).then(res => {\n        // console.log('获取到撤回原消息：', res.data.data)\n        if (res.data.code === 0) {\n\n            recall_msg_cache.value[msgid] = res.data.data;\n            recall_msg.value[msgid] = res.data.data;\n            console.log('写入撤回消息数组,写入msgid：', msgid)\n            handleMessageContent(message)\n        } else {\n            ElMessage.error(res.data.msg || '获取撤回消息失败');\n        }\n    }).catch(error => {\n        console.log(error)\n    }).finally(\n    );\n\n}\n\n\n// --------------------------------------------------------- 图片，表情、语音、视频消息相关处理\n\n//获取媒体消息的sdkfileid\nconst get_sdkfileid = (message) => {\n    if (!message?.chatMsg) return;\n\n    const ChatMsg = JSON.parse(message.chatMsg);\n    // console.log(\"点击2\", ChatMsg)\n    if (!ChatMsg || !ChatMsg.msgtype) return;\n\n    let sdkfileid = '';\n\n    // 检查消息类型和对应的属性是否存在\n    if (ChatMsg.msgtype === 'image' && ChatMsg.image && ChatMsg.image.sdkfileid) {\n        sdkfileid = ChatMsg.image.sdkfileid;\n    } else if (ChatMsg.msgtype === 'emotion' && ChatMsg.emotion && ChatMsg.emotion.sdkfileid) {\n        // console.log('trigger mind');\n        sdkfileid = ChatMsg.emotion.sdkfileid;\n    } else if (ChatMsg.msgtype === 'voice') {\n        sdkfileid = ChatMsg.voice.sdkfileid;\n    } else if (ChatMsg.msgtype === 'video') {\n        sdkfileid = ChatMsg.video.sdkfileid;\n    } else if (ChatMsg.msgtype === 'file') {\n        sdkfileid = ChatMsg.file.sdkfileid;\n    } else {\n        // console.log('无法获取有效的 sdkfileid');\n        return \"\";\n    }\n\n    return sdkfileid\n}\n\n\n// 获取媒体消息的方法\nconst fetchmediaMessage = async (message) => {\n    const sdkfileid = get_sdkfileid(message)\n\n    // 检查缓存\n    if (message.msgType === 'image' || message.msgType === 'emotion') {\n        if (imageUrls_cache.value[sdkfileid]) {\n            imageUrls.value[sdkfileid] = imageUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    } else if (message.msgType === 'voice') {\n        if (voiceUrls_cache.value[sdkfileid]) {\n            voiceUrls.value[sdkfileid] = voiceUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    } else if (message.msgType === 'video') {\n        if (videoUrls_cache.value[sdkfileid]) {\n            videoUrls.value[sdkfileid] = videoUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    } else if (message.msgType === 'file') {\n        if (fileUrls_cache.value[sdkfileid]) {\n            fileUrls.value[sdkfileid] = fileUrls_cache.value[sdkfileid];\n            mediaLoadingCount.value++;\n            checkAllMediaLoaded();\n            return;\n        }\n    }\n\n    if (!message.chatMsg) return;\n\n    const ChatMsg = JSON.parse(message.chatMsg);\n    if (!ChatMsg || !ChatMsg.msgtype) {\n        return;\n    }\n\n    try {\n\n        const jwt_token = localStorage.getItem('access_token');\n        axiosInstance.post('/api/chatmessage/chatmedia/download', {\n            sdkfileid: sdkfileid\n        }, {\n            headers: { Authorization: 'Bearer ' + jwt_token },\n            responseType: 'arraybuffer'\n        }).then(async res => {\n            let blob = null;\n            let url = null;\n            if (ChatMsg.msgtype === 'image') {\n                blob = new Blob([res.data], { type: 'image/jpeg' });\n                url = URL.createObjectURL(blob);\n\n                // 将 URL 存储到 imageUrls 中\n                imageUrls.value[sdkfileid] = url;\n                imageUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到图片媒体')\n                handleMessageContent(message)\n\n                // console.log(\"附件下载接口调用成功：jpeg\", url);\n            } else if (ChatMsg.msgtype === 'emotion') {\n                // 动态图可能是 gif 格式\n                blob = new Blob([res.data], { type: 'image/gif' });\n                url = URL.createObjectURL(blob);\n\n                // 将 URL 存储到 imageUrls 中\n                imageUrls.value[sdkfileid] = url;\n                imageUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到表情媒体')\n                handleMessageContent(message)\n\n                // console.log('表情 URL已设置:', url)\n            } else if (ChatMsg.msgtype === 'voice') {\n                try {\n                    // 创建AMR格式的Blob\n                    const amrBlob = new Blob([res.data], { type: 'audio/amr' });\n\n                    // 使用BenzAMRRecorder转换AMR为可播放格式\n                    const amrRecorder = new BenzAMRRecorder();\n\n                    // 初始化AMR解码器\n                    await amrRecorder.initWithBlob(amrBlob);\n\n                    // 等待解码完成\n                    await new Promise((resolve, reject) => {\n                        amrRecorder.onEnded(() => {\n                            resolve();\n                        });\n\n                        amrRecorder.onPlay(() => {\n                            // 立即停止播放，我们只需要解码\n                            amrRecorder.stop();\n                        });\n\n                        // 开始播放以触发解码\n                        amrRecorder.play();\n\n                        // 设置超时\n                        setTimeout(() => {\n                            reject(new Error('AMR解码超时'));\n                        }, 5000);\n                    });\n\n                    // 获取转换后的音频Blob\n                    const convertedBlob = amrRecorder.getBlob();\n\n                    if (!convertedBlob) {\n                        throw new Error('无法获取转换后的音频数据');\n                    }\n\n                    // 创建可播放的URL\n                    url = URL.createObjectURL(convertedBlob);\n                    voiceUrls.value[sdkfileid] = url;\n                    voiceUrls_cache.value[sdkfileid] = url;\n\n                    console.log('获取到音频媒体并转换成功', url)\n                    handleMessageContent(message)\n                } catch (amrError) {\n                    console.error('AMR转换失败:', amrError);\n                    // 如果转换失败，尝试直接使用原始数据\n                    try {\n                        const fallbackBlob = new Blob([res.data], { type: 'audio/wav' });\n                        url = URL.createObjectURL(fallbackBlob);\n                        voiceUrls.value[sdkfileid] = url;\n                        voiceUrls_cache.value[sdkfileid] = url;\n                        console.log('使用备用方案创建音频URL');\n                        handleMessageContent(message);\n                    } catch (fallbackError) {\n                        console.error('备用方案也失败:', fallbackError);\n                        voiceUrls.value[sdkfileid] = null;\n                        voiceUrls_cache.value[sdkfileid] = null;\n                        ElMessage.error('语音格式转换失败');\n                    }\n                }\n\n            } else if (ChatMsg.msgtype === 'video') {\n                blob = new Blob([res.data], { type: 'video/mp4' });\n                url = URL.createObjectURL(blob);\n                videoUrls.value[sdkfileid] = url;\n                videoUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到视频媒体')\n                handleMessageContent(message)\n            } else if (ChatMsg.msgtype === 'file') {\n                blob = new Blob([res.data], { type: 'application/octet-stream' });\n                url = URL.createObjectURL(blob);\n                fileUrls.value[sdkfileid] = url;\n                fileUrls_cache.value[sdkfileid] = url;\n\n                console.log('获取到文件媒体')\n                handleMessageContent(message)\n            }\n\n            // 增加媒体加载计数\n            // mediaLoadingCount.value++;\n            // console.log('此处获取媒体消息')\n\n        }).catch(error => {\n            console.log(error);\n            // 即使加载失败也要计数\n\n            checkAllMediaLoaded();\n            ElMessage.error('附件下载失败，请检查网络或联系管理员');\n        });\n    } catch (error) {\n        console.error('处理媒体消息时出错:', error);\n        // 出错时也要计数\n        mediaLoadingCount.value++;\n        checkAllMediaLoaded();\n    } finally {\n        console.log('获取媒体消息finally')\n        checkAllMediaLoaded();\n        mediaLoadingCount.value++;\n    }\n}\n\n// 添加检查所有媒体是否加载完成的方法\nconst checkAllMediaLoaded = () => {\n    // console.log('检查所有媒体消息是否加载完成')\n    if (mediaLoadingCount.value === totalMediaCount.value && totalMediaCount.value > 0) {\n        // 所有媒体加载完成后，执行滚动\n        nextTick(() => {\n            scrollToBottom();\n        });\n    }\n}\n\nconst isProcessing = ref(false)\n// 监听消息列表变化，处理撤回消息,图片、表情消息\nwatch(chatDetailMessages, async (newMessages) => {\n    if (!newMessages) {\n        isProcessing.value = true;\n        return;\n    }\n\n    const promises = newMessages.map(async message => {\n        if (message.msgType === 'revoke') {\n            fetchRecallMessage(message);\n        } else if (message.msgType === 'image' || message.msgType === 'emotion' || message.msgType === 'voice' || message.msgType === 'video' || message.msgType === 'file') {\n            await fetchmediaMessage(message);\n        } else {\n            handleMessageContent(message);\n        }\n\n    });\n\n    try {\n        await Promise.all(promises);\n\n    } catch (error) {\n        console.log('处理消息失败', error);\n    } finally {\n        console.log('ttttttttttttttttttttttt')\n        scrollToBottom()\n        isProcessing.value = true;\n    }\n}, { immediate: true });\n\n\n\n\n//调用高德地图API返回一张静态定位图\n// const getMapImage = (message) => {\n//     const key = 'a924f785e2522273c9b4113602e77dd0'\n//     const image_width = 500\n//     const image_height = 260\n//     const longitude = message.longitude//经度\n//     const latitude = message.latitude//纬度\n//     const zoom = 15\n\n//     const url = `https://restapi.amap.com/v3/staticmap?location=${longitude},${latitude}&zoom=${zoom}&size=${image_width}*${image_height}&markers=mid,,A:${longitude},${latitude}&key=${key}`\n\n//     return url\n// }\n\n\n\n\n\n\n\n\n\n\n// 聊天记录弹窗相关\nconst chatRecordVisible = ref(false)\nconst currentChatRecord = ref(null)\n\n// const showChatRecord = (message) => {\n//     // 处理聊天记录数据，确保数据格式正确\n//     const formattedRecords = message.item.map(item => {\n//         // 根据不同的记录类型进行格式化\n//         let formattedItem = {\n//             timestamp: message.item.msgtime,  // 使用记录项的时间\n//         }\n\n//         // 根据不同的记录类型设置不同的消息类型和内容\n//         switch (item.type) {\n//             case 'ChatRecordText':\n//                 formattedItem.msgtype = 'text'\n//                 formattedItem.content = item.content\n//                 break\n//             case 'ChatRecordImage':\n//                 formattedItem.msgtype = 'image'\n//                 formattedItem.content = item.content\n//                 break\n//             case 'ChatRecordFile':\n//                 formattedItem.msgtype = 'file'\n//                 formattedItem.filename = item.content.filename\n//                 formattedItem.filesize = item.content.filesize\n//                 formattedItem.fileext = item.content.fileext\n//                 formattedItem.fileurl = item.content.fileurl\n//                 break\n//             // 可以根据需要添加其他类型的处理\n//         }\n\n//         return formattedItem\n//     })\n\n//     currentChatRecord.value = {\n//         title: message.title,\n//         item: formattedRecords\n//     }\n//     chatRecordVisible.value = true\n// }\n\n// 对于会话记录消息类型，展示时将一些非文本类型消息用对应的类型信息进行标识\n// const nonTextMessage_ToText = (item) => {\n//     // 若传入的 item 为空，直接返回空字符串\n//     if (!item) {\n//         return '';\n//     }\n//     // 定义消息类型到文本描述的映射\n//     const messageTypeMap = {\n//         'ChatRecordText': item.content,\n//         'ChatRecordImage': '[图片]',\n//         'ChatRecordFile': '[文件]',\n//         'ChatRecordVoice': '[语音]',\n//         'ChatRecordVideo': '[视频]',\n//         'ChatRecordLocation': '[位置]',\n//         'ChatRecordCard': '[名片]',\n//         'ChatRecordSharing': '[分享]',\n//         'ChatRecordSystem': '[系统消息]'\n//     };\n//     // 根据 item 的 type 属性从映射中获取对应的文本描述\n//     const text = messageTypeMap[item.type];\n//     // 如果映射中存在对应的文本描述，则返回该描述；否则返回 '未知消息'\n//     return text || '[未知消息]';\n// };\n\n//------------------------------------------------------------------------------------------动态调整会话详情面板的宽度\n\n// 从父组件HomePage.vue中获取FunctionBar的开启状态\nconst FunctionBar_isCollapse = inject('FunctionBar_isCollapse')\n// 获取当前展示的列表类型，如果是groupchat\nconst currentComponent_List = inject('currentComponent_List')\nconst chatBoardStyle = ref({\n    width: 'calc(100vw - 40.5rem)' // 初始宽度 (660px -> 41.25rem)\n})\n\n// 更新聊天面板宽度的计算函数\nconst updateChatBoardWidth = (totalWidth) => {\n    if (currentComponent_List.value == 'GroupList') {\n        totalWidth = totalWidth - 19\n    }\n    chatBoardStyle.value.width = `calc(100vw - ${totalWidth}rem)`\n    // console.log('当前计算的面板总宽度：', chatBoardStyle.value.width)\n}\n\n// 监听FunctionBar宽度变化\nwatch(() => FunctionBar_isCollapse.value, (newWidth) => {\n    let totalWidth = 40.5\n    if (!newWidth) {\n        // console.log('展开状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)\n        totalWidth = 46  // 750px -> 46.875rem\n        updateChatBoardWidth(totalWidth)\n    } else {\n        // console.log('折叠状态：', newWidth, '当前currentComponent_List：', currentComponent_List.value)\n        updateChatBoardWidth(totalWidth)\n\n    }\n}, { immediate: true })\n\n// 监听FunctionBar宽度变化\nwatch(() => currentComponent_List.value, () => {\n    let totalWidth = 40.5\n    if (!FunctionBar_isCollapse.value) {\n        // console.log('展开状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)\n        totalWidth = 46  // 750px -> 46.875rem\n        updateChatBoardWidth(totalWidth)\n    } else {\n        // console.log('折叠状态：', FunctionBar_isCollapse.value, '当前currentComponent_List：', currentComponent_List.value)\n        updateChatBoardWidth(totalWidth)\n\n    }\n}, { immediate: true })\n\n\n\n\n\n// 创建聊天内容区域的引用\nconst chatContentRef = ref(null)\n// 控制加载状态，防止重复加载\nconst isLoading = ref(false)\n// 标识是否还有更多消息可以加载\nconst hasMore = ref(true)\n// 当前页码，用于分页请求\nconst currentPage = ref(1)\n// 距离顶部触发加载的阈值（像素），当滚动到距离顶部100px时触发加载\nconst loadMoreThreshold = 100\n// 记录上一次的滚动位置\nconst lastScrollTop = ref(0)\n\n// 滚动处理函数\nconst handleScroll = async (e) => {\n    const { scrollTop } = e.target\n    // 只有向上滚动且接近顶部时才触发加载更多\n    if (scrollTop < lastScrollTop.value && chatDetailMessagesTotal.value < selectedChat.value.limit) {\n        if (hasMore.value) {\n            ElMessage.warning('已加载全部消息');\n            hasMore.value = false\n        }\n    }\n\n    if (scrollTop < loadMoreThreshold && !isLoading.value && hasMore.value) {\n        await loadMoreMessages()\n    }\n    // 更新上一次的滚动位置\n    lastScrollTop.value = scrollTop\n}\n\n// 加载更多消息\nconst loadMoreMessages = async () => {\n    if (isLoading.value) {\n        return\n    }\n\n    isLoading.value = true\n    try {\n        currentPage.value += 1\n        // console.log('获取页码：', currentPage.value)\n\n        // 模拟API调用延迟\n        await new Promise(resolve => setTimeout(resolve, 1000))\n\n        getDetailChatMessages(selectedChat.value.from, selectedChat.value.to, selectedChat.value.type, searchName.value, currentPage.value, selectedChat.value.limit)\n\n    } catch (error) {\n        console.error('加载消息失败:', error)\n    } finally {\n        isLoading.value = false\n    }\n}\n\n// 滚动到底部\nconst scrollToBottom = async () => {\n    //当前页数不是1的情况下不滚动到底部\n    console.log('滚动到底部')\n    if (currentPage.value !== 1) {\n        return\n    }\n    //确保在DOM完全更新后再执行滚动，VUE的响应式更新是异步的，消息列表更新之后，DOM不会立即渲染，netTIck会等待VU完成DOM更新后才执行回调，这样就保证了scrollHeight的值是最新的，包含了所有消息的高度\n    await nextTick()\n    const chatContent = chatContentRef.value\n    if (chatContent) {\n        //scrollTop为元素已经滚动的高度\n        chatContent.scrollTop = chatContent.scrollHeight\n    }\n}\n\n\n\n// 在 script setup 部分添加高亮文本的方法\nconst highlightText = (text, keyword) => {\n\n    if (!keyword || !text) return text;\n    try {\n        // 转义特殊字符\n        const escapedKeyword = keyword.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n        const regex = new RegExp(escapedKeyword, 'gi');\n        return text.replace(regex, match => `<span class=\"highlight\">${match}</span>`);\n    } catch (error) {\n        console.error('高亮处理出错:', error);\n        return text;\n    }\n}\n\n// 添加图片预览相关的状态变量\nconst imagePreviewVisible = ref(false)\nconst previewImageUrl = ref('')\n\n// 添加基础尺寸相关的状态变量\nconst scale = ref(1)\nconst baseWidth = ref(0)\nconst baseHeight = ref(0)\nconst minScale = 0.1  // 降低最小缩放比例\nconst maxScale = 5    // 提高最大缩放比例\nconst scaleStep = 0.1\n\n// 处理图片加载完成事件\nconst handleImageLoad = (e) => {\n    const img = e.target\n    baseWidth.value = img.naturalWidth\n    baseHeight.value = img.naturalHeight\n\n    // 计算初始缩放比例，使图片适应屏幕\n    const maxWidth = window.innerWidth * 0.9\n    const maxHeight = window.innerHeight * 0.9\n\n    // 计算宽高比\n    const imageRatio = baseWidth.value / baseHeight.value\n    const screenRatio = maxWidth / maxHeight\n\n    let initialScale\n    if (imageRatio > screenRatio) {\n        // 图片更宽，以宽度为基准\n        initialScale = maxWidth / baseWidth.value\n    } else {\n        // 图片更高，以高度为基准\n        initialScale = maxHeight / baseHeight.value\n    }\n\n    // 确保初始缩放比例在合理范围内\n    scale.value = Math.min(Math.max(initialScale, minScale), maxScale)\n}\n\n// 处理鼠标滚轮事件\nconst handleWheel = (e) => {\n    // 计算新的缩放比例\n    const delta = e.deltaY > 0 ? -scaleStep : scaleStep\n    const newScale = scale.value + delta\n\n    // 限制缩放范围\n    if (newScale >= minScale && newScale <= maxScale) {\n        scale.value = newScale\n    }\n}\n\n// 修改图片点击处理函数，重置缩放比例\nconst handleImageClick = (message) => {\n    const imageUrl = getImageUrl(message)\n    if (imageUrl) {\n        previewImageUrl.value = imageUrl\n        imagePreviewVisible.value = true\n        scale.value = 1 // 重置缩放比例\n        document.body.style.overflow = 'hidden'\n    }\n}\n\n// 修改关闭图片预览函数，重置缩放比例\nconst closeImagePreview = () => {\n    imagePreviewVisible.value = false\n    scale.value = 1 // 重置缩放比例\n    document.body.style.overflow = 'auto'\n}\n\n\n\n\n\n\n</script>\n\n<style>\n/* 整个会话详情面板 */\n.chat-board {\n    height: calc(100vh - 5rem);\n    /* width 由 JavaScript 动态计算 */\n    display: flex;\n    flex-direction: column;\n    background-color: #f7f7f7;\n    border-left: 0.0625rem solid #e6e6e6;\n    transition: 0.4s;\n}\n\n.chat-board-nonShow {\n    height: calc(100vh - 5rem);\n    /* width 由 JavaScript 动态计算 */\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n\n    background-color: #f7f7f7;\n    border-left: 0.0625rem solid #e6e6e6;\n    transition: 0.4s;\n}\n\n.chat-board-nonShow img {\n    width: 30rem;\n    height: 30rem;\n}\n\n.chat-board-nonShow h3 {\n    font-size: 1.4rem;\n    color: #5478eb;\n}\n\n/* 顶部操作区域 */\n.operate-area {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    height: 2.0625rem;\n    padding: 0.125rem 0.8125rem;\n    background-color: #ffffff;\n}\n\n\n/* 会话详情面中顶部的收发信人的信息 */\n.operate-area .detailChat-info {\n    margin-right: 0.625rem;\n}\n\n.operate-area .detailChat-info .sender,\n.operate-area .detailChat-info .receiver {\n    color: #2c2c2c;\n    font-size: 1.04rem;\n    font-weight: 500;\n}\n\n\n/* 会话详情面中顶部的刷新按钮 */\n.operate-area .refresh-area {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n}\n\n\n/* 搜索区域 */\n.search-area {\n    padding: 0 1rem;\n    padding-top: 0.3125rem;\n    padding-bottom: 0.8125rem;\n    background-color: #ffffff;\n    border-bottom: 0.0625rem solid #e6e6e6;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n}\n\n/* 搜索输入框 */\n.search-input {\n    width: 25%;\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n}\n\n/* 搜索图标 */\n.search-icon {\n    color: #909399;\n}\n\n/* 筛选条件按钮 */\n.filter-options {\n    margin-left: 0.625rem;\n}\n\n/* 筛选条件按钮组：消息、文件、图片 */\n.filter-type {\n    margin-left: 0.625rem;\n    border: 0.0625rem solid #add9f5;\n}\n\n/* 筛选条件按钮 */\n.filter-type .el-button {\n    margin-left: 0;\n    border-radius: 0;\n    border: none;\n}\n\n/* 整体聊天内容区域 */\n.chat-content {\n    flex: 1;\n    overflow-y: auto;\n    padding: 1.25rem;\n    display: flex;\n    flex-direction: column;\n    position: relative;\n}\n\n.chat-content::-webkit-scrollbar {\n    width: 0.375rem;\n}\n\n.chat-content::-webkit-scrollbar-thumb {\n    background-color: #e0e0e0;\n    border-radius: 0.1875rem;\n}\n\n.chat-content::-webkit-scrollbar-track {\n    background-color: transparent;\n}\n\n\n/* 控制单条消息的默认位置，靠左*/\n.message-container {\n    display: flex;\n    align-items: flex-start;\n    gap: 0.75rem;\n    margin-top: 1.875rem;\n    width: 100%;\n}\n\n/* 控制发送方单条消息位置，靠右 */\n.message-container.sent {\n    flex-direction: row-reverse;\n}\n\n/* 单条消息的头像默认样式 */\n.avatar {\n    width: 40px;\n    height: 40px;\n    border-radius: 4px;\n    /* background: linear-gradient(135deg, #1890ff, #0960bd); */\n    /* color: white; */\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    font-weight: 500;\n    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.2);\n\n}\n\n/* 发送方单条消息的div样式 */\n.sent .avatar {\n    background: linear-gradient(135deg, #ffffff, #f0f0f0);\n    border-radius: 0.5rem;\n    flex-shrink: 0;\n}\n\n.avatar img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 0.5rem;\n}\n\n\n\n/* 单条消息的气泡内容、发送方的名字、标签、时间 */\n.message-content {\n    max-width: 60%;\n}\n\n/* 发送方名字、标签、时间样式 */\n.sent .sender-info {\n    text-align: right;\n    margin-bottom: 5px;\n}\n\n\n/* 单条消息的时间内容默认样式 */\n.sender-info .message-time {\n    text-align: center;\n    margin: 16px 0;\n    color: #909399;\n    font-size: 12px;\n}\n\n/* 单条消息的发送/接收方的标签默认样式 */\n.sender-info .sender-lable {\n    font-size: 13px;\n\n    margin-left: 5px;\n    margin-right: 5px;\n}\n\n.sender-info .sender-lable.employee {\n    color: #1890ff;\n}\n\n.sender-info .sender-lable.wechat {\n    color: #13d153;\n}\n\n.sender-info .sender-lable.other {\n    color: #ff8432;\n}\n\n/* 单条消息的发送/接收方的名称默认样式 */\n.sender-info .sender-name {\n    font-size: 12px;\n    color: #606266;\n    margin-bottom: 4px;\n}\n\n/* 单条消息的气泡默认样式 */\n.message-bubble {\n    padding: 12px 16px;\n    background-color: #ffffff;\n    border-radius: 4px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n    font-size: 14px;\n    line-height: 1.5;\n    word-break: break-word;\n\n    margin-top: 6px;\n}\n\n/* 发送方单条消息的气泡颜色样式 */\n.sent .message-bubble {\n    background-color: #eef8ff;\n}\n\n/* 图片类型消息 */\n.message-bubble img {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n\n\n\n\n/* 卡片样式 */\n.message-bubble .card-content {\n    /* 给卡片内容设置基础样式，可根据整体布局调整 */\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n\n    max-width: 500px;\n    /* 限制卡片最大宽度，可按需改 */\n    background-color: #fff;\n    border-radius: 8px;\n    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n    padding: 15px;\n}\n\n.top {\n    display: flex;\n    justify-content: space-between;\n    align-items: flex-start;\n}\n\n.left {\n    display: flex;\n    flex-direction: column;\n}\n\n.card-company {\n    font-size: 16px;\n    color: #333;\n    margin-bottom: 15px;\n}\n\n.card-userId {\n    font-size: 14px;\n    color: #666;\n    margin-bottom: 4px;\n}\n\n.card-Name {\n    font-size: 14px;\n    color: #999;\n}\n\n.right {\n    display: flex;\n    align-items: center;\n}\n\n.card-avatar img {\n    /* 添加背景阴影 */\n    box-shadow: 0 1px 2px rgba(94, 94, 94, 0.3);\n    width: 40px;\n    height: 40px;\n    margin-left: 20px;\n}\n\n.bottom {\n    margin-top: 8px;\n    font-size: 12px;\n    color: #999;\n    padding-top: 10px;\n    border-top: solid 1px rgba(150, 150, 150, 0.1);\n}\n\n/* 链接样式 */\n.message-bubble.link-content {\n    padding: 0;\n    margin-bottom: 16px;\n    transition: all 0.3s ease;\n}\n\n.link-card-container {\n    display: flex;\n\n    background-color: #ffffff;\n    border-radius: 12px;\n    overflow: hidden;\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\n    cursor: pointer;\n    transition: transform 0.2s ease, box-shadow 0.2s ease;\n}\n\n.link-card-container:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);\n}\n\n.link-image-container {\n    /* background-color: #f5f7fa; */\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    position: relative;\n    width: 120px;\n    /* 保持原始宽度 */\n    height: 80px;\n    /* 保持原始高度 */\n    overflow: hidden;\n    /* 防止图片溢出容器 */\n\n    padding: 13px;\n\n}\n\n.link-image {\n    max-width: 100%;\n    max-height: 100%;\n    object-fit: contain;\n    /* 保持图片宽高比，完整显示在容器内 */\n    width: auto;\n    /* 让宽度自适应高度 */\n    height: auto;\n    /* 让高度自适应宽度 */\n}\n\n.link-content {\n    flex: 1;\n    padding: 12px 0px;\n    padding-right: 18px;\n    display: flex;\n    flex-direction: column;\n    justify-content: space-between;\n}\n\n.link-title {\n    font-size: 16px;\n    font-weight: 600;\n    color: #1a1a1a;\n    margin-bottom: 8px;\n    line-height: 1.4;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n}\n\n.title-with-symbol {\n    padding-left: 24px;\n    position: relative;\n}\n\n.title-with-symbol::before {\n    content: attr(data-symbol);\n    position: absolute;\n    left: 0;\n    top: 0;\n    background-color: #ff4d4f;\n    color: white;\n    font-size: 12px;\n    padding: 2px 6px;\n    border-radius: 4px;\n}\n\n.link-description {\n    font-size: 14px;\n    color: #4d4d4d;\n    margin-bottom: 8px;\n    line-height: 1.5;\n    display: -webkit-box;\n    -webkit-line-clamp: 3;\n    -webkit-box-orient: vertical;\n    overflow: hidden;\n}\n\n.link-url {\n    font-size: 12px;\n    color: #0070f3;\n    display: flex;\n    align-items: center;\n}\n\n.url-text {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    flex: 1;\n}\n\n.icon-external-link {\n    margin-left: 4px;\n    font-size: 12px;\n}\n\n\n\n\n/* 地图样式 */\n.message-bubble.map-content {\n    box-sizing: border-box;\n    padding: 0;\n\n    border: 1px solid #e6e6e6;\n}\n\n\n.message-bubble .location-address {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    background-color: #f1f1f1;\n    padding: 3px 0;\n}\n\n/* 链接消息 */\n\n\n\n\n\n\n/* 小程序消息 */\n.message-bubble.weapp-content {\n    width: 100%;\n    padding: 10px;\n}\n\n.weapp-content .top .weapp-info {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    margin-bottom: 5px;\n}\n\n.weapp-content .top .weapp-info .weapp-default-icon {\n    width: 24px;\n    height: 24px;\n}\n\n.weapp-content .top .weapp-info .weapp-title {\n    margin-left: 5px;\n}\n\n.weapp-content .top .weapp-default-image {\n    background-color: #eeeeee;\n\n    width: 100%;\n    height: 200px;\n\n    display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n\n    margin-top: 5px;\n}\n\n.weapp-content .bottom {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n\n    margin-top: 8px;\n\n    border-top: 1px solid #e6e6e6;\n}\n\n.weapp-content .bottom .weapp-mini-icon {\n    width: 12px;\n    height: 12px;\n}\n\n.weapp-content .bottom span {\n    font-size: 12px;\n    color: #909399;\n    margin-left: 3px;\n    margin-top: 2px;\n}\n\n/* 会话记录消息 */\n.message-bubble.chatrecord-content {\n    cursor: pointer;\n}\n\n.chatrecord-content .chatrecord-part {\n    padding: 0 3px;\n    margin-top: 4px;\n    color: #909399;\n    font-weight: 300;\n}\n\n/* 待办消息样式 */\n.message-bubble.todo-content {\n    width: 300px;\n    padding: 0;\n    border: 1px solid #e6e6e6;\n    /* box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05); */\n    transition: all 0.3s ease;\n}\n\n.todo-content .todo-title {\n    padding: 12px 16px;\n    background-color: #f0f7ff;\n    color: #1677ff;\n    font-size: 15px;\n    font-weight: 500;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n}\n\n.todo-content .todo-title::before {\n    content: '';\n    display: inline-block;\n    width: 4px;\n    height: 16px;\n    background-color: #1677ff;\n    margin-right: 12px;\n    border-radius: 2px;\n}\n\n.todo-content .todo-content {\n    padding: 12px 16px;\n    color: #666;\n    font-size: 14px;\n    line-height: 1.6;\n    background-color: #ffffff;\n}\n\n/* 发送方待办消息样式覆盖 */\n.sent .message-bubble.todo-content {\n    background-color: #ffffff;\n}\n\n.sent .todo-content .todo-title {\n    background-color: #e6f4ff;\n}\n\n/* 投票消息样式 */\n.message-bubble.vote-content {\n    width: 300px;\n    padding: 0;\n    border: 1px solid #e6e6e6;\n    background-color: #ffffff;\n    border-radius: 8px;\n}\n\n/* 发起投票样式 */\n.vote-initiate {\n    display: flex;\n    flex-direction: column;\n}\n\n.vote-header {\n    padding: 12px 16px;\n    border-bottom: 1px solid #f0f0f0;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.vote-title {\n    font-size: 15px;\n    font-weight: 500;\n    color: #333;\n}\n\n.vote-options {\n    padding: 12px 16px;\n}\n\n.vote-option {\n    margin-bottom: 12px;\n}\n\n.vote-option:last-child {\n    margin-bottom: 0;\n}\n\n/* 参与投票样式 */\n.vote-participate {\n    display: flex;\n    flex-direction: column;\n}\n\n.vote-selected {\n    padding: 12px 16px;\n}\n\n.selected-option {\n    display: flex;\n    align-items: center;\n    margin-bottom: 8px;\n    color: #67c23a;\n}\n\n.selected-option .el-icon {\n    margin-right: 8px;\n}\n\n/* 投票底部样式 */\n.vote-footer {\n    padding: 8px 16px;\n    border-top: 1px solid #f0f0f0;\n    background-color: #fafafa;\n}\n\n.vote-id {\n    font-size: 12px;\n    color: #999;\n}\n\n/* 发送方投票消息样式覆盖 */\n.sent .vote-content {\n    background-color: #ffffff;\n}\n\n.sent .vote-header {\n    background-color: #f0f7ff;\n}\n\n.sent .vote-footer {\n    background-color: #f0f7ff;\n}\n\n/* 填表消息样式 */\n.message-bubble.collect-content {\n    width: 350px;\n    padding: 0;\n    border: 1px solid #e6e6e6;\n    background-color: #ffffff;\n    border-radius: 8px;\n}\n\n/* 表单头部 */\n.collect-header {\n    padding: 16px;\n    border-bottom: 1px solid #f0f0f0;\n    background-color: #f9f9f9;\n}\n\n.collect-title {\n    font-size: 16px;\n    font-weight: 500;\n    color: #333;\n    margin-bottom: 8px;\n}\n\n.collect-info {\n    display: flex;\n    justify-content: space-between;\n    font-size: 12px;\n    color: #666;\n    margin-bottom: 4px;\n}\n\n.collect-room {\n    font-size: 12px;\n    color: #666;\n}\n\n/* 表单主体 */\n.collect-body {\n    padding: 16px;\n}\n\n.collect-item {\n    margin-bottom: 16px;\n}\n\n.collect-item:last-child {\n    margin-bottom: 0;\n}\n\n.item-label {\n    margin-bottom: 8px;\n    font-size: 14px;\n    color: #333;\n}\n\n.item-required {\n    color: #f56c6c;\n    margin-right: 4px;\n}\n\n.item-input {\n    width: 100%;\n}\n\n.item-input :deep(.el-input),\n.item-input :deep(.el-input-number),\n.item-input :deep(.el-date-picker),\n.item-input :deep(.el-time-picker) {\n    width: 100%;\n}\n\n/* 表单底部 */\n.collect-footer {\n    padding: 12px 16px;\n    border-top: 1px solid #f0f0f0;\n    text-align: right;\n    background-color: #f9f9f9;\n}\n\n/* 发送方填表消息样式覆盖 */\n.sent .collect-content {\n    background-color: #ffffff;\n}\n\n.sent .collect-header,\n.sent .collect-footer {\n    background-color: #f0f7ff;\n}\n\n\n/* TODO：红包消息样式  */\n.message-bubble.redpacket-content {\n    padding: 0;\n    width: 240px;\n    background-color: #f43f3b;\n    border-radius: 8px;\n    overflow: hidden;\n}\n\n.redpacket-wrapper {\n    display: flex;\n    flex-direction: column;\n    color: #fff;\n    cursor: pointer;\n}\n\n.redpacket-icon {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    padding: 16px 0;\n}\n\n.redpacket-icon i {\n    font-size: 36px;\n}\n\n.redpacket-info {\n    padding: 0 16px 16px;\n    text-align: center;\n}\n\n.redpacket-wish {\n    font-size: 14px;\n    margin-bottom: 8px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.redpacket-amount {\n    font-size: 24px;\n    font-weight: bold;\n}\n\n.redpacket-type {\n    background-color: rgba(0, 0, 0, 0.1);\n    padding: 8px 0;\n    text-align: center;\n    font-size: 12px;\n    color: rgba(255, 255, 255, 0.9);\n}\n\n/* 发送方红包样式 */\n.sent .redpacket-content {\n    background-color: #ff6b6b;\n}\n\n.redpacket-wrapper:hover {\n    opacity: 0.95;\n}\n\n/* 会议消息样式 */\n.message-bubble.meeting-content {\n    width: 320px;\n    padding: 0;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    border-radius: 8px;\n}\n\n.meeting-header {\n    padding: 12px 16px;\n    background-color: #f0f7ff;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n}\n\n.meeting-header i {\n    color: #1677ff;\n    font-size: 18px;\n    margin-right: 8px;\n}\n\n.meeting-title {\n    font-size: 15px;\n    font-weight: 500;\n    color: #1677ff;\n    flex: 1;\n}\n\n.meeting-type {\n    font-size: 12px;\n    color: #1677ff;\n    background-color: rgba(22, 119, 255, 0.1);\n    padding: 2px 8px;\n    border-radius: 4px;\n}\n\n.meeting-body {\n    padding: 12px 16px;\n}\n\n.meeting-time,\n.meeting-address,\n.meeting-remarks {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 12px;\n    color: #606266;\n    font-size: 14px;\n}\n\n.meeting-time:last-child,\n.meeting-address:last-child,\n.meeting-remarks:last-child {\n    margin-bottom: 0;\n}\n\n.meeting-time i,\n.meeting-address i,\n.meeting-remarks i {\n    font-size: 14px;\n    margin-right: 8px;\n    margin-top: 3px;\n    color: #909399;\n}\n\n.time-details {\n    flex: 1;\n}\n\n.time-row {\n    display: flex;\n    margin-bottom: 4px;\n}\n\n.time-row:last-child {\n    margin-bottom: 0;\n}\n\n.time-label {\n    color: #909399;\n    margin-right: 4px;\n    min-width: 70px;\n}\n\n.remarks-label {\n    color: #909399;\n    margin-right: 4px;\n}\n\n.remarks-content {\n    flex: 1;\n    word-break: break-all;\n}\n\n.meeting-footer {\n    padding: 8px 16px;\n    background-color: #f7f7f7;\n    border-top: 1px solid #e6e6e6;\n}\n\n.meeting-id {\n    font-size: 12px;\n    color: #909399;\n}\n\n/* 发送方会议消息样式覆盖 */\n.sent .meeting-content {\n    background-color: #ffffff;\n}\n\n.sent .meeting-header {\n    background-color: #f0f7ff;\n}\n\n.sent .meeting-footer {\n    background-color: #f7f7f7;\n}\n\n/* 切换企业日志消息样式 */\n.message-container .switch-label {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding-top: 10px;\n    width: 100%;\n}\n\n.switch-label .switch-time {\n    font-size: 12px;\n    color: #909399;\n    margin-bottom: 7px;\n    text-align: center;\n    margin-top: 30px;\n}\n\n.switch-label p {\n    font-size: 13px;\n    color: #606266;\n    text-align: center;\n    background-color: #f2f6fc;\n    width: 150px;\n    height: 25px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: 4px;\n    margin: 0 auto;\n}\n\n/* 在线文档消息样式 */\n.docmsg-content {\n    width: 280px;\n    padding: 0;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    cursor: pointer;\n}\n\n.docmsg-content .top {\n    padding: 12px 16px;\n    background-color: #f5f7fa;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n}\n\n.docmsg-content .top .docmsg-icon-mini {\n    width: 20px;\n    height: 20px;\n    margin-right: 8px;\n}\n\n.docmsg-content .top .docmsg-type {\n    font-size: 13px;\n    color: #606266;\n}\n\n.docmsg-content .bottom {\n    padding: 12px 16px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}\n\n.docmsg-content .bottom .docmsg-msg {\n    flex: 1;\n    margin-right: 12px;\n}\n\n.docmsg-content .bottom .docmsg-title {\n    display: block;\n    font-size: 14px;\n    color: #303133;\n    margin-bottom: 4px;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}\n\n.docmsg-content .bottom .docmsg-creator {\n    display: block;\n    font-size: 12px;\n    color: #909399;\n}\n\n.docmsg-content .bottom .docmsg-icon {\n    width: 32px;\n    height: 32px;\n    flex-shrink: 0;\n}\n\n/* 发送方在线文档消息样式覆盖 */\n.sent .docmsg-content .top {\n    background-color: #f0f7ff;\n}\n\n.docmsg-content:hover {\n    background-color: #f5f7fa;\n}\n\n/* 日程消息样式 */\n.calendar-content {\n    width: 300px;\n    padding: 0;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    border-radius: 8px;\n    overflow: hidden;\n}\n\n.calendar-header {\n    padding: 12px 16px;\n    background-color: #f0f7ff;\n    border-bottom: 1px solid #e6e6e6;\n    display: flex;\n    align-items: center;\n    gap: 8px;\n}\n\n.calendar-header img {\n    width: 20px;\n    height: 20px;\n    object-fit: contain;\n}\n\n.calendar-type {\n    font-size: 14px;\n    color: #1677ff;\n    font-weight: 500;\n}\n\n.calendar-body {\n    padding: 16px;\n}\n\n.calendar-title {\n    font-size: 15px;\n    font-weight: 500;\n    color: #303133;\n    margin-bottom: 16px;\n}\n\n.calendar-info .info-item {\n    display: flex;\n    align-items: flex-start;\n    margin-bottom: 12px;\n    color: #606266;\n    font-size: 13px;\n    line-height: 1.5;\n}\n\n.calendar-info .info-item:last-child {\n    margin-bottom: 0;\n}\n\n.calendar-info .info-item .el-icon {\n    margin-right: 8px;\n    margin-top: 3px;\n    color: #909399;\n    flex-shrink: 0;\n    font-size: 16px;\n}\n\n.calendar-info .info-item span {\n    flex: 1;\n    word-break: break-all;\n}\n\n.calendar-info .info-item span+span {\n    margin-left: 8px;\n}\n\n/* 发送方日程消息样式覆盖 */\n.sent .calendar-content {\n    background-color: #ffffff;\n}\n\n.sent .calendar-header {\n    background-color: #f0f7ff;\n}\n\n.calendar-info .info-item.time-info {\n    align-items: flex-start;\n}\n\n.calendar-info .time-details {\n    flex: 1;\n}\n\n.calendar-info .time-details .time-row {\n    color: #606266;\n    margin-bottom: 4px;\n}\n\n.calendar-info .time-details .time-row:last-child {\n    margin-bottom: 0;\n}\n\n\n\n/* 加载更多提示样式 */\n.loading-more {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: 1rem;\n    color: #909399;\n    font-size: 0.875rem;\n}\n\n.loading-more .loading-icon {\n    margin-right: 0.5rem;\n    animation: rotate 1s linear infinite;\n}\n\n@keyframes rotate {\n    from {\n        transform: rotate(0deg);\n    }\n\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n\n\n/* ----------------------------------------------------------------------------- */\n/* 文本消息内容样式 */\n.text-content {\n    max-width: 100%;\n    white-space: pre-wrap;\n    word-wrap: break-word;\n}\n\n.text-content p {\n    margin: 0;\n    padding: 0;\n    line-height: 1.6;\n    color: #333;\n}\n\n\n/* 发送方文本消息样式 */\n.sent .text-content {\n    background-color: #eef8ff;\n}\n\n.sent .text-content p {\n    color: #333;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 图片消息样式 */\n.message-bubble.image-content {\n    max-width: 300px;\n    /* max-height: 400px; */\n    padding: 0;\n    overflow: hidden;\n    border-radius: 4px;\n    background-color: transparent;\n}\n\n.message-bubble.image-content img {\n    width: 100%;\n    height: 100%;\n    /*保持图片比例，确保完整显示 */\n    object-fit: contain;\n    display: block;\n}\n\n/* 发送方图片消息样式 */\n.sent .message-bubble.image-content {\n    background-color: transparent;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 文件消息样式  */\n.message-bubble.file-content {\n    display: flex;\n    flex-direction: row;\n    justify-content: space-between;\n    align-items: center;\n    padding: 12px 16px;\n    max-width: 90%;\n    background-color: #ffffff;\n    border: 1px solid #e6e6e6;\n    border-radius: 4px;\n    cursor: pointer;\n    transition: background-color 0.2s ease;\n}\n\n.file-content:hover {\n    background-color: #f5f7fa;\n}\n\n.file-content .left {\n    flex: 1;\n    overflow: hidden;\n    margin-right: 5px;\n    min-width: 0;\n    /* 确保flex子元素可以正确收缩 */\n}\n\n.file-content .left .file-name {\n    font-size: 14px;\n    color: #303133;\n    margin: 0 0 4px 0;\n    word-break: break-all;\n    white-space: normal;\n    overflow-wrap: break-word;\n    line-height: 1.4;\n}\n\n.file-content .left .file-size {\n    font-size: 12px;\n    color: #909399;\n    margin: 0;\n}\n\n.file-content .right {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n    margin-left: 8px;\n}\n\n.file-content .right img {\n    width: 32px;\n    height: 32px;\n    object-fit: contain;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 撤回消息样式  */\n.message-container .recall-lable {\n    display: flex;\n    flex-direction: row;\n\n    margin: auto;\n    margin-left: 2px;\n\n    background-color: #d1d1d1;\n    border-radius: 4px;\n    padding: 3px 4px;\n\n    color: #fdfdfd;\n    font-size: 12px;\n    font-weight: 200;\n    letter-spacing: 0.5px;\n\n    cursor: default;\n\n}\n\n.message-container .recall-lable .el-icon {\n    margin-right: 3px;\n}\n\n.message-container.sent .recall-lable {\n    display: flex;\n    flex-direction: row;\n\n    margin: auto;\n    margin-right: 2px;\n\n    background-color: #d1d1d1;\n    border-radius: 4px;\n    padding: 3px 4px;\n\n    color: #fdfdfd;\n    font-weight: 200;\n    letter-spacing: 0.5px;\n\n    cursor: default;\n\n}\n\n/* ----------------------------------------------------------------------------- */\n/* agree 类型消息*/\n.message-container .agree,\n.message-container .disagree {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n\n    padding-top: 10px;\n}\n\n.agree .agree-time,\n.disagree .disagree-time {\n    font-size: 12px;\n    color: #909399;\n    margin-bottom: 7px;\n    text-align: center;\n}\n\n.agree p,\n.disagree p {\n    font-size: 14px;\n    color: #359632;\n    text-align: center;\n    background-color: #adff94;\n\n    width: 150px;\n    height: 25px;\n\n    display: flex;\n    align-items: center;\n    justify-content: center;\n\n    border-radius: 4px;\n\n}\n\n.disagree p {\n    background-color: #ff9191;\n    color: #cc3a3a;\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 语音消息样式 */\n.voice-content {\n    min-width: 200px;\n    max-width: 350px;\n    padding: 12px 16px;\n    transition: all 0.3s ease;\n    border-radius: 12px;\n    background-color: #ffffff;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n    border: 1px solid #e4e7ed;\n}\n\n.voice-content:hover {\n    background-color: #f5f7fa;\n    transform: translateY(-1px);\n    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n}\n\n.voice-wrapper {\n    display: flex;\n    flex-direction: column;\n    gap: 8px;\n}\n\n.voice-controls {\n    display: flex;\n    align-items: center;\n    gap: 12px;\n}\n\n.voice-play-btn {\n    width: 36px;\n    height: 36px;\n    border: none;\n    background-color: #5188ff;\n    color: white;\n    transition: all 0.3s ease;\n}\n\n.voice-play-btn:hover {\n    background-color: #4070ff;\n    transform: scale(1.05);\n}\n\n.voice-play-btn:active {\n    transform: scale(0.95);\n}\n\n.voice-duration {\n    color: #606266;\n    font-size: 14px;\n    font-weight: 500;\n    margin-left: auto;\n}\n\n.voice-visual-container {\n    width: 100%;\n    height: 40px;\n    position: relative;\n    background-color: #f8f9fa;\n    border-radius: 6px;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n}\n\n.voice-waveform {\n    width: 100%;\n    height: 100%;\n}\n\n.voice-loading {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    background-color: #f0f0f0;\n    width: 100%;\n    height: 100%;\n    color: #999;\n    font-size: 12px;\n}\n\n.voice-waveform-placeholder {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n    width: 100%;\n    height: 100%;\n    padding: 0 8px;\n}\n\n.voice-bars {\n    display: flex;\n    align-items: center;\n    gap: 2px;\n    flex: 1;\n}\n\n.voice-bars .bar {\n    width: 3px;\n    background-color: #ddd;\n    border-radius: 2px;\n    transition: all 0.3s ease;\n}\n\n.voice-bars .bar:nth-child(odd) {\n    height: 20px;\n}\n\n.voice-bars .bar:nth-child(even) {\n    height: 15px;\n}\n\n.voice-bars .bar:nth-child(3n) {\n    height: 25px;\n}\n\n.voice-content.playing .voice-bars .bar {\n    background-color: #5188ff;\n    animation: wave 1.5s infinite ease-in-out;\n}\n\n.sent .voice-content.playing .voice-bars .bar {\n    background-color: #1976d2;\n}\n\n.voice-text {\n    font-size: 12px;\n    color: #666;\n    white-space: nowrap;\n}\n\n@keyframes wave {\n    0%, 100% {\n        transform: scaleY(1);\n    }\n    50% {\n        transform: scaleY(1.5);\n    }\n}\n\n/* 发送方语音消息样式覆盖 */\n.sent .voice-content {\n    background-color: #e3f2fd;\n    border-color: #bbdefb;\n}\n\n.sent .voice-content:hover {\n    background-color: #d6eafb;\n}\n\n.sent .voice-play-btn {\n    background-color: #1976d2;\n}\n\n.sent .voice-play-btn:hover {\n    background-color: #1565c0;\n}\n\n.sent .voice-visual-container {\n    background-color: #e8f4fd;\n}\n\n/* 音频播放时的动画效果 */\n@keyframes pulse {\n    0% {\n        transform: scale(1);\n    }\n\n    50% {\n        transform: scale(1.05);\n    }\n\n    100% {\n        transform: scale(1);\n    }\n}\n\n.voice-content.playing .voice-play-btn {\n    animation: pulse 1.5s infinite;\n}\n\n/* vue-audio-visual 样式覆盖 */\n.voice-waveform canvas {\n    border-radius: 4px;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n    .voice-content {\n        min-width: 180px;\n        max-width: 280px;\n        padding: 10px 12px;\n    }\n\n    .voice-play-btn {\n        width: 32px;\n        height: 32px;\n    }\n\n    .voice-visual-container {\n        height: 35px;\n    }\n}\n\n/* ----------------------------------------------------------------------------- */\n/* 视频消息样式 */\n.video-wrapper {\n    position: relative;\n    max-width: 300px;\n    border-radius: 8px;\n    overflow: hidden;\n    margin-bottom: 5px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    background-color: #2a2a2a;\n}\n\n.video-container {\n    position: relative;\n    width: 100%;\n}\n\n.video-player {\n    width: 100%;\n    max-height: 200px;\n    object-fit: contain;\n    display: block;\n    background-color: #000;\n    border-radius: 8px;\n}\n\n.video-overlay {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background-color: rgba(0, 0, 0, 0.3);\n    opacity: 0;\n    transition: opacity 0.3s ease;\n    cursor: pointer;\n}\n\n.video-overlay:hover {\n    opacity: 1;\n}\n\n.play-button {\n    width: 50px;\n    height: 50px;\n    border-radius: 50%;\n    background-color: rgba(255, 255, 255, 0.8);\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    transition: transform 0.3s ease;\n}\n\n.play-button:hover {\n    transform: scale(1.1);\n}\n\n.play-button .el-icon {\n    font-size: 24px;\n    color: #409eff;\n}\n\n.video-info {\n    padding: 8px 10px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    background-color: #3a3a3a;\n}\n\n.video-duration {\n    font-size: 12px;\n    color: #fff;\n    padding: 2px 5px;\n    border-radius: 3px;\n    background-color: rgba(0, 0, 0, 0.3);\n}\n\n.chat-msg-sent .video-wrapper {\n    background-color: #2b82ff;\n}\n\n.chat-msg-sent .video-info {\n    background-color: #1a70e8;\n}\n\n.chat-msg-received .video-wrapper {\n    background-color: #383838;\n}\n\n.chat-msg-received .video-info {\n    background-color: #2a2a2a;\n}\n\n/* 遮罩层样式 */\n.content-overlay {\n    position: absolute;\n    top: auto;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(255, 255, 255, 0.8);\n    display: flex;\n    flex-direction: column;\n    justify-content: center;\n    align-items: center;\n    z-index: 10;\n    height: 100%;\n    /* width: 100vw; */\n}\n\n.content-overlay .loading-icon {\n    color: #409EFF;\n    animation: rotate 1s linear infinite;\n}\n\n.content-overlay .loading-text {\n    margin-top: 10px;\n    color: #606266;\n    font-size: 14px;\n}\n\n@keyframes rotate {\n    from {\n        transform: rotate(0deg);\n    }\n\n    to {\n        transform: rotate(360deg);\n    }\n}\n\n/* 确保chat-content是relative定位，这样遮罩层的绝对定位才能正确覆盖 */\n.chat-content {\n    position: relative;\n    /* ... 保持原有样式 ... */\n}\n\n/* 添加高亮样式 */\n.highlight {\n    background-color: #fff131;\n    padding: 2px 4px;\n    border-radius: 2px;\n    color: #000;\n    /*font-weight: bold;*/\n}\n\n/* 添加图片预览相关样式 */\n.image-preview-dialog {\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.image-preview-dialog :deep(.el-dialog__body) {\n    padding: 0;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background-color: rgba(0, 0, 0, 0.9);\n}\n\n.preview-image {\n    max-width: 100%;\n    max-height: 80vh;\n    object-fit: contain;\n}\n\n/* 修改图片消息样式，添加鼠标手型 */\n.message-bubble.image-content {\n    cursor: pointer;\n}\n\n/* 图片预览相关样式 */\n.image-preview-mask {\n    position: fixed;\n    top: 0;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    background-color: rgba(0, 0, 0, 0.85);\n    z-index: 2000;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n}\n\n.image-preview-container {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    overflow: visible;\n    /* 允许内容溢出 */\n}\n\n.preview-image {\n    object-fit: contain;\n    border-radius: 4px;\n    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);\n    transition: width 0.1s ease-out, height 0.1s ease-out;\n    /* 添加平滑过渡效果 */\n}\n\n/* 图片消息样式 */\n.message-bubble.image-content {\n    cursor: pointer;\n    transition: transform 0.2s ease;\n}\n\n.message-bubble.image-content:hover {\n    transform: scale(1.02);\n}\n\n/* 修改图片预览相关样式 */\n.image-preview-container {\n    position: relative;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    overflow: visible;\n    min-width: 100px;\n    min-height: 100px;\n}\n\n.preview-image {\n    object-fit: contain;\n    border-radius: 4px;\n    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);\n    transition: width 0.1s ease-out, height 0.1s ease-out;\n    will-change: width, height;\n    /* 优化性能 */\n}\n\n/* 文件操作弹窗样式 */\n.file-dialog-content {\n    padding: 20px;\n}\n\n.file-info {\n    display: flex;\n    align-items: center;\n    margin-bottom: 20px;\n    padding: 15px;\n    background-color: #f5f7fa;\n    border-radius: 8px;\n}\n\n.file-icon {\n    width: 40px;\n    height: 40px;\n    margin-right: 15px;\n}\n\n.file-details {\n    flex: 1;\n}\n\n.file-details .file-name {\n    font-size: 16px;\n    color: #303133;\n    margin: 0 0 5px 0;\n    word-break: break-all;\n}\n\n.file-details .file-size {\n    font-size: 14px;\n    color: #909399;\n    margin: 0;\n}\n\n.file-actions {\n    display: flex;\n    justify-content: center;\n    gap: 20px;\n}\n\n.file-actions .el-button {\n    min-width: 100px;\n}\n\n.preview-dialog {\n    :deep(.el-dialog__body) {\n        padding: 0;\n        height: calc(80vh - 120px);\n        overflow: hidden;\n    }\n}\n\n.preview-container {\n    height: 100%;\n    overflow: auto;\n    background-color: #f5f7fa;\n    padding: 20px;\n}\n\n:deep(.vue-office-docx),\n:deep(.vue-office-excel),\n:deep(.vue-office-pdf) {\n    height: 100%;\n    background-color: #fff;\n    border-radius: 4px;\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\n}\n</style>\n"], "mappings": ";OA+RaA,UAA4B;;EA5R5BC,KAAK,EAAC;AAAc;;EAHjCC,GAAA;EAIiBD,KAAK,EAAC;;;EAEDA,KAAK,EAAC;AAAQ;;EAGdA,KAAK,EAAC;AAAU;;EATtCC,GAAA;EAYiBD,KAAK,EAAC;;;EAEDA,KAAK,EAAC;AAAQ;;EAEnBA,KAAK,EAAC;AAAc;;EAOxBA,KAAK,EAAC;AAAa;;EAWfA,KAAK,EAAC;AAAgB;;EAWtBA,KAAK,EAAC;AAAa;;EA7CpCC,GAAA;EAqFkCD,KAAK,EAAC;;;EArFxCC,GAAA;AAAA;;EAAAA,GAAA;EAkG6BD,KAAK,EAAC;;oBAlGnC;;EAAAC,GAAA;EAuG6BD,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAa;;EACdA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAc;;EA7G1DC,GAAA;EAiHiCD,KAAK,EAAC;;oBAjHvC;oBAAA;;EAyHqCA,KAAK,EAAC;AAAM;;EACVA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAGnBA,KAAK,EAAC;AAAO;oBA9HlD;oBAAA;oBAAA;;EAAAC,GAAA;EA4IiCD,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC;AAAgB;;EASjBA,KAAK,EAAC;AAAgB;;EAI3BA,KAAK,EAAC;AAAwB;;EAE1BA,KAAK,EAAC;AAA4B;;EAC9BA,KAAK,EAAC;AAAY;oBA/JnE;;EAAAC,GAAA;EAoLiCD,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAiB;oBAtL5D;;EAAAC,GAAA;EAkMiCD,KAAK,EAAC;;;EAEFA,KAAK,EAAC;AAAgB;;EAClBA,KAAK,EAAC;AAAK;;EACPA,KAAK,EAAC;AAAM;;EACRA,KAAK,EAAC;AAAc;;EAGpBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAW;;EAIrBA,KAAK,EAAC;AAAO;;EACTA,KAAK,EAAC;AAAa;oBAlNpE;;EAAAC,GAAA;EA8NiCD,KAAK,EAAC;;oBA9NvC;;EAkOyCA,KAAK,EAAC;AAAsB;oBAlOrE;;EAsOyCA,KAAK,EAAC;AAAc;;EAIhBA,KAAK,EAAC;AAAkB;;EAGxBA,KAAK,EAAC;AAAU;;EACXA,KAAK,EAAC;AAAU;;EA9OlEC,GAAA;EAyP6BD,KAAK,EAAC;;;EAzPnCC,GAAA;EAiQ6BD,KAAK,EAAC;;;EACFA,KAAK,EAAC;AAAY;;EAlQnDC,GAAA;EAuQ6BD,KAAK,EAAC;;;EACFA,KAAK,EAAC;AAAe;;EAxQtDC,GAAA;EAgRyBD,KAAK,EAAC;;;EACFA,KAAK,EAAC;AAAa;oBAjRhD;;EAoTaA,KAAK,EAAC;AAAqB;;EACvBA,KAAK,EAAC;AAAW;oBArTlC;;EAuTqBA,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAW;;EACjBA,KAAK,EAAC;AAAW;;EAKvBA,KAAK,EAAC;AAAc;;EAaxBA,KAAK,EAAC;AAAmB;;;;;;;;uBA3UtCE,mBAAA,CAAAC,SAAA,SAE0DC,MAAA,CAAAC,gBAAgB,I,cAAtEH,mBAAA,CAyRM;IA3RVD,GAAA;IAESD,KAAK,EAAC,YAAY;IAAEM,KAAK,EAFlCC,eAAA,CAEoCH,MAAA,CAAAI,cAAc;MAC1CC,mBAAA,CAkBM,OAlBNC,UAkBM,GAjBiCN,MAAA,CAAAO,YAAY,CAACC,IAAI,U,cAApDV,mBAAA,CAOM,OAPNW,UAOM,GANFC,mBAAA,gEAAiE,EACjEL,mBAAA,CAAuD,QAAvDM,UAAuD,EAAAC,gBAAA,CAA/BZ,MAAA,CAAAO,YAAY,CAACM,QAAQ,kB,0BAC7CR,mBAAA,CAAgB,cAAV,KAAG,sBACTK,mBAAA,oEAAqE,EACrEL,mBAAA,CAAuD,QAAvDS,UAAuD,EAAAF,gBAAA,CAA7BZ,MAAA,CAAAO,YAAY,CAACQ,MAAM,kB,0BAC7CV,mBAAA,CAAmB,cAAb,QAAM,qB,KAV5BK,mBAAA,gBAY+CV,MAAA,CAAAO,YAAY,CAACC,IAAI,mB,cAApDV,mBAAA,CAGM,OAHNkB,UAGM,GAFFN,mBAAA,gEAAiE,EACjEL,mBAAA,CAAgE,QAAhEY,UAAgE,EAA3C,KAAG,GAAAL,gBAAA,CAAGZ,MAAA,CAAAO,YAAY,CAACM,QAAQ,IAAG,QAAM,gB,KAdzEH,mBAAA,gBAgBYL,mBAAA,CAIM,OAJNa,UAIM,GAHFC,YAAA,CAEUC,kBAAA;IAFDC,IAAI,EAAC,MAAM;IAAEC,OAAK,EAAEtB,MAAA,CAAAuB;;IAjB7CC,OAAA,EAAAC,QAAA,CAkBoB,MAAW,CAAXN,YAAA,CAAWnB,MAAA,a;IAlB/B0B,CAAA;UAsBQhB,mBAAA,YAAe,EACfL,mBAAA,CAiDM,OAjDNsB,UAiDM,GAhDFjB,mBAAA,8EAA2E,EAC3ES,YAAA,CAMWS,mBAAA;IA/BvBC,UAAA,EAyB+B7B,MAAA,CAAA8B,UAAU;IAzBzC,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyB+BhC,MAAA,CAAA8B,UAAU,GAAAE,MAAA;IAAEC,WAAW,EAAC,QAAQ;IAACrC,KAAK,EAAC,cAAc;IAACsC,SAAS,EAAT;;IAC1DC,MAAM,EAAAV,QAAA,CACb,MAEU,CAFVN,YAAA,CAEUC,kBAAA;MAFDxB,KAAK,EAAC;IAAa;MA3BhD4B,OAAA,EAAAC,QAAA,CA4BwB,MAAU,CAAVN,YAAA,CAAUnB,MAAA,Y;MA5BlC0B,CAAA;;IAAAA,CAAA;qCAgCYhB,mBAAA,mBAAsB,EAEtBL,mBAAA,CASM,OATN+B,UASM,GARFjB,YAAA,CAOakB,qBAAA;IAPDC,OAAO,EAAC,YAAY;IAACC,SAAS,EAAC,KAAK;IAACC,MAAM,EAAC;;IAnCxEhB,OAAA,EAAAC,QAAA,CAoCoB,MAKY,CALZN,YAAA,CAKYsB,oBAAA;MALDjC,IAAI,EAAC,SAAS;MAACkC,KAAK,EAAL,EAAK;MAACrB,IAAI,EAAC;;MApCzDG,OAAA,EAAAC,QAAA,CAqCwB,MAEU,CAFVN,YAAA,CAEUC,kBAAA;QAvClCI,OAAA,EAAAC,QAAA,CAsC4B,MAAU,CAAVN,YAAA,CAAUnB,MAAA,Y;QAtCtC0B,CAAA;oCAAAiB,gBAAA,CAuCkC,QAEd,G;MAzCpBjB,CAAA;;IAAAA,CAAA;QA6CYrB,mBAAA,CAyBM,OAzBNuC,WAyBM,GAxBFzB,YAAA,CAOakB,qBAAA;IAPDC,OAAO,EAAC,YAAY;IAACC,SAAS,EAAC,KAAK;IAACC,MAAM,EAAC;;IA9CxEhB,OAAA,EAAAC,QAAA,CA+CoB,MAKY,CALZN,YAAA,CAKYsB,oBAAA;MALDjC,IAAI,EAAC,SAAS;MAACkC,KAAK,EAAL,EAAK;MAACrB,IAAI,EAAC;;MA/CzDG,OAAA,EAAAC,QAAA,CAgDwB,MAEU,CAFVN,YAAA,CAEUC,kBAAA;QAlDlCI,OAAA,EAAAC,QAAA,CAiD4B,MAAgB,CAAhBN,YAAA,CAAgBnB,MAAA,kB;QAjD5C0B,CAAA;oCAAAiB,gBAAA,CAkDkC,MAEd,G;MApDpBjB,CAAA;;IAAAA,CAAA;MAsDgBP,YAAA,CAOakB,qBAAA;IAPDC,OAAO,EAAC,YAAY;IAACC,SAAS,EAAC,KAAK;IAACC,MAAM,EAAC;;IAtDxEhB,OAAA,EAAAC,QAAA,CAuDoB,MAKY,CALZN,YAAA,CAKYsB,oBAAA;MALDjC,IAAI,EAAC,SAAS;MAACkC,KAAK,EAAL,EAAK;MAACrB,IAAI,EAAC;;MAvDzDG,OAAA,EAAAC,QAAA,CAwDwB,MAEU,CAFVN,YAAA,CAEUC,kBAAA;QA1DlCI,OAAA,EAAAC,QAAA,CAyD4B,MAAY,CAAZN,YAAA,CAAYnB,MAAA,c;QAzDxC0B,CAAA;sCAAAiB,gBAAA,CA0DkC,MAEd,G;MA5DpBjB,CAAA;;IAAAA,CAAA;MA8DgBP,YAAA,CAOakB,qBAAA;IAPDC,OAAO,EAAC,YAAY;IAACC,SAAS,EAAC,KAAK;IAACC,MAAM,EAAC;;IA9DxEhB,OAAA,EAAAC,QAAA,CA+DoB,MAKY,CALZN,YAAA,CAKYsB,oBAAA;MALDjC,IAAI,EAAC,SAAS;MAACkC,KAAK,EAAL,EAAK;MAACrB,IAAI,EAAC;;MA/DzDG,OAAA,EAAAC,QAAA,CAgEwB,MAEU,CAFVN,YAAA,CAEUC,kBAAA;QAlElCI,OAAA,EAAAC,QAAA,CAiE4B,MAAW,CAAXN,YAAA,CAAWnB,MAAA,a;QAjEvC0B,CAAA;sCAAAiB,gBAAA,CAkEkC,MAEd,G;MApEpBjB,CAAA;;IAAAA,CAAA;UA0EQhB,mBAAA,YAAe,EACfL,mBAAA,CA+MM;IA/MDT,KAAK,EAAC,cAAc;IAACiD,GAAG,EAAC,gBAAgB;IAAEC,QAAM,EAAE9C,MAAA,CAAA+C,YAAY;IAC/D7C,KAAK,EA5ElBC,eAAA;MAAA6C,aAAA,EA4EqChD,MAAA,CAAAiD,YAAY;IAAA;MACrCvC,mBAAA,WAAc,EACqBV,MAAA,CAAAiD,YAAY,I,cAA/CnD,mBAAA,CAKM;IAnFlBD,GAAA;IA8EiBD,KAAK,EAAC,iBAAiB;IAAsBM,KAAK,EA9EnEC,eAAA;MAAA+C,GAAA,EA8E4ElD,MAAA,CAAAmD,UAAU;IAAA;MACtEhC,YAAA,CAEUC,kBAAA;IAFDxB,KAAK,EAAC,cAAc;IAAEyB,IAAI,EAAE;;IA/ErDG,OAAA,EAAAC,QAAA,CAgFoB,MAAW,CAAXN,YAAA,CAAWnB,MAAA,a;IAhF/B0B,CAAA;kCAkFgBrB,mBAAA,CAAwC;IAAlCT,KAAK,EAAC;EAAc,GAAC,QAAM,qB,oBAlFjDc,mBAAA,gBAoFYA,mBAAA,aAAgB,EACLV,MAAA,CAAAoD,SAAS,I,cAApBtD,mBAAA,CAKM,OALNuD,WAKM,GAJFlC,YAAA,CAEUC,kBAAA;IAFDxB,KAAK,EAAC,cAAc;IAAEyB,IAAI,EAAE;;IAtFrDG,OAAA,EAAAC,QAAA,CAuFoB,MAAW,CAAXN,YAAA,CAAWnB,MAAA,a;IAvF/B0B,CAAA;kCAyFgBrB,mBAAA,CAAsB,cAAhB,WAAS,qB,KAzF/BK,mBAAA,gBA2FuBV,MAAA,CAAAsD,YAAY,I,cAAvBxD,mBAAA,CA6LM,OAxRlByD,WAAA,I,kBA4FgBzD,mBAAA,CA2LMC,SAAA,QAvRtByD,WAAA,CA4FgDxD,MAAA,CAAAyD,kBAAkB,EA5FlE,CA4F6BC,OAAO,EAAEC,KAAK;yBAA3B7D,mBAAA,CA2LM;MA3L+CD,GAAG,EAAE8D;IAAK,IAC3DjD,mBAAA,UAAa,EAEyDgD,OAAO,CAACE,MAAM,iB,cAApF9D,mBAAA,CA8KM;MA7Q1BD,GAAA;MA+F0BD,KAAK,EA/F/BiE,eAAA,uBA+FuD7D,MAAA,CAAA8D,iBAAiB,CAACJ,OAAO;QACxDhD,mBAAA,QAAW,EACXA,mBAAA,+DAAkE,EACxCgD,OAAO,CAACK,OAAO,gBAAgBL,OAAO,CAACK,OAAO,mB,cAAxEjE,mBAAA,CAEM,OAFNkE,WAEM,GADF3D,mBAAA,CAAkD;MAA5C4D,GAAG,EAAEjE,MAAA,CAAAkE,iBAAiB,CAACR,OAAO;MAAGS,GAAG,EAAC;4BAnGvEC,WAAA,E,KAAA1D,mBAAA,gBAsGwBA,mBAAA,UAAa,EAEHgD,OAAO,CAACK,OAAO,gBAAgBL,OAAO,CAACK,OAAO,mB,cADxDjE,mBAAA,CAgJM,OAhJNuE,WAgJM,GA9IFhE,mBAAA,CAKM,OALNiE,WAKM,GAJFjE,mBAAA,CAA8E,QAA9EkE,WAA8E,EAAA3D,gBAAA,CAAjDZ,MAAA,CAAAwE,iBAAiB,CAACC,GAAG,CAACf,OAAO,CAACgB,QAAQ,mBACnErE,mBAAA,CACa;MADPT,KAAK,EA3G3CiE,eAAA,EA2G4C,cAAc,EAAS7D,MAAA,CAAA2E,aAAa,CAACjB,OAAO;wBAAM1D,MAAA,CAAA4E,QAAQ,CAAClB,OAAO,0BAE9ErD,mBAAA,CAAoE,QAApEwE,WAAoE,EAAAjE,gBAAA,CAAtCZ,MAAA,CAAA8E,WAAW,CAACpB,OAAO,CAACqB,OAAO,kB,GAE7DrE,mBAAA,gBAAmB,EACnBA,mBAAA,aAAgB,EAENV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,gB,cAD1C5D,mBAAA,CAGM,OAHNmF,WAGM,GADF5E,mBAAA,CAAwF;MAArF6E,SAAgF,EAAxElF,MAAA,CAAAmF,aAAa,CAACnF,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG/C,OAAO,QAAQtC,MAAA,CAAA8B,UAAU;4BAnHjHwD,WAAA,E,KAAA5E,mBAAA,gBAsH4BA,mBAAA,YAAe,EACgCV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,gB,cAA/E5D,mBAAA,CAUM;MAjIlCD,GAAA;MAuHiCD,KAAK,EAAC,6BAA6B;MACnC0B,OAAK,EAAAU,MAAA,IAAEhC,MAAA,CAAAuF,cAAc,CAACvF,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK;QACnDhF,mBAAA,CAIM,OAJNmF,WAIM,GAHFnF,mBAAA,CAAsE,KAAtEoF,WAAsE,EAAA7E,gBAAA,CAA9CZ,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGK,QAAQ,kBAC/DrF,mBAAA,CACI,KADJsF,WACI,EAAA/E,gBAAA,CADoBZ,MAAA,CAAA4F,cAAc,CAAC5F,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGQ,QAAQ,kB,GAGlFxF,mBAAA,CAEM,OAFNyF,WAEM,GADFzF,mBAAA,CAA2E;MAArE4D,GAAG,EAAEjE,MAAA,CAAA+F,WAAW,CAAC/F,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGW,OAAO;MAAG7B,GAAG,EAAC;4BA/HxG8B,WAAA,E,mBAAAC,WAAA,KAAAxF,mBAAA,gBAmI4BA,mBAAA,aAAgB,EAENV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,iB,cAD1C5D,mBAAA,CAGM;MAvIlCD,GAAA;MAoIiCD,KAAK,EAAC,8BAA8B;MACiB0B,OAAK,EAAAU,MAAA,IAAEhC,MAAA,CAAAmG,gBAAgB,CAACzC,OAAO;QACrFrD,mBAAA,CAA0C;MAApC4D,GAAG,EAAEjE,MAAA,CAAAoG,WAAW,CAAC1C,OAAO;MAAGS,GAAG,EAAC;4BAtIrEkC,WAAA,E,iBAAAC,WAAA,KAAA5F,mBAAA,gBAyI4BA,mBAAA,2DAA8D,EAE9DA,mBAAA,YAAe,EAELV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,iB,cAD1C5D,mBAAA,CAqCM,OArCNyG,WAqCM,GAnCFlG,mBAAA,CAkCM,OAlCNmG,WAkCM,GAjCFnG,mBAAA,CAYM,OAZNoG,WAYM,GAXFtF,YAAA,CAOYsB,oBAAA;MANPiE,IAAI,EAAE1G,MAAA,CAAA2G,gBAAgB,CAACjD,OAAO,CAAC2B,KAAK;MACrCuB,MAAM,EAAN,EAAM;MACNvF,IAAI,EAAC,OAAO;MACXC,OAAK,EAAAU,MAAA,IAAEhC,MAAA,CAAA6G,eAAe,CAACnD,OAAO,CAAC2B,KAAK,EAAErF,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK;MAClEyB,OAAO,EAAE9G,MAAA,CAAA+G,WAAW,CAACrD,OAAO,CAAC2B,KAAK,GAAGyB,OAAO;MAC7ClH,KAAK,EAAC;6DAEVS,mBAAA,CAEO,QAFP2G,WAEO,EAAApG,gBAAA,CADAZ,MAAA,CAAAiH,mBAAmB,CAACjH,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG6B,UAAU,kB,GAGxE7G,mBAAA,CAQM,OARN8G,WAQM,GAPFzG,mBAAA,eAAkB,EAClBL,mBAAA,CAKM,OALN+G,WAKM,GAJF/G,mBAAA,CAEM,OAFNgH,WAEM,I,cADFvH,mBAAA,CAAgDC,SAAA,QAhKhGyD,WAAA,CAgK6E,EAAE,EAAP8D,CAAC;aAAzBjH,mBAAA,CAAgD;QAA3CT,KAAK,EAAC,KAAK;QAAkBC,GAAG,EAAEyH;;kEAE3CjH,mBAAA,CAAoC;MAA9BT,KAAK,EAAC;IAAY,GAAC,MAAI,qB,KAGrCc,mBAAA,sBAAyB,EACzBL,mBAAA,CASQ;MA/K5CkH,OAAA;MAuKyC1E,GAAG,EAAE2E,EAAE,IAAIxH,MAAA,CAAAyH,WAAW,CAAC/D,OAAO,CAAC2B,KAAK,EAAEmC,EAAE;MACxCvD,GAAG,EAAEjE,MAAA,CAAA0H,SAAS,CAAC1H,MAAA,CAAA2H,0BAA0B,CAACjE,OAAO;MACjDkE,MAAI,EAAA5F,MAAA,IAAEhC,MAAA,CAAA6H,eAAe,CAACnE,OAAO,CAAC2B,KAAK;MACnCyC,OAAK,EAAA9F,MAAA,IAAEhC,MAAA,CAAA+H,gBAAgB,CAACrE,OAAO,CAAC2B,KAAK;MACrC2C,OAAK,EAAAhG,MAAA,IAAEhC,MAAA,CAAAiI,gBAAgB,CAACvE,OAAO,CAAC2B,KAAK;MACrC6C,OAAK,EAAAlG,MAAA,IAAEhC,MAAA,CAAAmI,gBAAgB,CAACzE,OAAO,CAAC2B,KAAK;MACrC+C,gBAAc,EAAApG,MAAA,IAAEhC,MAAA,CAAAqI,iBAAiB,CAAC3E,OAAO,CAAC2B,KAAK;MAChDnF,KAAsB,EAAtB;QAAA;MAAA;6CA9KxCoI,WAAA,E,OAAA5H,mBAAA,gBAmL4BA,mBAAA,aAAgB,EAENV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,iB,cAD1C5D,mBAAA,CAWM,OAXNyI,WAWM,GATFlI,mBAAA,CAQM,OARNmI,WAQM,GAPFnI,mBAAA,CAMQ;MANDoI,QAAQ,EAAR,EAAQ;MAAC7I,KAAK,EAAC,cAAc;MAC/BqE,GAAG,EAAEjE,MAAA,CAAA0I,WAAW,CAAChF,OAAO;MACxBkE,MAAI,EAAA5F,MAAA,IAAEhC,MAAA,CAAA2I,eAAe,CAACjF,OAAO;MAAIoE,OAAK,EAAA9F,MAAA,IAAEhC,MAAA,CAAA4I,gBAAgB,CAAClF,OAAO;MAChEsE,OAAK,EAAAhG,MAAA,IAAEhC,MAAA,CAAA6I,gBAAgB,CAACnF,OAAO;MAAIwE,OAAK,EAAAlG,MAAA,IAAEhC,MAAA,CAAA8I,gBAAgB,CAACpF,OAAO;MA1L3G6D,OAAA;MA2LyC1E,GAAG,EAAE2E,EAAE,IAAIxH,MAAA,CAAA+I,WAAW,CAACrF,OAAO,CAAC2B,KAAK,EAAEmC,EAAE;yCACzCnH,mBAAA,CAAyB,cAAnB,cAAY,oB,qCA5L1D2I,WAAA,E,OAAAtI,mBAAA,gBAiM4BA,mBAAA,eAAkB,EAERV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,gB,cAD1C5D,mBAAA,CAyBM,OAzBNmJ,WAyBM,GAvBF5I,mBAAA,CAsBM,OAtBN6I,WAsBM,GArBF7I,mBAAA,CAiBM,OAjBN8I,WAiBM,GAhBF9I,mBAAA,CAUM,OAVN+I,WAUM,GATF/I,mBAAA,CAEM,OAFNgJ,WAEM,GADFhJ,mBAAA,CAA0D,cAAAO,gBAAA,CAAjDZ,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGiE,QAAQ,iB,GAEpDjJ,mBAAA,CAEM,OAFNkJ,WAEM,GADFlJ,mBAAA,CAAuE,cAAAO,gBAAA,CAA9DZ,MAAA,CAAAwJ,aAAa,CAACxJ,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGoE,MAAM,kB,GAEhEpJ,mBAAA,CAEM,OAFNqJ,WAEM,GADFrJ,mBAAA,CAA0D,cAAAO,gBAAA,CAAjDZ,MAAA,CAAAwE,iBAAiB,CAACC,GAAG,CAACf,OAAO,CAACgB,QAAQ,kB,KAGvDrE,mBAAA,CAIM,OAJNsJ,WAIM,GAHFtJ,mBAAA,CAEM,OAFNuJ,WAEM,GADFvJ,mBAAA,CAAkD;MAA5C4D,GAAG,EAAEjE,MAAA,CAAAkE,iBAAiB,CAACR,OAAO;MAAGS,GAAG,EAAC;4BAnN3F0F,WAAA,E,mCAuNoCxJ,mBAAA,CAEM;MAFDT,KAAK,EAAC;IAAQ,IACfS,mBAAA,CAAiB,cAAX,MAAI,E,4BAxNlDK,mBAAA,gBA6N4BA,mBAAA,UAAa,EAEHV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,gB,cAD1C5D,mBAAA,CAuBM,OAvBNgK,WAuBM,GArBFzJ,mBAAA,CAoBM;MApBDT,KAAK,EAAC,qBAAqB;MAC3B0B,OAAK,EAAAU,MAAA,IAAEhC,MAAA,CAAA+J,eAAe,CAAC/J,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG2E,QAAQ;QAC/D3J,mBAAA,CAGM,OAHN4J,WAGM,GAFF5J,mBAAA,CACuB;MADjB4D,GAAG,EAAEjE,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG6E,SAAS;MAAE/F,GAAG,EAAC,MAAM;MAC3DvE,KAAK,EAAC;4BApOlDuK,WAAA,E,GAsOoC9J,mBAAA,CAaM,OAbN+J,WAaM,GAZF/J,mBAAA,CAEM;MAFDT,KAAK,EAvOlDiE,eAAA,EAuOmD,YAAY,EAASwG,IAAA,CAAAC,gBAAgB;wBACzCtK,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGkF,KAAK,yBAE3ClK,mBAAA,CAEM,OAFNmK,WAEM,EAAA5J,gBAAA,CADCZ,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAGoF,WAAW,kBAEjDpK,mBAAA,CAKM,OALNqK,WAKM,GAJFrK,mBAAA,CAEa,QAFbsK,WAEa,EAAA/J,gBAAA,CADTZ,MAAA,CAAA4K,WAAW,CAAC5K,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG2E,QAAQ,mB,4BAEvD3J,mBAAA,CAAyC;MAAnCT,KAAK,EAAC;IAAoB,GAAC,GAAC,qB,qBAjP9EiL,WAAA,E,KAAAnK,mBAAA,e,KAAAA,mBAAA,gBAyPwDgD,OAAO,CAACK,OAAO,iB,cAA/CjE,mBAAA,CAKM,OALNgL,WAKM,GAJF3J,YAAA,CAEUC,kBAAA;MA5PtCI,OAAA,EAAAC,QAAA,CA2PgC,MAAe,CAAfN,YAAA,CAAenB,MAAA,iB;MA3P/C0B,CAAA;oCA6P4BrB,mBAAA,CAAiB,cAAX,MAAI,qB,KA7PtCK,mBAAA,gBAgQwBA,mBAAA,cAAiB,EACQV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,iB,cAAzD5D,mBAAA,CAGM,OAHNiL,WAGM,GAFF1K,mBAAA,CAA0F,OAA1F2K,WAA0F,EAAApK,gBAAA,CAA/DZ,MAAA,CAAA8E,WAAW,CAAC9E,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG4F,UAAU,mB,4BAChF5K,mBAAA,CAAe,WAAZ,UAAQ,qB,KAnQvCK,mBAAA,gBAsQwBA,mBAAA,eAAkB,EACUV,MAAA,CAAAgF,wBAAwB,CAACtB,OAAO,oB,cAA5D5D,mBAAA,CAIM,OAJNoL,WAIM,GAHF7K,mBAAA,CACM,OADN8K,WACM,EAAAvK,gBAAA,CADwBZ,MAAA,CAAA8E,WAAW,CAAC9E,MAAA,CAAAoF,cAAc,CAAC1B,OAAO,CAAC2B,KAAK,GAAG+F,aAAa,mB,4BAEtF/K,mBAAA,CAAgB,WAAb,WAAS,qB,KA1QxCK,mBAAA,e,oBAAAA,mBAAA,gBA+QoBA,mBAAA,YAAe,EACiBgD,OAAO,CAACE,MAAM,iB,cAA9C9D,mBAAA,CAGM,OAHNuL,WAGM,GAFFhL,mBAAA,CAA8D,OAA9DiL,WAA8D,EAAA1K,gBAAA,CAAlCZ,MAAA,CAAA8E,WAAW,CAACpB,OAAO,CAAC6H,IAAI,mBACpDlL,mBAAA,CAA+B,WAAAO,gBAAA,CAAzB8C,OAAO,CAAC8H,IAAI,IAAG,QAAM,gB,KAlRnD9K,mBAAA,e;sCAAAA,mBAAA,e,sDAAAA,mBAAA,gB,CA6RmEV,MAAA,CAAAC,gBAAgB,I,cAA/EH,mBAAA,CAGM;IAhSVD,GAAA;IA6RSD,KAAK,EAAC,oBAAoB;IAAEM,KAAK,EA7R1CC,eAAA,CA6R4CH,MAAA,CAAAI,cAAc;kCAClDC,mBAAA,CAAqC;IAAjCT,KAAK,EAAC;EAAY,GAAC,WAAS,qBAChCS,mBAAA,CAAiD;IAA5C4D,GAA4B,EAA5BtE,UAA4B;IAACwE,GAAG,EAAC;kDA/R9CzD,mBAAA,gBAiSIA,mBAAA,gBAAmB,EACnBS,YAAA,CACuFnB,MAAA;IAD9DyL,OAAO,EAAEzL,MAAA,CAAA0L,iBAAiB;IAlSvD,oBAAA3J,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkSsChC,MAAA,CAAA0L,iBAAiB,GAAA1J,MAAA;IAAGuI,KAAK,EAAEvK,MAAA,CAAA2L,iBAAiB,EAAEpB,KAAK;IAChF,cAAY,EAAEvK,MAAA,CAAA2L,iBAAiB,EAAEC,IAAI;IAASC,OAAK,EAAA9J,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEhC,MAAA,CAAA0L,iBAAiB;iEAE3EhL,mBAAA,eAAkB,EACoBV,MAAA,CAAA8L,mBAAmB,I,cAAzDhM,mBAAA,CASM;IA/SVD,GAAA;IAsSSD,KAAK,EAAC,oBAAoB;IAA6B0B,OAAK,EAAEtB,MAAA,CAAA+L;MAC/D1L,mBAAA,CAOM;IAPDT,KAAK,EAAC,yBAAyB;IAAE0B,OAAK,EAAAS,MAAA,QAAAA,MAAA,MAvSnDiK,cAAA,CAuS6C,QAAW;IAAEC,OAAK,EAvS/DD,cAAA,CAuSyEhM,MAAA,CAAAkM,WAAW;MACxE7L,mBAAA,CAK2B;IALrB4D,GAAG,EAAEjE,MAAA,CAAAmM,eAAe;IAAEhI,GAAG,EAAC,MAAM;IAACvE,KAAK,EAAC,eAAe;IAAEM,KAAK,EAxS/EC,eAAA;gBAwS6GH,MAAA,CAAAoM,SAAS,GAAGpM,MAAA,CAAAqM,KAAK;iBAAiCrM,MAAA,CAAAsM,UAAU,GAAGtM,MAAA,CAAAqM,KAAK;;;;IAKjKE,MAAI,EAAEvM,MAAA,CAAAwM;kDA7StBC,WAAA,E,gCAAA/L,mBAAA,gBAiTIA,mBAAA,YAAe,EACfS,YAAA,CAoBYuL,oBAAA;IAtUhB7K,UAAA,EAkTwB7B,MAAA,CAAA2M,iBAAiB;IAlTzC,uBAAA5K,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAkTwBhC,MAAA,CAAA2M,iBAAiB,GAAA3K,MAAA;IAAEuI,KAAK,EAAC,MAAM;IAACqC,KAAK,EAAC,KAAK;IAAE,YAAU,EAAE,IAAI;IAAG,sBAAoB,EAAE,IAAI;IACzG,uBAAqB,EAAE,IAAI;IAAEhN,KAAK,EAAC;;IAnT5C4B,OAAA,EAAAC,QAAA,CAoTQ,MAiBM,CAjBNpB,mBAAA,CAiBM,OAjBNwM,WAiBM,GAhBFxM,mBAAA,CAMM,OANNyM,WAMM,GALFzM,mBAAA,CAA2E;MAArE4D,GAAG,EAAEjE,MAAA,CAAA+F,WAAW,CAAC/F,MAAA,CAAA+M,WAAW,EAAE/G,OAAO;MAAG7B,GAAG,EAAC,MAAM;MAACvE,KAAK,EAAC;4BAtT/EoN,WAAA,GAuTgB3M,mBAAA,CAGM,OAHN4M,WAGM,GAFF5M,mBAAA,CAAoD,KAApD6M,WAAoD,EAAAtM,gBAAA,CAA5BZ,MAAA,CAAA+M,WAAW,EAAErH,QAAQ,kBAC7CrF,mBAAA,CAAoE,KAApE8M,WAAoE,EAAAvM,gBAAA,CAA5CZ,MAAA,CAAA4F,cAAc,CAAC5F,MAAA,CAAA+M,WAAW,EAAElH,QAAQ,kB,KAGpE1E,YAAA,CACmCiM,mBAAA;MADzB7C,KAAK,EAAC,4BAA4B;MAAC/J,IAAI,EAAC,MAAM;MAAE6M,QAAQ,EAAE,KAAK;MAAE,WAAS,EAAT,EAAS;MAChFnN,KAA4B,EAA5B;QAAA;MAAA;QACJG,mBAAA,CAMM,OANNiN,WAMM,GALFnM,YAAA,CAGYsB,oBAAA;MAHDjC,IAAI,EAAC,SAAS;MAAEc,OAAK,EAAEtB,MAAA,CAAAuN,WAAW;MACxCC,QAAQ,2BAA2BC,QAAQ,CAACzN,MAAA,CAAA+M,WAAW,EAAE/G,OAAO,EAAE0H,WAAW;;MAhUlGlM,OAAA,EAAAC,QAAA,CAgUuG,MAEvFM,MAAA,SAAAA,MAAA,QAlUhBY,gBAAA,CAgUuG,MAEvF,E;MAlUhBjB,CAAA;qCAmUgBP,YAAA,CAA8DsB,oBAAA;MAAnDjC,IAAI,EAAC,SAAS;MAAEc,OAAK,EAAEtB,MAAA,CAAA2N;;MAnUlDnM,OAAA,EAAAC,QAAA,CAmUgE,MAAEM,MAAA,SAAAA,MAAA,QAnUlEY,gBAAA,CAmUgE,IAAE,E;MAnUlEjB,CAAA;;IAAAA,CAAA;qCAwUIhB,mBAAA,YAAe,EACfS,YAAA,CAaYuL,oBAAA;IAtVhB7K,UAAA,EAyUwB7B,MAAA,CAAA4N,oBAAoB;IAzU5C,uBAAA7L,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAyUwBhC,MAAA,CAAA4N,oBAAoB,GAAA5L,MAAA;IAAGuI,KAAK,EAAEvK,MAAA,CAAA+M,WAAW,EAAErH,QAAQ;IAAEkH,KAAK,EAAC,KAAK;IAAE,YAAU,EAAE,IAAI;IACjG,sBAAoB,EAAE,KAAK;IAAG,uBAAqB,EAAE,IAAI;IAAEhN,KAAK,EAAC;;IA1U1E4B,OAAA,EAAAC,QAAA,CA2UQ,MAUM,CAVNpB,mBAAA,CAUM,OAVNwN,WAUM,GATFnN,mBAAA,cAAiB,EACMV,MAAA,CAAA+M,WAAW,EAAE/G,OAAO,EAAE0H,WAAW,iB,cAAxDI,YAAA,CACiE9N,MAAA;MA9U7EH,GAAA;MA6UoFoE,GAAG,EAAEjE,MAAA,CAAA+N,cAAc;MACtFC,UAAQ,EAAEhO,MAAA,CAAAiO,kBAAkB;MAAG/F,OAAK,EAAElI,MAAA,CAAAkO;wCA9UvDxN,mBAAA,gBA+UYA,mBAAA,eAAkB,EACMV,MAAA,CAAA+M,WAAW,EAAE/G,OAAO,EAAE0H,WAAW,iB,cAAzDI,YAAA,CACkE9N,MAAA;MAjV9EH,GAAA;MAgVqFoE,GAAG,EAAEjE,MAAA,CAAA+N,cAAc;MACvFC,UAAQ,EAAEhO,MAAA,CAAAmO,mBAAmB;MAAGjG,OAAK,EAAElI,MAAA,CAAAkO;wCAjVxDxN,mBAAA,gBAkVYA,mBAAA,aAAgB,EACMV,MAAA,CAAA+M,WAAW,EAAE/G,OAAO,EAAE0H,WAAW,gB,cAAvDI,YAAA,CACgE9N,MAAA;MApV5EH,GAAA;MAmVkFoE,GAAG,EAAEjE,MAAA,CAAA+N,cAAc;MACpFC,UAAQ,EAAEhO,MAAA,CAAAoO,iBAAiB;MAAGlG,OAAK,EAAElI,MAAA,CAAAkO;wCApVtDxN,mBAAA,e;IAAAgB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}