{"ast": null, "code": "const useTimePanel = ({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds\n}) => {\n  const getAvailableTime = (date, role, first, compareDate) => {\n    const availableTimeGetters = {\n      hour: getAvailableHours,\n      minute: getAvailableMinutes,\n      second: getAvailableSeconds\n    };\n    let result = date;\n    [\"hour\", \"minute\", \"second\"].forEach(type => {\n      if (availableTimeGetters[type]) {\n        let availableTimeSlots;\n        const method = availableTimeGetters[type];\n        switch (type) {\n          case \"minute\":\n            {\n              availableTimeSlots = method(result.hour(), role, compareDate);\n              break;\n            }\n          case \"second\":\n            {\n              availableTimeSlots = method(result.hour(), result.minute(), role, compareDate);\n              break;\n            }\n          default:\n            {\n              availableTimeSlots = method(role, compareDate);\n              break;\n            }\n        }\n        if ((availableTimeSlots == null ? void 0 : availableTimeSlots.length) && !availableTimeSlots.includes(result[type]())) {\n          const pos = first ? 0 : availableTimeSlots.length - 1;\n          result = result[type](availableTimeSlots[pos]);\n        }\n      }\n    });\n    return result;\n  };\n  const timePickerOptions = {};\n  const onSetOption = ([key, val]) => {\n    timePickerOptions[key] = val;\n  };\n  return {\n    timePickerOptions,\n    getAvailableTime,\n    onSetOption\n  };\n};\nexport { useTimePanel };", "map": {"version": 3, "names": ["useTimePanel", "getAvailableHours", "getAvailableMinutes", "getAvailableSeconds", "getAvailableTime", "date", "role", "first", "compareDate", "availableTimeGetters", "hour", "minute", "second", "result", "for<PERSON>ach", "type", "availableTimeSlots", "method", "length", "includes", "pos", "timePickerOptions", "onSetOption", "key", "val"], "sources": ["../../../../../../../packages/components/time-picker/src/composables/use-time-panel.ts"], "sourcesContent": ["import type { Dayjs } from 'dayjs'\n\nimport type {\n  GetDisabledHours,\n  GetDisabledMinutes,\n  GetDisabledSeconds,\n} from '../common/props'\n\ntype UseTimePanelProps = {\n  getAvailableHours: GetDisabledHours\n  getAvailableMinutes: GetDisabledMinutes\n  getAvailableSeconds: GetDisabledSeconds\n}\n\nexport const useTimePanel = ({\n  getAvailableHours,\n  getAvailableMinutes,\n  getAvailableSeconds,\n}: UseTimePanelProps) => {\n  const getAvailableTime = (\n    date: Dayjs,\n    role: string,\n    first: boolean,\n    compareDate?: Dayjs\n  ) => {\n    const availableTimeGetters = {\n      hour: getAvailableHours,\n      minute: getAvailableMinutes,\n      second: getAvailableSeconds,\n    } as const\n    let result = date\n    ;(['hour', 'minute', 'second'] as const).forEach((type) => {\n      if (availableTimeGetters[type]) {\n        let availableTimeSlots: number[]\n        const method = availableTimeGetters[type]\n        switch (type) {\n          case 'minute': {\n            availableTimeSlots = (method as typeof getAvailableMinutes)(\n              result.hour(),\n              role,\n              compareDate\n            )\n            break\n          }\n          case 'second': {\n            availableTimeSlots = (method as typeof getAvailableSeconds)(\n              result.hour(),\n              result.minute(),\n              role,\n              compareDate\n            )\n            break\n          }\n          default: {\n            availableTimeSlots = (method as typeof getAvailableHours)(\n              role,\n              compareDate\n            )\n            break\n          }\n        }\n\n        if (\n          availableTimeSlots?.length &&\n          !availableTimeSlots.includes(result[type]())\n        ) {\n          const pos = first ? 0 : availableTimeSlots.length - 1\n          result = result[type](availableTimeSlots[pos]) as unknown as Dayjs\n        }\n      }\n    })\n    return result\n  }\n\n  const timePickerOptions: Record<string, (...args: any[]) => void> = {}\n\n  const onSetOption = ([key, val]: [string, (...args: any[]) => void]) => {\n    timePickerOptions[key] = val\n  }\n\n  return {\n    timePickerOptions,\n\n    getAvailableTime,\n    onSetOption,\n  }\n}\n"], "mappings": "AAAY,MAACA,YAAY,GAAGA,CAAC;EAC3BC,iBAAiB;EACjBC,mBAAmB;EACnBC;AACF,CAAC,KAAK;EACJ,MAAMC,gBAAgB,GAAGA,CAACC,IAAI,EAAEC,IAAI,EAAEC,KAAK,EAAEC,WAAW,KAAK;IAC3D,MAAMC,oBAAoB,GAAG;MAC3BC,IAAI,EAAET,iBAAiB;MACvBU,MAAM,EAAET,mBAAmB;MAC3BU,MAAM,EAAET;IACd,CAAK;IACD,IAAIU,MAAM,GAAGR,IAAI;IACjB,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACS,OAAO,CAAEC,IAAI,IAAK;MAC7C,IAAIN,oBAAoB,CAACM,IAAI,CAAC,EAAE;QAC9B,IAAIC,kBAAkB;QACtB,MAAMC,MAAM,GAAGR,oBAAoB,CAACM,IAAI,CAAC;QACzC,QAAQA,IAAI;UACV,KAAK,QAAQ;YAAE;cACbC,kBAAkB,GAAGC,MAAM,CAACJ,MAAM,CAACH,IAAI,EAAE,EAAEJ,IAAI,EAAEE,WAAW,CAAC;cAC7D;YACZ;UACU,KAAK,QAAQ;YAAE;cACbQ,kBAAkB,GAAGC,MAAM,CAACJ,MAAM,CAACH,IAAI,EAAE,EAAEG,MAAM,CAACF,MAAM,EAAE,EAAEL,IAAI,EAAEE,WAAW,CAAC;cAC9E;YACZ;UACU;YAAS;cACPQ,kBAAkB,GAAGC,MAAM,CAACX,IAAI,EAAEE,WAAW,CAAC;cAC9C;YACZ;QACA;QACQ,IAAI,CAACQ,kBAAkB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,MAAM,KAAK,CAACF,kBAAkB,CAACG,QAAQ,CAACN,MAAM,CAACE,IAAI,CAAC,EAAE,CAAC,EAAE;UACrH,MAAMK,GAAG,GAAGb,KAAK,GAAG,CAAC,GAAGS,kBAAkB,CAACE,MAAM,GAAG,CAAC;UACrDL,MAAM,GAAGA,MAAM,CAACE,IAAI,CAAC,CAACC,kBAAkB,CAACI,GAAG,CAAC,CAAC;QACxD;MACA;IACA,CAAK,CAAC;IACF,OAAOP,MAAM;EACjB,CAAG;EACD,MAAMQ,iBAAiB,GAAG,EAAE;EAC5B,MAAMC,WAAW,GAAGA,CAAC,CAACC,GAAG,EAAEC,GAAG,CAAC,KAAK;IAClCH,iBAAiB,CAACE,GAAG,CAAC,GAAGC,GAAG;EAChC,CAAG;EACD,OAAO;IACLH,iBAAiB;IACjBjB,gBAAgB;IAChBkB;EACJ,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}