{"ast": null, "code": "import baseUniq from './_baseUniq.js';\n\n/**\n * Creates a duplicate-free version of an array, using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons, in which only the first occurrence of each element\n * is kept. The order of result values is determined by the order they occur\n * in the array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.uniq([2, 1, 2]);\n * // => [2, 1]\n */\nfunction uniq(array) {\n  return array && array.length ? baseUniq(array) : [];\n}\nexport default uniq;", "map": {"version": 3, "names": ["baseUniq", "uniq", "array", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/uniq.js"], "sourcesContent": ["import baseUniq from './_baseUniq.js';\n\n/**\n * Creates a duplicate-free version of an array, using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons, in which only the first occurrence of each element\n * is kept. The order of result values is determined by the order they occur\n * in the array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @returns {Array} Returns the new duplicate free array.\n * @example\n *\n * _.uniq([2, 1, 2]);\n * // => [2, 1]\n */\nfunction uniq(array) {\n  return (array && array.length) ? baseUniq(array) : [];\n}\n\nexport default uniq;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gBAAgB;;AAErC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,OAAQA,KAAK,IAAIA,KAAK,CAACC,MAAM,GAAIH,QAAQ,CAACE,KAAK,CAAC,GAAG,EAAE;AACvD;AAEA,eAAeD,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}