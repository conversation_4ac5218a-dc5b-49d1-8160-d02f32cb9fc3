{"ast": null, "code": "import toString from './toString.js';\n\n/**\n * Replaces matches for `pattern` in `string` with `replacement`.\n *\n * **Note:** This method is based on\n * [`String#replace`](https://mdn.io/String/replace).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to modify.\n * @param {RegExp|string} pattern The pattern to replace.\n * @param {Function|string} replacement The match replacement.\n * @returns {string} Returns the modified string.\n * @example\n *\n * _.replace('<PERSON> <PERSON>', '<PERSON>', '<PERSON>');\n * // => 'Hi Barney'\n */\nfunction replace() {\n  var args = arguments,\n    string = toString(args[0]);\n  return args.length < 3 ? string : string.replace(args[1], args[2]);\n}\nexport default replace;", "map": {"version": 3, "names": ["toString", "replace", "args", "arguments", "string", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/replace.js"], "sourcesContent": ["import toString from './toString.js';\n\n/**\n * Replaces matches for `pattern` in `string` with `replacement`.\n *\n * **Note:** This method is based on\n * [`String#replace`](https://mdn.io/String/replace).\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category String\n * @param {string} [string=''] The string to modify.\n * @param {RegExp|string} pattern The pattern to replace.\n * @param {Function|string} replacement The match replacement.\n * @returns {string} Returns the modified string.\n * @example\n *\n * _.replace('<PERSON> <PERSON>', '<PERSON>', '<PERSON>');\n * // => 'Hi Barney'\n */\nfunction replace() {\n  var args = arguments,\n      string = toString(args[0]);\n\n  return args.length < 3 ? string : string.replace(args[1], args[2]);\n}\n\nexport default replace;\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,eAAe;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAAA,EAAG;EACjB,IAAIC,IAAI,GAAGC,SAAS;IAChBC,MAAM,GAAGJ,QAAQ,CAACE,IAAI,CAAC,CAAC,CAAC,CAAC;EAE9B,OAAOA,IAAI,CAACG,MAAM,GAAG,CAAC,GAAGD,MAAM,GAAGA,MAAM,CAACH,OAAO,CAACC,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC;AACpE;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}