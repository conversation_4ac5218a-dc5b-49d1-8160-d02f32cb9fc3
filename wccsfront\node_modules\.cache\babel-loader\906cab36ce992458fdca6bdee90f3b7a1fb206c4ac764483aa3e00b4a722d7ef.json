{"ast": null, "code": "import baseIteratee from './_baseIteratee.js';\nimport basePullAll from './_basePullAll.js';\n\n/**\n * This method is like `_.pullAll` except that it accepts `iteratee` which is\n * invoked for each element of `array` and `values` to generate the criterion\n * by which they're compared. The iteratee is invoked with one argument: (value).\n *\n * **Note:** Unlike `_.differenceBy`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [{ 'x': 1 }, { 'x': 2 }, { 'x': 3 }, { 'x': 1 }];\n *\n * _.pullAllBy(array, [{ 'x': 1 }, { 'x': 3 }], 'x');\n * console.log(array);\n * // => [{ 'x': 2 }]\n */\nfunction pullAllBy(array, values, iteratee) {\n  return array && array.length && values && values.length ? basePullAll(array, values, baseIteratee(iteratee, 2)) : array;\n}\nexport default pullAllBy;", "map": {"version": 3, "names": ["baseIteratee", "basePullAll", "pullAllBy", "array", "values", "iteratee", "length"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/pullAllBy.js"], "sourcesContent": ["import baseIteratee from './_baseIteratee.js';\nimport basePullAll from './_basePullAll.js';\n\n/**\n * This method is like `_.pullAll` except that it accepts `iteratee` which is\n * invoked for each element of `array` and `values` to generate the criterion\n * by which they're compared. The iteratee is invoked with one argument: (value).\n *\n * **Note:** Unlike `_.differenceBy`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [iteratee=_.identity] The iteratee invoked per element.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = [{ 'x': 1 }, { 'x': 2 }, { 'x': 3 }, { 'x': 1 }];\n *\n * _.pullAllBy(array, [{ 'x': 1 }, { 'x': 3 }], 'x');\n * console.log(array);\n * // => [{ 'x': 2 }]\n */\nfunction pullAllBy(array, values, iteratee) {\n  return (array && array.length && values && values.length)\n    ? basePullAll(array, values, baseIteratee(iteratee, 2))\n    : array;\n}\n\nexport default pullAllBy;\n"], "mappings": "AAAA,OAAOA,YAAY,MAAM,oBAAoB;AAC7C,OAAOC,WAAW,MAAM,mBAAmB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EAC1C,OAAQF,KAAK,IAAIA,KAAK,CAACG,MAAM,IAAIF,MAAM,IAAIA,MAAM,CAACE,MAAM,GACpDL,WAAW,CAACE,KAAK,EAAEC,MAAM,EAAEJ,YAAY,CAACK,QAAQ,EAAE,CAAC,CAAC,CAAC,GACrDF,KAAK;AACX;AAEA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}