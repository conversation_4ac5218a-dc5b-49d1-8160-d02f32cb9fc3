{"ast": null, "code": "import baseNth from './_baseNth.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Gets the element at index `n` of `array`. If `n` is negative, the nth\n * element from the end is returned.\n *\n * @static\n * @memberOf _\n * @since 4.11.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=0] The index of the element to return.\n * @returns {*} Returns the nth element of `array`.\n * @example\n *\n * var array = ['a', 'b', 'c', 'd'];\n *\n * _.nth(array, 1);\n * // => 'b'\n *\n * _.nth(array, -2);\n * // => 'c';\n */\nfunction nth(array, n) {\n  return array && array.length ? baseNth(array, toInteger(n)) : undefined;\n}\nexport default nth;", "map": {"version": 3, "names": ["baseNth", "toInteger", "nth", "array", "n", "length", "undefined"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/nth.js"], "sourcesContent": ["import baseNth from './_baseNth.js';\nimport toInteger from './toInteger.js';\n\n/**\n * Gets the element at index `n` of `array`. If `n` is negative, the nth\n * element from the end is returned.\n *\n * @static\n * @memberOf _\n * @since 4.11.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=0] The index of the element to return.\n * @returns {*} Returns the nth element of `array`.\n * @example\n *\n * var array = ['a', 'b', 'c', 'd'];\n *\n * _.nth(array, 1);\n * // => 'b'\n *\n * _.nth(array, -2);\n * // => 'c';\n */\nfunction nth(array, n) {\n  return (array && array.length) ? baseNth(array, toInteger(n)) : undefined;\n}\n\nexport default nth;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,eAAe;AACnC,OAAOC,SAAS,MAAM,gBAAgB;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAGA,CAACC,KAAK,EAAEC,CAAC,EAAE;EACrB,OAAQD,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAIL,OAAO,CAACG,KAAK,EAAEF,SAAS,CAACG,CAAC,CAAC,CAAC,GAAGE,SAAS;AAC3E;AAEA,eAAeJ,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}