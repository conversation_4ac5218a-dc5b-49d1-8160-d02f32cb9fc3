{"ast": null, "code": "import eq from './eq.js';\n\n/**\n * The base implementation of `_.sortedUniq` and `_.sortedUniqBy` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\nfunction baseSortedUniq(array, iteratee) {\n  var index = -1,\n    length = array.length,\n    resIndex = 0,\n    result = [];\n  while (++index < length) {\n    var value = array[index],\n      computed = iteratee ? iteratee(value) : value;\n    if (!index || !eq(computed, seen)) {\n      var seen = computed;\n      result[resIndex++] = value === 0 ? 0 : value;\n    }\n  }\n  return result;\n}\nexport default baseSortedUniq;", "map": {"version": 3, "names": ["eq", "baseSortedUniq", "array", "iteratee", "index", "length", "resIndex", "result", "value", "computed", "seen"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_baseSortedUniq.js"], "sourcesContent": ["import eq from './eq.js';\n\n/**\n * The base implementation of `_.sortedUniq` and `_.sortedUniqBy` without\n * support for iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @returns {Array} Returns the new duplicate free array.\n */\nfunction baseSortedUniq(array, iteratee) {\n  var index = -1,\n      length = array.length,\n      resIndex = 0,\n      result = [];\n\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    if (!index || !eq(computed, seen)) {\n      var seen = computed;\n      result[resIndex++] = value === 0 ? 0 : value;\n    }\n  }\n  return result;\n}\n\nexport default baseSortedUniq;\n"], "mappings": "AAAA,OAAOA,EAAE,MAAM,SAAS;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EACvC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,CAACG,MAAM;IACrBC,QAAQ,GAAG,CAAC;IACZC,MAAM,GAAG,EAAE;EAEf,OAAO,EAAEH,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIG,KAAK,GAAGN,KAAK,CAACE,KAAK,CAAC;MACpBK,QAAQ,GAAGN,QAAQ,GAAGA,QAAQ,CAACK,KAAK,CAAC,GAAGA,KAAK;IAEjD,IAAI,CAACJ,KAAK,IAAI,CAACJ,EAAE,CAACS,QAAQ,EAAEC,IAAI,CAAC,EAAE;MACjC,IAAIA,IAAI,GAAGD,QAAQ;MACnBF,MAAM,CAACD,QAAQ,EAAE,CAAC,GAAGE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK;IAC9C;EACF;EACA,OAAOD,MAAM;AACf;AAEA,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}