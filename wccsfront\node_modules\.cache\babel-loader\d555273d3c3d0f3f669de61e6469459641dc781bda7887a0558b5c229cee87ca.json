{"ast": null, "code": "import { createVNode } from 'vue';\nconst Footer = (props, {\n  slots\n}) => {\n  var _a;\n  return createVNode(\"div\", {\n    \"class\": props.class,\n    \"style\": props.style\n  }, [(_a = slots.default) == null ? void 0 : _a.call(slots)]);\n};\nFooter.displayName = \"ElTableV2Footer\";\nvar Footer$1 = Footer;\nexport { Footer$1 as default };", "map": {"version": 3, "names": ["slots", "_a", "createVNode", "props", "class", "style", "default", "call", "Footer$1", "Footer"], "sources": ["../../../../../../../packages/components/table-v2/src/renderers/footer.tsx"], "sourcesContent": ["import type { CSSProperties, FunctionalComponent } from 'vue'\n\ntype FooterRendererProps = {\n  class?: JSX.IntrinsicAttributes['class']\n  style: CSSProperties\n}\n\nconst Footer: FunctionalComponent<FooterRendererProps> = (props, { slots }) => {\n  return (\n    <div class={props.class} style={props.style}>\n      {slots.default?.()}\n    </div>\n  )\n}\n\nFooter.displayName = 'ElTableV2Footer'\n\nexport default Footer\n"], "mappings": ";;EAOAA;AAAmE;EAAY,IAAAC,EAAA;EAC7E,OAAAC,WAAA;IAAA,OACc,EAAAC,KAAK,CAACC,KADpB;IAAA,SACkCD,KAAK,CAACE;GACnC,IAAAJ,EAAA,GAAMD,KAAA,CAAAM,OAFX,qBAAAL,EAAA,CAAAM,IAAA,CAAAP,KAAA;AAKD,CAND;;AAQA,IAAAQ,QAAA,GAAAC,MAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}