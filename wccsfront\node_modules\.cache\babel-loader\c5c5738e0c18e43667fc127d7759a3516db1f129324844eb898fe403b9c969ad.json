{"ast": null, "code": "import { defineComponent, inject, withDirectives, cloneVNode, Fragment, createVNode, Text, Comment } from 'vue';\nimport { FORWARD_REF_INJECTION_KEY, useForwardRefDirective } from '../../../hooks/use-forward-ref/index.mjs';\nimport { NOOP, isObject } from '@vue/shared';\nimport { debugWarn } from '../../../utils/error.mjs';\nimport { useNamespace } from '../../../hooks/use-namespace/index.mjs';\nconst NAME = \"ElOnlyChild\";\nconst OnlyChild = defineComponent({\n  name: NAME,\n  setup(_, {\n    slots,\n    attrs\n  }) {\n    var _a;\n    const forwardRefInjection = inject(FORWARD_REF_INJECTION_KEY);\n    const forwardRefDirective = useForwardRefDirective((_a = forwardRefInjection == null ? void 0 : forwardRefInjection.setForwardRef) != null ? _a : NOOP);\n    return () => {\n      var _a2;\n      const defaultSlot = (_a2 = slots.default) == null ? void 0 : _a2.call(slots, attrs);\n      if (!defaultSlot) return null;\n      if (defaultSlot.length > 1) {\n        debugWarn(NAME, \"requires exact only one valid child.\");\n        return null;\n      }\n      const firstLegitNode = findFirstLegitChild(defaultSlot);\n      if (!firstLegitNode) {\n        debugWarn(NAME, \"no valid child node found\");\n        return null;\n      }\n      return withDirectives(cloneVNode(firstLegitNode, attrs), [[forwardRefDirective]]);\n    };\n  }\n});\nfunction findFirstLegitChild(node) {\n  if (!node) return null;\n  const children = node;\n  for (const child of children) {\n    if (isObject(child)) {\n      switch (child.type) {\n        case Comment:\n          continue;\n        case Text:\n        case \"svg\":\n          return wrapTextContent(child);\n        case Fragment:\n          return findFirstLegitChild(child.children);\n        default:\n          return child;\n      }\n    }\n    return wrapTextContent(child);\n  }\n  return null;\n}\nfunction wrapTextContent(s) {\n  const ns = useNamespace(\"only-child\");\n  return createVNode(\"span\", {\n    \"class\": ns.e(\"content\")\n  }, [s]);\n}\nexport { OnlyChild };", "map": {"version": 3, "names": ["NAME", "<PERSON><PERSON><PERSON><PERSON>", "defineComponent", "name", "slots", "attrs", "_a", "forwardRefInjection", "inject", "FORWARD_REF_INJECTION_KEY", "forwardRefDirective", "useForwardRefDirective", "setForwardRef", "NOOP", "_a2", "defaultSlot", "default", "call", "length", "debugWarn", "firstLegitNode", "findFirstLegitChild", "withDirectives", "cloneVNode", "node", "child", "children", "isObject", "type", "Text", "wrapTextContent", "Fragment", "ns", "useNamespace", "createVNode", "s"], "sources": ["../../../../../../packages/components/slot/src/only-child.tsx"], "sourcesContent": ["import {\n  Comment,\n  Fragment,\n  Text,\n  cloneVNode,\n  defineComponent,\n  inject,\n  withDirectives,\n} from 'vue'\nimport { NOOP, debugWarn, isObject } from '@element-plus/utils'\nimport {\n  FORWARD_REF_INJECTION_KEY,\n  useForwardRefDirective,\n  useNamespace,\n} from '@element-plus/hooks'\n\nimport type { Ref, VNode } from 'vue'\n\nconst NAME = 'ElOnlyChild'\n\nexport const OnlyChild = defineComponent({\n  name: NAME,\n  setup(_, { slots, attrs }) {\n    const forwardRefInjection = inject(FORWARD_REF_INJECTION_KEY)\n    const forwardRefDirective = useForwardRefDirective(\n      forwardRefInjection?.setForwardRef ?? NOOP\n    )\n    return () => {\n      const defaultSlot = slots.default?.(attrs)\n      if (!defaultSlot) return null\n\n      if (defaultSlot.length > 1) {\n        debugWarn(NAME, 'requires exact only one valid child.')\n        return null\n      }\n\n      const firstLegitNode = findFirstLegitChild(defaultSlot)\n      if (!firstLegitNode) {\n        debugWarn(NAME, 'no valid child node found')\n        return null\n      }\n\n      return withDirectives(cloneVNode(firstLegitNode!, attrs), [\n        [forwardRefDirective],\n      ])\n    }\n  },\n})\n\nfunction findFirstLegitChild(node: VNode[] | undefined): VNode | null {\n  if (!node) return null\n  const children = node as VNode[]\n  for (const child of children) {\n    /**\n     * when user uses h(Fragment, [text]) to render plain string,\n     * this switch case just cannot handle, when the value is primitives\n     * we should just return the wrapped string\n     */\n    if (isObject(child)) {\n      switch (child.type) {\n        case Comment:\n          continue\n        case Text:\n        case 'svg':\n          return wrapTextContent(child)\n        case Fragment:\n          return findFirstLegitChild(child.children as VNode[])\n        default:\n          return child\n      }\n    }\n    return wrapTextContent(child)\n  }\n  return null\n}\n\nfunction wrapTextContent(s: string | VNode) {\n  const ns = useNamespace('only-child')\n  return <span class={ns.e('content')}>{s}</span>\n}\n\nexport type OnlyChildExpose = {\n  forwardRef: Ref<HTMLElement>\n}\n"], "mappings": ";;;;;AAkBA,MAAMA,IAAI,GAAG,aAAb;AAEa,MAAAC,SAAS,GAAGC,eAAe,CAAC;EACvCC,IAAI,EAAEH,IADiC;;IAElCI,KAAA;IAAMC;EAAO;IAAS,IAAAC,EAAA;IACzB,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,yBAAD,CAAlC;IACA,MAAMC,mBAAmB,GAAGC,sBAAsB,CAChD,CAAAL,EAAA,GAAAC,mBAAqB,WAAiB,MADU,GAAlDA,mBAAA,CAAAK,aAAA,YAAAN,EAAA,GAAAO,IAAA;IAGA,OAAO,MAAM;MACX,IAAAC,GAAA;MACA,MAAKC,WAAa,IAAAD,GAAA,GAAAV,KAAA,CAAAY,OAAA,qBAAAF,GAAA,CAAAG,IAAA,CAAAb,KAAA,EAAAC,KAAA;wBAElB,OAAe;MACb,IAAAU,WAAU,CAADG,MAAO;QAChBC,SAAA,CAAAnB,IAAA;QACD;;MAED,MAAMoB,cAAc,GAAGC,mBAAmB,CAACN,WAAD,CAA1C;;QACII,SAAA,CAAAnB,IAAA,6BAAiB;QACnB,WAAU;MACV;MACD,OAAAsB,cAAA,CAAAC,UAAA,CAAAH,cAAA,EAAAf,KAAA,KAAAK,mBAAA;;EAED;;AAIH,SAAAW,oBAAAG,IAAA;aA1BI;;EA6BE,WAAAC,KAAA,IAAAC,QAAoB;IAC3B,IAAKC,QAAM,CAAAF,KAAA;MACL,QAAAA,KAAW,CAAjBG,IAAA;;UACW;QACT,KAAAC,IAAA;QACJ;UACA,OAAAC,eAAA,CAAAL,KAAA;QACA,KAAAM,QAAA;UACA,OAAAV,mBAAA,CAAAI,KAAA,CAAAC,QAAA;QACQ;UACM,OAAAD,KAAR;MACE;IACE;;EACF;EACA,WAAK;;;EAEL,MAAAO,EAAA,GAAKC,YAAL;EACE,OAAAC,WAA0B;;EAC5B,IAAAC,CAAA;AACE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}