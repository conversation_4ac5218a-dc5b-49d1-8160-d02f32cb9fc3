{"ast": null, "code": "import createBaseFor from './_createBaseFor.js';\n\n/**\n * This function is like `baseFor` except that it iterates over properties\n * in the opposite order.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseForRight = createBaseFor(true);\nexport default baseForRight;", "map": {"version": 3, "names": ["createBaseFor", "baseForRight"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/_baseForRight.js"], "sourcesContent": ["import createBaseFor from './_createBaseFor.js';\n\n/**\n * This function is like `baseFor` except that it iterates over properties\n * in the opposite order.\n *\n * @private\n * @param {Object} object The object to iterate over.\n * @param {Function} iteratee The function invoked per iteration.\n * @param {Function} keysFunc The function to get the keys of `object`.\n * @returns {Object} Returns `object`.\n */\nvar baseForRight = createBaseFor(true);\n\nexport default baseForRight;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,qBAAqB;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,YAAY,GAAGD,aAAa,CAAC,IAAI,CAAC;AAEtC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}