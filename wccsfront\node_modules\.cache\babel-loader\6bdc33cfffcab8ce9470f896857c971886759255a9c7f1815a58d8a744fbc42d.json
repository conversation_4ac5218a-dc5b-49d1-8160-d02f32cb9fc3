{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\nconst _hoisted_1 = {\n  class: \"chat-record-content\"\n};\nconst _hoisted_2 = {\n  class: \"message-header\"\n};\nconst _hoisted_3 = {\n  class: \"sender-info\"\n};\nconst _hoisted_4 = {\n  class: \"sender-name\"\n};\nconst _hoisted_5 = {\n  class: \"message-time\"\n};\nconst _hoisted_6 = {\n  key: 0,\n  class: \"text-message\"\n};\nconst _hoisted_7 = {\n  class: \"image-message\"\n};\nconst _hoisted_8 = {\n  class: \"file-message\"\n};\nconst _hoisted_9 = {\n  class: \"file-size\"\n};\nconst _hoisted_10 = {\n  class: \"dialog-footer\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  const _component_el_image = _resolveComponent(\"el-image\");\n  const _component_el_icon = _resolveComponent(\"el-icon\");\n  const _component_el_button = _resolveComponent(\"el-button\");\n  const _component_el_dialog = _resolveComponent(\"el-dialog\");\n  return _openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: $setup.dialogVisible,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = $event => $setup.dialogVisible = $event),\n    title: $props.title,\n    width: \"60%\",\n    \"before-close\": $setup.handleClose,\n    class: \"chat-record-dialog\"\n  }, {\n    footer: _withCtx(() => [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_button, {\n      onClick: $setup.handleClose\n    }, {\n      default: _withCtx(() => _cache[1] || (_cache[1] = [_createTextVNode(\"关闭\")])),\n      _: 1 /* STABLE */\n    })])]),\n    default: _withCtx(() => [_createElementVNode(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($props.chatRecords, (item, index) => {\n      return _openBlock(), _createElementBlock(\"div\", {\n        key: index,\n        class: \"chat-message\"\n      }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"span\", _hoisted_4, _toDisplayString(item.sender), 1 /* TEXT */), _createElementVNode(\"span\", {\n        class: _normalizeClass([\"sender-label\", $setup.getLabelClass(item.sender_label)])\n      }, \" @\" + _toDisplayString(item.sender_label), 3 /* TEXT, CLASS */)]), _createElementVNode(\"span\", _hoisted_5, _toDisplayString(item.timestamp), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n        class: _normalizeClass([\"message-body\", {\n          'is-received': item.type === 'received'\n        }])\n      }, [_createCommentVNode(\" 文本消息 \"), item.msgtype === 'text' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, _toDisplayString(item.content), 1 /* TEXT */)) : item.msgtype === 'image' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 图片消息 \"), _createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_image, {\n        src: item.content,\n        \"preview-src-list\": [item.content],\n        fit: \"cover\"\n      }, null, 8 /* PROPS */, [\"src\", \"preview-src-list\"])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : item.msgtype === 'file' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 2\n      }, [_createCommentVNode(\" 文件消息 \"), _createElementVNode(\"div\", _hoisted_8, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        link: \"\"\n      }, {\n        default: _withCtx(() => [_createVNode(_component_el_icon, null, {\n          default: _withCtx(() => [_createVNode($setup[\"Document\"])]),\n          _: 1 /* STABLE */\n        }), _createTextVNode(\" \" + _toDisplayString(item.filename), 1 /* TEXT */)]),\n        _: 2 /* DYNAMIC */\n      }, 1024 /* DYNAMIC_SLOTS */), _createElementVNode(\"span\", _hoisted_9, _toDisplayString($setup.formatFileSize(item.filesize)), 1 /* TEXT */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)]);\n    }), 128 /* KEYED_FRAGMENT */))])]),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"title\"]);\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_dialog", "modelValue", "$setup", "dialogVisible", "_cache", "$event", "title", "$props", "width", "handleClose", "footer", "_withCtx", "_createElementVNode", "_hoisted_10", "_createVNode", "_component_el_button", "onClick", "default", "_createTextVNode", "_", "_hoisted_1", "_createElementBlock", "_Fragment", "_renderList", "chatRecords", "item", "index", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "sender", "_normalizeClass", "getLabelClass", "sender_label", "_hoisted_5", "timestamp", "type", "_createCommentVNode", "msgtype", "_hoisted_6", "content", "_hoisted_7", "_component_el_image", "src", "fit", "_hoisted_8", "link", "_component_el_icon", "filename", "_hoisted_9", "formatFileSize", "filesize"], "sources": ["D:\\Code\\VUE\\WCCSFront\\wccsfront\\src\\components\\ChatRecordPopUp.vue"], "sourcesContent": ["<template>\r\n  <el-dialog\r\n    v-model=\"dialogVisible\"\r\n    :title=\"title\"\r\n    width=\"60%\"\r\n    :before-close=\"handleClose\"\r\n    class=\"chat-record-dialog\"\r\n  >\r\n    <div class=\"chat-record-content\">\r\n      <div v-for=\"(item, index) in chatRecords\" :key=\"index\" class=\"chat-message\">\r\n        <div class=\"message-header\">\r\n          <div class=\"sender-info\">\r\n            <span class=\"sender-name\">{{ item.sender }}</span>\r\n            <span class=\"sender-label\" :class=\"getLabelClass(item.sender_label)\">\r\n              @{{ item.sender_label }}\r\n            </span>\r\n          </div>\r\n          <span class=\"message-time\">{{ item.timestamp }}</span>\r\n        </div>\r\n        <div class=\"message-body\" :class=\"{ 'is-received': item.type === 'received' }\">\r\n          <!-- 文本消息 -->\r\n          <div v-if=\"item.msgtype === 'text'\" class=\"text-message\">\r\n            {{ item.content }}\r\n          </div>\r\n          <!-- 图片消息 -->\r\n          <div v-else-if=\"item.msgtype === 'image'\" class=\"image-message\">\r\n            <el-image \r\n              :src=\"item.content\" \r\n              :preview-src-list=\"[item.content]\"\r\n              fit=\"cover\"\r\n            />\r\n          </div>\r\n          <!-- 文件消息 -->\r\n          <div v-else-if=\"item.msgtype === 'file'\" class=\"file-message\">\r\n            <el-button type=\"primary\" link>\r\n              <el-icon><Document /></el-icon>\r\n              {{ item.filename }}\r\n            </el-button>\r\n            <span class=\"file-size\">{{ formatFileSize(item.filesize) }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    \r\n    <template #footer>\r\n      <div class=\"dialog-footer\">\r\n        <el-button @click=\"handleClose\">关闭</el-button>\r\n      </div>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref, defineProps, defineEmits, watch, onMounted } from 'vue'\r\nimport { Document } from '@element-plus/icons-vue'\r\n\r\nconst props = defineProps({\r\n  title: {\r\n    type: String,\r\n    default: '聊天记录'//弹窗标题，默认是聊天记录\r\n  },\r\n  chatRecords: {\r\n    type: Array,\r\n    default: () => []\r\n  },\r\n  visible: {\r\n    type: Boolean,\r\n    default: false//弹窗是否可见，默认是关闭的\r\n  }\r\n})\r\n\r\n// 定义emit，用于更新visible属性和关闭弹窗\r\nconst emit = defineEmits(['update:visible', 'close'])\r\n\r\n// 定义dialogVisible，用于控制弹窗的可见性\r\nconst dialogVisible = ref(props.visible)\r\n\r\n// 监听父组件传入的 visible 属性变化\r\nwatch(() => props.visible, (newVal) => {\r\n  dialogVisible.value = newVal\r\n})\r\n\r\n// 监听本地 dialogVisible 变化，同步回父组件\r\nwatch(dialogVisible, (newVal) => {\r\n  emit('update:visible', newVal)\r\n})\r\n\r\nconst handleClose = () => {\r\n  dialogVisible.value = false\r\n  emit('close')\r\n}\r\n\r\nconst getLabelClass = (label) => {\r\n  switch (label?.toLowerCase()) {\r\n    case '员工':\r\n      return 'label-employee'\r\n    case '微信':\r\n      return 'label-wechat'\r\n    default:\r\n      return 'label-default'\r\n  }\r\n}\r\n\r\nconst formatFileSize = (size) => {\r\n  if (!size) return '0 B'\r\n  const units = ['B', 'KB', 'MB', 'GB']\r\n  let index = 0\r\n  let fileSize = size\r\n\r\n  while (fileSize >= 1024 && index < units.length - 1) {\r\n    fileSize /= 1024\r\n    index++\r\n  }\r\n\r\n  return `${fileSize.toFixed(2)} ${units[index]}`\r\n}\r\n\r\nconst currentChatRecord = ref(null)\r\n\r\nonMounted(() => {\r\n    currentChatRecord.value = {\r\n        title: props.title,\r\n        item: props.chatRecords\r\n    }\r\n})\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.chat-record-dialog {\r\n  --el-dialog-padding-primary: 20px;\r\n}\r\n\r\n.chat-record-content {\r\n  max-height: 60vh;\r\n  overflow-y: auto;\r\n  padding: 0 10px;\r\n}\r\n\r\n.chat-message {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.message-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.sender-info {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.sender-name {\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n\r\n.sender-label {\r\n  padding: 2px 6px;\r\n  border-radius: 4px;\r\n  font-size: 12px;\r\n}\r\n\r\n.label-employee {\r\n  background-color: #e6f4ff;\r\n  color: #1677ff;\r\n}\r\n\r\n.label-wechat {\r\n  background-color: #f6ffed;\r\n  color: #52c41a;\r\n}\r\n\r\n.label-default {\r\n  background-color: #f5f5f5;\r\n  color: #666;\r\n}\r\n\r\n.message-time {\r\n  font-size: 12px;\r\n  color: #999;\r\n}\r\n\r\n.message-body {\r\n  margin-left: 20px;\r\n  max-width: 80%;\r\n}\r\n\r\n.message-body.is-received {\r\n  margin-left: 0;\r\n  margin-right: 20px;\r\n}\r\n\r\n.text-message {\r\n  background-color: #f5f5f5;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  line-height: 1.5;\r\n  word-break: break-word;\r\n}\r\n\r\n.image-message {\r\n  display: inline-block;\r\n  max-width: 300px;\r\n}\r\n\r\n.image-message :deep(.el-image) {\r\n  border-radius: 8px;\r\n  overflow: hidden;\r\n}\r\n\r\n.file-message {\r\n  background-color: #f5f5f5;\r\n  padding: 12px 16px;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 12px;\r\n}\r\n\r\n.file-size {\r\n  color: #999;\r\n  font-size: 12px;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n  margin-top: 20px;\r\n}\r\n</style>"], "mappings": ";;EAQSA,KAAK,EAAC;AAAqB;;EAEvBA,KAAK,EAAC;AAAgB;;EACpBA,KAAK,EAAC;AAAa;;EAChBA,KAAK,EAAC;AAAa;;EAKrBA,KAAK,EAAC;AAAc;;EAjBpCC,GAAA;EAqB8CD,KAAK,EAAC;;;EAIAA,KAAK,EAAC;AAAe;;EAQtBA,KAAK,EAAC;AAAc;;EAKrDA,KAAK,EAAC;AAAW;;EAOxBA,KAAK,EAAC;AAAe;;;;;;uBA5C9BE,YAAA,CAgDYC,oBAAA;IAjDdC,UAAA,EAEaC,MAAA,CAAAC,aAAa;IAF1B,uBAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAEaH,MAAA,CAAAC,aAAa,GAAAE,MAAA;IACrBC,KAAK,EAAEC,MAAA,CAAAD,KAAK;IACbE,KAAK,EAAC,KAAK;IACV,cAAY,EAAEN,MAAA,CAAAO,WAAW;IAC1BZ,KAAK,EAAC;;IAsCKa,MAAM,EAAAC,QAAA,CACf,MAEM,CAFNC,mBAAA,CAEM,OAFNC,WAEM,GADJC,YAAA,CAA8CC,oBAAA;MAAlCC,OAAK,EAAEd,MAAA,CAAAO;IAAW;MA9CtCQ,OAAA,EAAAN,QAAA,CA8CwC,MAAEP,MAAA,QAAAA,MAAA,OA9C1Cc,gBAAA,CA8CwC,IAAE,E;MA9C1CC,CAAA;;IAAAF,OAAA,EAAAN,QAAA,CAQI,MAkCM,CAlCNC,mBAAA,CAkCM,OAlCNQ,UAkCM,I,kBAjCJC,mBAAA,CAgCMC,SAAA,QAzCZC,WAAA,CASmChB,MAAA,CAAAiB,WAAW,EAT9C,CASmBC,IAAI,EAAEC,KAAK;2BAAxBL,mBAAA,CAgCM;QAhCqCvB,GAAG,EAAE4B,KAAK;QAAE7B,KAAK,EAAC;UAC3De,mBAAA,CAQM,OARNe,UAQM,GAPJf,mBAAA,CAKM,OALNgB,UAKM,GAJJhB,mBAAA,CAAkD,QAAlDiB,UAAkD,EAAAC,gBAAA,CAArBL,IAAI,CAACM,MAAM,kBACxCnB,mBAAA,CAEO;QAFDf,KAAK,EAbvBmC,eAAA,EAawB,cAAc,EAAS9B,MAAA,CAAA+B,aAAa,CAACR,IAAI,CAACS,YAAY;SAAG,IAClE,GAAAJ,gBAAA,CAAGL,IAAI,CAACS,YAAY,wB,GAGzBtB,mBAAA,CAAsD,QAAtDuB,UAAsD,EAAAL,gBAAA,CAAxBL,IAAI,CAACW,SAAS,iB,GAE9CxB,mBAAA,CAqBM;QArBDf,KAAK,EAnBlBmC,eAAA,EAmBmB,cAAc;UAAA,eAA0BP,IAAI,CAACY,IAAI;QAAA;UAC1DC,mBAAA,UAAa,EACFb,IAAI,CAACc,OAAO,e,cAAvBlB,mBAAA,CAEM,OAFNmB,UAEM,EAAAV,gBAAA,CADDL,IAAI,CAACgB,OAAO,oBAGDhB,IAAI,CAACc,OAAO,gB,cAA5BlB,mBAAA,CAMMC,SAAA;QA/BhBxB,GAAA;MAAA,IAwBUwC,mBAAA,UAAa,EACb1B,mBAAA,CAMM,OANN8B,UAMM,GALJ5B,YAAA,CAIE6B,mBAAA;QAHCC,GAAG,EAAEnB,IAAI,CAACgB,OAAO;QACjB,kBAAgB,GAAGhB,IAAI,CAACgB,OAAO;QAChCI,GAAG,EAAC;iHAIQpB,IAAI,CAACc,OAAO,e,cAA5BlB,mBAAA,CAMMC,SAAA;QAvChBxB,GAAA;MAAA,IAgCUwC,mBAAA,UAAa,EACb1B,mBAAA,CAMM,OANNkC,UAMM,GALJhC,YAAA,CAGYC,oBAAA;QAHDsB,IAAI,EAAC,SAAS;QAACU,IAAI,EAAJ;;QAlCtC9B,OAAA,EAAAN,QAAA,CAmCc,MAA+B,CAA/BG,YAAA,CAA+BkC,kBAAA;UAnC7C/B,OAAA,EAAAN,QAAA,CAmCuB,MAAY,CAAZG,YAAA,CAAYZ,MAAA,c;UAnCnCiB,CAAA;YAAAD,gBAAA,CAmC6C,GAC/B,GAAAY,gBAAA,CAAGL,IAAI,CAACwB,QAAQ,iB;QApC9B9B,CAAA;oCAsCYP,mBAAA,CAAkE,QAAlEsC,UAAkE,EAAApB,gBAAA,CAAvC5B,MAAA,CAAAiD,cAAc,CAAC1B,IAAI,CAAC2B,QAAQ,kB,sDAtCnEd,mBAAA,e;;IAAAnB,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}