{"ast": null, "code": "import chain from './chain.js';\n\n/**\n * Creates a `lodash` wrapper instance with explicit method chain sequences enabled.\n *\n * @name chain\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36 },\n *   { 'user': 'fred',   'age': 40 }\n * ];\n *\n * // A sequence without explicit chaining.\n * _(users).head();\n * // => { 'user': 'barney', 'age': 36 }\n *\n * // A sequence with explicit chaining.\n * _(users)\n *   .chain()\n *   .head()\n *   .pick('user')\n *   .value();\n * // => { 'user': 'barney' }\n */\nfunction wrapperChain() {\n  return chain(this);\n}\nexport default wrapperChain;", "map": {"version": 3, "names": ["chain", "wrapperChain"], "sources": ["D:/Code/VUE/WCCSFront/wccsfront/node_modules/lodash-es/wrapperChain.js"], "sourcesContent": ["import chain from './chain.js';\n\n/**\n * Creates a `lodash` wrapper instance with explicit method chain sequences enabled.\n *\n * @name chain\n * @memberOf _\n * @since 0.1.0\n * @category Seq\n * @returns {Object} Returns the new `lodash` wrapper instance.\n * @example\n *\n * var users = [\n *   { 'user': 'barney', 'age': 36 },\n *   { 'user': 'fred',   'age': 40 }\n * ];\n *\n * // A sequence without explicit chaining.\n * _(users).head();\n * // => { 'user': 'barney', 'age': 36 }\n *\n * // A sequence with explicit chaining.\n * _(users)\n *   .chain()\n *   .head()\n *   .pick('user')\n *   .value();\n * // => { 'user': 'barney' }\n */\nfunction wrapperChain() {\n  return chain(this);\n}\n\nexport default wrapperChain;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,YAAY;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB,OAAOD,KAAK,CAAC,IAAI,CAAC;AACpB;AAEA,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}